# 玩家动画控制器设置指南

## 🎬 动画控制器结构

### 创建动画控制器
1. 在`Assets/Animations/`文件夹中右键
2. 选择`Create > Animator Controller`
3. 命名为`PlayerAnimatorController`

### 动画参数设置
在Animator窗口的Parameters面板中添加以下参数：

```
参数名称        类型      默认值    描述
MoveX          Float     0         水平移动输入 (-1到1)
MoveY          Float     0         垂直移动输入 (-1到1)  
IsMoving       Bool      false     是否正在移动
Direction      Int       0         当前方向 (0=下, 1=左, 2=右, 3=上)
TakeDamage     Trigger   -         受伤触发器
```

## 🎯 动画状态机设计

### 主要状态
```
1. Idle (待机状态)
   - 默认状态
   - 播放待机动画循环
   - 条件: IsMoving = false

2. Moving (移动状态)  
   - 根据Direction参数选择移动动画
   - 条件: IsMoving = true

3. Hurt (受伤状态)
   - 播放受伤动画
   - 触发: TakeDamage trigger
   - 自动返回到Idle或Moving
```

### 状态转换条件
```
Any State -> Hurt:
- Trigger: TakeDamage
- Has Exit Time: false
- Transition Duration: 0

Idle -> Moving:
- Condition: IsMoving = true
- Has Exit Time: false
- Transition Duration: 0.1

Moving -> Idle:
- Condition: IsMoving = false
- Has Exit Time: false  
- Transition Duration: 0.1

Hurt -> Idle:
- Has Exit Time: true
- Exit Time: 1.0
- Transition Duration: 0.1
```

## 🎨 动画剪辑创建

### 如果你有精灵资源
1. 选择精灵序列
2. 拖拽到Hierarchy创建动画
3. 保存动画剪辑到`Assets/Animations/`

### 如果暂时没有精灵资源
创建占位符动画：

#### 1. 待机动画 (PlayerIdle)
```
- 长度: 1秒
- 循环: true
- 关键帧: 可以暂时为空或使用简单的位置变化
```

#### 2. 移动动画 (PlayerWalk)
```
- 长度: 0.5秒  
- 循环: true
- 可以创建4个变体:
  - PlayerWalkDown
  - PlayerWalkLeft  
  - PlayerWalkRight
  - PlayerWalkUp
```

#### 3. 受伤动画 (PlayerHurt)
```
- 长度: 0.3秒
- 循环: false
- 可以使用颜色变化或位置偏移模拟受伤效果
```

## 🔧 Blend Tree设置 (高级选项)

如果想要更平滑的方向切换，可以使用Blend Tree：

### 创建2D Blend Tree
1. 在Moving状态右键选择`Create State > From New Blend Tree`
2. 双击进入Blend Tree
3. 设置Blend Type为`2D Simple Directional`
4. 设置Parameters为`MoveX`和`MoveY`

### 添加动画剪辑
```
位置 (0, -1): PlayerWalkDown   (向下)
位置 (-1, 0): PlayerWalkLeft   (向左)
位置 (1, 0):  PlayerWalkRight  (向右)  
位置 (0, 1):  PlayerWalkUp     (向上)
```

## 📝 代码集成

### PlayerController中的动画更新
```csharp
private void UpdateAnimation()
{
    if (animator == null) return;
    
    // 设置移动参数
    animator.SetFloat("MoveX", MoveInput.x);
    animator.SetFloat("MoveY", MoveInput.y);
    animator.SetBool("IsMoving", IsMoving);
    animator.SetInteger("Direction", (int)CurrentDirection);
}

// 受伤时调用
public void TriggerHurtAnimation()
{
    if (animator != null)
    {
        animator.SetTrigger("TakeDamage");
    }
}
```

### PlayerData中的受伤动画触发
```csharp
public void TakeDamage(float damage)
{
    // ... 伤害处理逻辑 ...
    
    // 触发受伤动画
    if (PlayerController.Instance != null)
    {
        PlayerController.Instance.TriggerHurtAnimation();
    }
}
```

## 🎮 临时测试方案

### 使用简单精灵测试
如果暂时没有完整的角色精灵，可以：

1. **创建简单测试精灵**
   - 使用Unity内置的方形精灵
   - 调整颜色区分不同状态
   - 添加简单的Transform动画

2. **使用颜色变化模拟动画**
   ```csharp
   // 在PlayerController中添加
   private void UpdateTestAnimation()
   {
       if (spriteRenderer != null)
       {
           if (IsMoving)
           {
               // 移动时变绿
               spriteRenderer.color = Color.green;
           }
           else
           {
               // 待机时变白
               spriteRenderer.color = Color.white;
           }
       }
   }
   ```

3. **使用Transform动画**
   - 移动时轻微缩放变化
   - 受伤时红色闪烁
   - 待机时轻微上下浮动

## 🔍 调试和测试

### 动画调试
1. 在Animator窗口中观察状态转换
2. 在Inspector中查看参数值变化
3. 使用Debug.Log输出动画状态

### 测试检查清单
- [ ] 待机动画正常播放
- [ ] 移动时切换到移动动画
- [ ] 停止移动时回到待机
- [ ] 方向改变时动画正确切换
- [ ] 受伤动画能正确触发
- [ ] 受伤后能正确返回其他状态
- [ ] 参数值在Inspector中正确显示

## 📱 手机测试注意事项

### 性能优化
- 限制同时播放的动画数量
- 使用简单的动画剪辑
- 避免复杂的Blend Tree（如果性能不足）

### 触屏测试
- 确保虚拟摇杆输入正确传递给动画系统
- 测试快速方向切换的动画表现
- 验证触摸延迟对动画的影响

## 🚀 后续扩展

### 可以添加的动画状态
- Death (死亡)
- Victory (胜利)
- Pickup (拾取道具)
- UseItem (使用道具)
- Interact (交互)

### 高级功能
- 动画事件 (Animation Events)
- 状态机行为 (State Machine Behaviours)
- 动画遮罩 (Avatar Masks)
- 动画重定向

## 💡 最佳实践

1. **保持简单**: 先实现基础功能，再添加复杂特性
2. **命名规范**: 使用清晰的动画和参数命名
3. **性能优先**: 在手机上测试性能表现
4. **模块化设计**: 动画逻辑与游戏逻辑分离
5. **易于扩展**: 为将来添加新动画预留空间

完成动画控制器设置后，玩家就能在游戏中展现生动的动画表现了！
