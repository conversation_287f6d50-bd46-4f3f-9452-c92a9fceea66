using UnityEngine;
using UnityEngine.SceneManagement;
using System.Collections;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 游戏管理器 - 负责游戏状态管理、场景切换、数据持久化
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("游戏设置")]
        [SerializeField] private GameConfig gameConfig;
        
        [Header("调试选项")]
        [SerializeField] private bool enableDebugMode = false;
        
        // 单例模式
        public static GameManager Instance { get; private set; }
        
        // 游戏状态
        public GameState CurrentState { get; private set; }
        
        // 事件系统
        public System.Action<GameState> OnGameStateChanged;
        public System.Action OnGamePaused;
        public System.Action OnGameResumed;
        
        // 游戏数据
        private GameData gameData;
        private bool isInitialized = false;
        
        #region Unity生命周期
        
        private void Awake()
        {
            // 单例模式实现
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (isInitialized)
            {
                ChangeGameState(GameState.MainMenu);
            }
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                PauseGame();
            }
            else
            {
                ResumeGame();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && CurrentState == GameState.Playing)
            {
                PauseGame();
            }
        }
        
        #endregion
        
        #region 游戏初始化
        
        private void InitializeGame()
        {
            // 设置目标帧率
            Application.targetFrameRate = 60;
            
            // 设置屏幕方向为横屏
            Screen.orientation = ScreenOrientation.LandscapeLeft;
            Screen.autorotateToLandscapeLeft = true;
            Screen.autorotateToLandscapeRight = true;
            Screen.autorotateToPortrait = false;
            Screen.autorotateToPortraitUpsideDown = false;
            
            // 加载游戏配置
            LoadGameConfig();
            
            // 初始化子系统
            InitializeSubSystems();
            
            // 加载游戏数据
            LoadGameData();
            
            isInitialized = true;
            
            Debug.Log("游戏管理器初始化完成");
        }
        
        private void LoadGameConfig()
        {
            if (gameConfig == null)
            {
                gameConfig = Resources.Load<GameConfig>("GameConfig");
                if (gameConfig == null)
                {
                    Debug.LogError("未找到游戏配置文件！");
                }
            }
        }
        
        private void InitializeSubSystems()
        {
            // 初始化音频系统
            AudioManager.Instance?.Initialize();
            
            // 初始化输入系统
            InputManager.Instance?.Initialize();
            
            // 初始化UI系统
            UIManager.Instance?.Initialize();
            
            Debug.Log("子系统初始化完成");
        }
        
        #endregion
        
        #region 游戏状态管理
        
        public void ChangeGameState(GameState newState)
        {
            if (CurrentState == newState) return;
            
            GameState previousState = CurrentState;
            CurrentState = newState;
            
            Debug.Log($"游戏状态改变: {previousState} -> {newState}");
            
            // 处理状态切换逻辑
            HandleStateTransition(previousState, newState);
            
            // 触发状态改变事件
            OnGameStateChanged?.Invoke(newState);
        }
        
        private void HandleStateTransition(GameState from, GameState to)
        {
            switch (to)
            {
                case GameState.MainMenu:
                    Time.timeScale = 1f;
                    break;
                    
                case GameState.Playing:
                    Time.timeScale = 1f;
                    break;
                    
                case GameState.Paused:
                    Time.timeScale = 0f;
                    break;
                    
                case GameState.GameOver:
                    Time.timeScale = 0f;
                    break;
                    
                case GameState.Victory:
                    Time.timeScale = 0f;
                    break;
            }
        }
        
        public void PauseGame()
        {
            if (CurrentState == GameState.Playing)
            {
                ChangeGameState(GameState.Paused);
                OnGamePaused?.Invoke();
            }
        }
        
        public void ResumeGame()
        {
            if (CurrentState == GameState.Paused)
            {
                ChangeGameState(GameState.Playing);
                OnGameResumed?.Invoke();
            }
        }
        
        #endregion
        
        #region 场景管理
        
        public void LoadScene(string sceneName)
        {
            StartCoroutine(LoadSceneAsync(sceneName));
        }
        
        private IEnumerator LoadSceneAsync(string sceneName)
        {
            // 显示加载界面
            UIManager.Instance?.ShowLoadingScreen();
            
            // 异步加载场景
            AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
            
            while (!asyncLoad.isDone)
            {
                // 更新加载进度
                float progress = Mathf.Clamp01(asyncLoad.progress / 0.9f);
                UIManager.Instance?.UpdateLoadingProgress(progress);
                yield return null;
            }
            
            // 隐藏加载界面
            UIManager.Instance?.HideLoadingScreen();
        }
        
        public void RestartCurrentLevel()
        {
            string currentScene = SceneManager.GetActiveScene().name;
            LoadScene(currentScene);
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInput()
        {
            // 处理Android返回键
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                HandleBackButton();
            }
        }
        
        private void HandleBackButton()
        {
            switch (CurrentState)
            {
                case GameState.Playing:
                    PauseGame();
                    break;
                    
                case GameState.Paused:
                    ResumeGame();
                    break;
                    
                case GameState.MainMenu:
                    QuitGame();
                    break;
            }
        }
        
        #endregion
        
        #region 数据管理
        
        private void LoadGameData()
        {
            gameData = SaveSystem.LoadGameData();
            if (gameData == null)
            {
                gameData = new GameData();
                Debug.Log("创建新的游戏数据");
            }
            else
            {
                Debug.Log("加载游戏数据成功");
            }
        }
        
        public void SaveGameData()
        {
            if (gameData != null)
            {
                SaveSystem.SaveGameData(gameData);
                Debug.Log("游戏数据保存成功");
            }
        }
        
        public GameData GetGameData()
        {
            return gameData;
        }
        
        #endregion
        
        #region 公共方法
        
        public void QuitGame()
        {
            SaveGameData();
            
            #if UNITY_EDITOR
                UnityEditor.EditorApplication.isPlaying = false;
            #else
                Application.Quit();
            #endif
        }
        
        public GameConfig GetGameConfig()
        {
            return gameConfig;
        }
        
        public bool IsDebugMode()
        {
            return enableDebugMode;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 游戏状态枚举
    /// </summary>
    public enum GameState
    {
        MainMenu,    // 主菜单
        Playing,     // 游戏中
        Paused,      // 暂停
        GameOver,    // 游戏结束
        Victory,     // 胜利
        Loading      // 加载中
    }
}
