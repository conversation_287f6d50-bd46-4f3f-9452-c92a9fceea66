# 迷宫探险 - 美术资源清单

## 1. 美术风格概述

### 1.1 视觉风格定位
- **整体风格**: 黑暗神秘的地下迷宫探险
- **色彩基调**: 以黑色、深灰色为主，配合彩色道具点缀
- **艺术风格**: 2D像素艺术/简洁几何风格
- **氛围营造**: 紧张、神秘、探索感

### 1.2 色彩方案
- **主色调**: #000000 (黑色) - 背景环境
- **次色调**: #333333 (深灰) - 迷宫墙壁
- **辅助色**: #666666 (中灰) - 地面纹理
- **道具色**: #4CAF50 (绿色) - 电池
- **警示色**: #F44336 (红色) - 陷阱机关
- **角色色**: #00BCD4 (青色) - 玩家角色
- **UI色**: #FFFFFF (白色) - 文字信息

## 2. 角色设计

### 2.1 玩家角色
**文件名**: `player.png`
**尺寸**: 32x32 像素
**格式**: PNG (透明背景)
**帧数**: 4帧 (上下左右行走动画)

**设计描述**:
- 头戴头灯的探险者形象
- 简洁的几何形状组合
- 头灯部分需要发光效果
- 背包和锄头的简化表现

**动画帧**:
- Frame 1: 向上行走
- Frame 2: 向下行走  
- Frame 3: 向左行走
- Frame 4: 向右行走

**细节要求**:
- 头灯光源位置明确
- 角色轮廓清晰可辨
- 色彩对比度高，在黑暗中可见

### 2.2 角色状态
**文件名**: `player_effects.png`
**尺寸**: 64x64 像素
**格式**: PNG (透明背景)
**帧数**: 8帧 (受伤、无敌等状态)

**状态效果**:
- 受伤闪烁效果
- 无敌状态光环
- 速度提升特效
- 护盾保护效果

## 3. 环境设计

### 3.1 迷宫墙壁
**文件名**: `wall.png`
**尺寸**: 64x64 像素
**格式**: PNG
**变化**: 4种不同的墙壁纹理

**设计描述**:
- 石质纹理的墙壁
- 适当的阴影和高光
- 可无缝拼接的纹理
- 不同方向的墙壁样式

**墙壁类型**:
- `wall_horizontal.png` - 水平墙壁
- `wall_vertical.png` - 垂直墙壁
- `wall_corner.png` - 角落墙壁
- `wall_inner.png` - 内部墙壁

### 3.2 地面纹理
**文件名**: `floor.png`
**尺寸**: 32x32 像素
**格式**: PNG
**变化**: 3种不同的地面样式

**设计描述**:
- 简洁的地面纹理
- 适当的噪点和质感
- 可平铺的重复图案
- 不同区域的地面变化

### 3.3 背景环境
**文件名**: `background.png`
**尺寸**: 800x600 像素
**格式**: PNG
**层次**: 2层远景背景

**设计描述**:
- 深度感的背景层次
- 模糊的远距离效果
- 适当的颜色渐变
- 营造地下氛围

## 4. 道具设计

### 4.1 电池道具
**文件名**: `battery.png`
**尺寸**: 24x24 像素
**格式**: PNG (透明背景)
**帧数**: 4帧 (闪烁动画)

**设计描述**:
- 电池的简化图标
- 绿色发光效果
- 闪烁的吸引注意动画
- 清晰的识别度

**动画帧**:
- Frame 1-4: 亮度变化的闪烁效果

### 4.2 医疗包
**文件名**: `medkit.png`
**尺寸**: 24x24 像素
**格式**: PNG (透明背景)
**帧数**: 2帧 (浮动动画)

**设计描述**:
- 红十字医疗包
- 白色背景突出显示
- 缓慢的上下浮动
- 清晰的医疗标识

### 4.3 速度提升
**文件名**: `speed_boost.png`
**尺寸**: 24x24 像素
**格式**: PNG (透明背景)
**帧数**: 4帧 (旋转动画)

**设计描述**:
- 闪电或翅膀图标
- 黄色或蓝色发光
- 快速旋转效果
- 动态的视觉表现

### 4.4 护盾道具
**文件名**: `shield.png`
**尺寸**: 24x24 像素
**格式**: PNG (透明背景)
**帧数**: 3帧 (脉冲动画)

**设计描述**:
- 盾牌图标
- 蓝色保护光晕
- 脉冲扩散效果
- 坚固的视觉感受

## 5. 机关设计

### 5.1 尖刺陷阱
**文件名**: `spike_trap.png`
**尺寸**: 32x32 像素
**格式**: PNG (透明背景)
**帧数**: 4帧 (触发动画)

**设计描述**:
- 尖锐的金属尖刺
- 红色警示边缘
- 触发时的弹出动画
- 危险的视觉表现

**动画帧**:
- Frame 1: 隐藏状态
- Frame 2-3: 弹出过程
- Frame 4: 完全弹出

### 5.2 移动机关
**文件名**: `moving_trap.png`
**尺寸**: 32x32 像素
**格式**: PNG (透明背景)
**帧数**: 2帧 (移动动画)

**设计描述**:
- 来回移动的锯齿
- 橙色或红色警示
- 平滑的移动轨迹
- 预测性的运动模式

### 5.3 感应陷阱
**文件名**: `sensor_trap.png`
**尺寸**: 48x48 像素
**格式**: PNG (透明背景)
**帧数**: 6帧 (感应和触发)

**设计描述**:
- 感应范围显示
- 触发后的爆炸效果
- 红色感应光圈
- 延迟触发机制

### 5.4 定时炸弹
**文件名**: `time_bomb.png`
**尺寸**: 32x32 像素
**格式**: PNG (透明背景)
**帧数**: 8帧 (倒计时和爆炸)

**设计描述**:
- 数字倒计时显示
- 红色闪烁警告
- 爆炸的粒子效果
- 清晰的时间提示

## 6. 特效设计

### 6.1 光效系统
**文件名**: `light_effects.png`
**尺寸**: 256x256 像素
**格式**: PNG (透明背景)
**帧数**: 16帧 (不同强度和颜色)

**效果类型**:
- 头灯光束效果
- 电量不足的闪烁
- 道具发光效果
- 爆炸闪光效果

### 6.2 粒子效果
**文件名**: `particles.png`
**尺寸**: 128x128 像素
**格式**: PNG (透明背景)
**帧数**: 32帧 (各种粒子)

**粒子类型**:
- 爆炸粒子
- 道具拾取粒子
- 移动轨迹粒子
- 受伤效果粒子

### 6.3 UI特效
**文件名**: `ui_effects.png`
**尺寸**: 64x64 像素
**格式**: PNG (透明背景)
**帧数**: 8帧 (UI动画)

**特效类型**:
- 按钮悬停效果
- 数值变化动画
- 警告闪烁效果
- 成就解锁特效

## 7. UI界面设计

### 7.1 游戏界面
**文件名**: `game_ui.png`
**尺寸**: 800x600 像素
**格式**: PNG
**包含元素**:
- 电量条背景和填充
- 生命条背景和填充
- 分数显示区域
- 时间显示区域

### 7.2 主菜单
**文件名**: `main_menu.png`
**尺寸**: 800x600 像素
**格式**: PNG
**包含元素**:
- 游戏标题
- 开始按钮
- 设置按钮
- 退出按钮

### 7.3 暂停菜单
**文件名**: `pause_menu.png`
**尺寸**: 400x300 像素
**格式**: PNG (透明背景)
**包含元素**:
- 继续按钮
- 重新开始按钮
- 返回主菜单按钮

### 7.4 游戏结束
**文件名**: `game_over.png`
**尺寸**: 400x300 像素
**格式**: PNG (透明背景)
**包含元素**:
- 游戏结束文字
- 最终分数
- 重新开始按钮
- 分享按钮

## 8. 图标设计

### 8.1 游戏图标
**文件名**: `game_icon.png`
**尺寸**: 512x512 像素
**格式**: PNG
**用途**: 游戏启动图标

**设计描述**:
- 头灯和迷宫元素
- 简洁的图标设计
- 高分辨率适配
- 不同尺寸版本

### 8.2 成就图标
**文件名**: `achievements.png`
**尺寸**: 64x64 像素
**格式**: PNG
**数量**: 20个成就图标

**成就类型**:
- 首次完成关卡
- 收集达人
- 速度之星
- 无伤通关
- 探险大师

## 9. 资源规格总结

### 9.1 文件格式要求
- **图片格式**: PNG (透明背景)
- **色彩模式**: RGB + Alpha
- **压缩级别**: 无损压缩
- **文件命名**: 英文小写+下划线

### 9.2 尺寸规格
- **角色资源**: 32x32 - 64x64 像素
- **环境资源**: 32x32 - 64x64 像素
- **道具资源**: 24x24 - 32x32 像素
- **UI资源**: 根据界面需求
- **特效资源**: 64x64 - 256x256 像素

### 9.3 动画规格
- **帧率**: 8-12 FPS
- **循环**: 大部分动画循环播放
- **帧数**: 根据复杂度决定
- **格式**: 序列帧或精灵图

### 9.4 优化要求
- **文件大小**: 单个图片<100KB
- **内存占用**: 总图片资源<2MB
- **加载时间**: 所有资源<3秒
- **性能影响**: 不影响60FPS

## 10. 制作优先级

### 10.1 第一优先级 (必须)
- 玩家角色基础动画
- 迷宫墙壁和地面
- 电池道具
- 基础UI界面
- 尖刺陷阱

### 10.2 第二优先级 (重要)
- 其他道具类型
- 更多陷阱类型
- 光效和粒子特效
- 菜单界面
- 成就图标

### 10.3 第三优先级 (优化)
- 角色状态效果
- 背景环境
- 高级特效
- 动画细节
- UI动画效果

---

**文档版本**: 1.0  
**创建日期**: 2025-08-05  
**最后更新**: 2025-08-05  
**作者**: 美术设计团队