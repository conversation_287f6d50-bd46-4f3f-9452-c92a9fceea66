using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using MazeAdventure.Core;
using MazeAdventure.Gameplay;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 游戏HUD界面 - 显示游戏中的重要信息
    /// </summary>
    public class GameplayHUD : MonoBehaviour
    {
        [Header("生命值显示")]
        [SerializeField] private Slider healthSlider;
        [SerializeField] private Text healthText;
        [SerializeField] private Image healthIcon;
        [SerializeField] private GameObject healthWarningPanel;
        
        [Header("电量显示")]
        [SerializeField] private Slider batterySlider;
        [SerializeField] private Text batteryText;
        [SerializeField] private Image batteryIcon;
        [SerializeField] private GameObject batteryWarningPanel;
        [SerializeField] private Image headlampStatusIcon;
        
        [Header("时间显示")]
        [SerializeField] private Text timeText;
        [SerializeField] private Text timeLabel;
        [SerializeField] private GameObject timeWarningPanel;
        
        [Header("道具计数")]
        [SerializeField] private Text batteryCountText;
        [SerializeField] private Image batteryCountIcon;
        [SerializeField] private Text healthPackCountText;
        [SerializeField] private Image healthPackCountIcon;
        
        [Header("游戏状态")]
        [SerializeField] private Text scoreText;
        [SerializeField] private Text levelText;
        [SerializeField] private GameObject objectivePanel;
        [SerializeField] private Text objectiveText;
        
        [Header("控制按钮")]
        [SerializeField] private Button pauseButton;
        [SerializeField] private Button headlampToggleButton;
        [SerializeField] private Button settingsButton;
        
        [Header("迷你地图")]
        [SerializeField] private RawImage minimapImage;
        [SerializeField] private Transform playerMarker;
        [SerializeField] private Transform exitMarker;
        [SerializeField] private bool enableMinimap = true;
        
        [Header("警告效果")]
        [SerializeField] private float warningFlashSpeed = 3f;
        [SerializeField] private float criticalFlashSpeed = 6f;
        [SerializeField] private Color warningColor = Color.yellow;
        [SerializeField] private Color criticalColor = Color.red;
        
        [Header("动画设置")]
        [SerializeField] private float valueChangeAnimationTime = 0.5f;
        [SerializeField] private AnimationCurve valueChangeCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private bool enableSmoothUpdates = true;
        
        // 当前值缓存
        private float currentHealthDisplay;
        private float currentBatteryDisplay;
        private float targetHealthValue;
        private float targetBatteryValue;
        
        // 警告状态
        private bool isHealthWarningActive;
        private bool isBatteryWarningActive;
        private bool isTimeWarningActive;
        
        // 动画协程
        private Coroutine healthAnimationCoroutine;
        private Coroutine batteryAnimationCoroutine;
        private Coroutine healthWarningCoroutine;
        private Coroutine batteryWarningCoroutine;
        private Coroutine timeWarningCoroutine;
        
        // 迷你地图
        private Camera minimapCamera;
        private RenderTexture minimapTexture;
        
        #region Unity生命周期
        
        private void Start()
        {
            InitializeHUD();
            SetupEventListeners();
            
            if (enableMinimap)
            {
                SetupMinimap();
            }
        }
        
        private void Update()
        {
            UpdateHUD();
            UpdateMinimap();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeHUD()
        {
            // 初始化显示值
            currentHealthDisplay = 1f;
            currentBatteryDisplay = 1f;
            targetHealthValue = 1f;
            targetBatteryValue = 1f;
            
            // 设置初始UI状态
            UpdateHealthDisplay(100f, 100f);
            UpdateBatteryDisplay(100f, 100f);
            UpdateTimeDisplay(0f);
            
            // 设置按钮事件
            if (pauseButton != null)
                pauseButton.onClick.AddListener(OnPauseClicked);
            if (headlampToggleButton != null)
                headlampToggleButton.onClick.AddListener(OnHeadlampToggleClicked);
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            
            Debug.Log("游戏HUD初始化完成");
        }
        
        private void SetupEventListeners()
        {
            // 订阅玩家数据事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnHealthChanged += UpdateHealthDisplay;
                PlayerData.Instance.OnBatteryChanged += UpdateBatteryDisplay;
                PlayerData.Instance.OnLowHealth += TriggerHealthWarning;
                PlayerData.Instance.OnLowBattery += TriggerBatteryWarning;
            }
            
            // 订阅游戏状态事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameTimeChanged += UpdateTimeDisplay;
            }
            
            // 订阅道具系统事件
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnBatteryCountChanged += UpdateBatteryCount;
                ItemSystem.Instance.OnHealthPackCountChanged += UpdateHealthPackCount;
            }
            
            // 订阅头灯系统事件
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.OnHeadlampToggled += UpdateHeadlampStatus;
                HeadlampSystem.Instance.OnIntensityChanged += UpdateHeadlampIntensity;
            }
        }
        
        private void SetupMinimap()
        {
            // 创建迷你地图相机
            GameObject minimapCameraObj = new GameObject("MinimapCamera");
            minimapCameraObj.transform.SetParent(transform);
            
            minimapCamera = minimapCameraObj.AddComponent<Camera>();
            minimapCamera.orthographic = true;
            minimapCamera.orthographicSize = 10f;
            minimapCamera.cullingMask = LayerMask.GetMask("Minimap");
            minimapCamera.clearFlags = CameraClearFlags.SolidColor;
            minimapCamera.backgroundColor = Color.black;
            
            // 创建渲染纹理
            minimapTexture = new RenderTexture(256, 256, 0);
            minimapCamera.targetTexture = minimapTexture;
            
            if (minimapImage != null)
            {
                minimapImage.texture = minimapTexture;
            }
        }
        
        #endregion
        
        #region HUD更新
        
        private void UpdateHUD()
        {
            // 平滑更新数值显示
            if (enableSmoothUpdates)
            {
                UpdateSmoothValues();
            }
            
            // 更新警告状态
            UpdateWarningStates();
        }
        
        private void UpdateSmoothValues()
        {
            // 平滑更新生命值显示
            if (Mathf.Abs(currentHealthDisplay - targetHealthValue) > 0.01f)
            {
                currentHealthDisplay = Mathf.Lerp(currentHealthDisplay, targetHealthValue, Time.deltaTime * 3f);
                UpdateHealthSlider(currentHealthDisplay);
            }
            
            // 平滑更新电量显示
            if (Mathf.Abs(currentBatteryDisplay - targetBatteryValue) > 0.01f)
            {
                currentBatteryDisplay = Mathf.Lerp(currentBatteryDisplay, targetBatteryValue, Time.deltaTime * 3f);
                UpdateBatterySlider(currentBatteryDisplay);
            }
        }
        
        private void UpdateWarningStates()
        {
            // 更新警告面板显示
            if (healthWarningPanel != null)
            {
                healthWarningPanel.SetActive(isHealthWarningActive);
            }
            
            if (batteryWarningPanel != null)
            {
                batteryWarningPanel.SetActive(isBatteryWarningActive);
            }
            
            if (timeWarningPanel != null)
            {
                timeWarningPanel.SetActive(isTimeWarningActive);
            }
        }
        
        #endregion
        
        #region 生命值显示
        
        private void UpdateHealthDisplay(float current, float max)
        {
            float percentage = max > 0 ? current / max : 0f;
            targetHealthValue = percentage;
            
            if (!enableSmoothUpdates)
            {
                currentHealthDisplay = percentage;
                UpdateHealthSlider(percentage);
            }
            
            // 更新文本
            if (healthText != null)
            {
                healthText.text = $"{Mathf.RoundToInt(current)}/{Mathf.RoundToInt(max)}";
            }
            
            // 更新图标颜色
            UpdateHealthIcon(percentage);
            
            // 检查警告状态
            CheckHealthWarning(percentage);
        }
        
        private void UpdateHealthSlider(float percentage)
        {
            if (healthSlider != null)
            {
                healthSlider.value = percentage;
                
                // 根据生命值改变滑块颜色
                Image fillImage = healthSlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (percentage > 0.6f)
                        fillImage.color = Color.green;
                    else if (percentage > 0.3f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }
        
        private void UpdateHealthIcon(float percentage)
        {
            if (healthIcon != null)
            {
                if (percentage > 0.6f)
                    healthIcon.color = Color.white;
                else if (percentage > 0.3f)
                    healthIcon.color = Color.yellow;
                else
                    healthIcon.color = Color.red;
            }
        }
        
        private void CheckHealthWarning(float percentage)
        {
            bool shouldWarn = percentage <= 0.2f;
            
            if (shouldWarn && !isHealthWarningActive)
            {
                TriggerHealthWarning();
            }
            else if (!shouldWarn && isHealthWarningActive)
            {
                StopHealthWarning();
            }
        }
        
        private void TriggerHealthWarning()
        {
            isHealthWarningActive = true;
            
            if (healthWarningCoroutine != null)
            {
                StopCoroutine(healthWarningCoroutine);
            }
            
            healthWarningCoroutine = StartCoroutine(FlashWarning(healthWarningPanel, criticalColor, criticalFlashSpeed));
        }
        
        private void StopHealthWarning()
        {
            isHealthWarningActive = false;
            
            if (healthWarningCoroutine != null)
            {
                StopCoroutine(healthWarningCoroutine);
                healthWarningCoroutine = null;
            }
        }
        
        #endregion
        
        #region 电量显示
        
        private void UpdateBatteryDisplay(float current, float max)
        {
            float percentage = max > 0 ? current / max : 0f;
            targetBatteryValue = percentage;
            
            if (!enableSmoothUpdates)
            {
                currentBatteryDisplay = percentage;
                UpdateBatterySlider(percentage);
            }
            
            // 更新文本
            if (batteryText != null)
            {
                batteryText.text = $"{Mathf.RoundToInt(percentage * 100)}%";
            }
            
            // 更新图标
            UpdateBatteryIcon(percentage);
            
            // 检查警告状态
            CheckBatteryWarning(percentage);
        }
        
        private void UpdateBatterySlider(float percentage)
        {
            if (batterySlider != null)
            {
                batterySlider.value = percentage;
                
                // 根据电量改变滑块颜色
                Image fillImage = batterySlider.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (percentage > 0.5f)
                        fillImage.color = Color.cyan;
                    else if (percentage > 0.2f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }
        
        private void UpdateBatteryIcon(float percentage)
        {
            if (batteryIcon != null)
            {
                if (percentage > 0.5f)
                    batteryIcon.color = Color.white;
                else if (percentage > 0.2f)
                    batteryIcon.color = Color.yellow;
                else
                    batteryIcon.color = Color.red;
            }
        }
        
        private void CheckBatteryWarning(float percentage)
        {
            bool shouldWarn = percentage <= 0.3f;
            
            if (shouldWarn && !isBatteryWarningActive)
            {
                TriggerBatteryWarning();
            }
            else if (!shouldWarn && isBatteryWarningActive)
            {
                StopBatteryWarning();
            }
        }
        
        private void TriggerBatteryWarning()
        {
            isBatteryWarningActive = true;
            
            if (batteryWarningCoroutine != null)
            {
                StopCoroutine(batteryWarningCoroutine);
            }
            
            float flashSpeed = targetBatteryValue <= 0.1f ? criticalFlashSpeed : warningFlashSpeed;
            Color flashColor = targetBatteryValue <= 0.1f ? criticalColor : warningColor;
            
            batteryWarningCoroutine = StartCoroutine(FlashWarning(batteryWarningPanel, flashColor, flashSpeed));
        }
        
        private void StopBatteryWarning()
        {
            isBatteryWarningActive = false;
            
            if (batteryWarningCoroutine != null)
            {
                StopCoroutine(batteryWarningCoroutine);
                batteryWarningCoroutine = null;
            }
        }
        
        #endregion
        
        #region 时间显示
        
        private void UpdateTimeDisplay(float gameTime)
        {
            if (timeText != null && GameStateManager.Instance != null)
            {
                float remainingTime = GameStateManager.Instance.RemainingTime;
                int minutes = Mathf.FloorToInt(remainingTime / 60f);
                int seconds = Mathf.FloorToInt(remainingTime % 60f);
                
                timeText.text = $"{minutes:00}:{seconds:00}";
                
                // 根据剩余时间改变颜色
                if (remainingTime < 30f)
                {
                    timeText.color = criticalColor;
                    if (!isTimeWarningActive)
                    {
                        TriggerTimeWarning();
                    }
                }
                else if (remainingTime < 60f)
                {
                    timeText.color = warningColor;
                    StopTimeWarning();
                }
                else
                {
                    timeText.color = Color.white;
                    StopTimeWarning();
                }
            }
        }
        
        private void TriggerTimeWarning()
        {
            isTimeWarningActive = true;
            
            if (timeWarningCoroutine != null)
            {
                StopCoroutine(timeWarningCoroutine);
            }
            
            timeWarningCoroutine = StartCoroutine(FlashWarning(timeWarningPanel, criticalColor, criticalFlashSpeed));
        }
        
        private void StopTimeWarning()
        {
            isTimeWarningActive = false;
            
            if (timeWarningCoroutine != null)
            {
                StopCoroutine(timeWarningCoroutine);
                timeWarningCoroutine = null;
            }
        }
        
        #endregion
        
        #region 道具计数
        
        private void UpdateBatteryCount(int count)
        {
            if (batteryCountText != null)
            {
                batteryCountText.text = count.ToString();
                
                // 添加收集动画效果
                StartCoroutine(AnimateCountChange(batteryCountText.transform));
            }
        }
        
        private void UpdateHealthPackCount(int count)
        {
            if (healthPackCountText != null)
            {
                healthPackCountText.text = count.ToString();
                
                // 添加收集动画效果
                StartCoroutine(AnimateCountChange(healthPackCountText.transform));
            }
        }
        
        private IEnumerator AnimateCountChange(Transform target)
        {
            Vector3 originalScale = target.localScale;
            Vector3 targetScale = originalScale * 1.2f;
            
            // 放大
            float elapsedTime = 0f;
            while (elapsedTime < 0.1f)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / 0.1f;
                target.localScale = Vector3.Lerp(originalScale, targetScale, t);
                yield return null;
            }
            
            // 缩回
            elapsedTime = 0f;
            while (elapsedTime < 0.1f)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / 0.1f;
                target.localScale = Vector3.Lerp(targetScale, originalScale, t);
                yield return null;
            }
            
            target.localScale = originalScale;
        }
        
        #endregion
        
        #region 头灯状态
        
        private void UpdateHeadlampStatus(bool isOn)
        {
            if (headlampStatusIcon != null)
            {
                headlampStatusIcon.color = isOn ? Color.yellow : Color.gray;
            }
            
            if (headlampToggleButton != null)
            {
                Image buttonImage = headlampToggleButton.GetComponent<Image>();
                if (buttonImage != null)
                {
                    buttonImage.color = isOn ? Color.white : Color.gray;
                }
            }
        }
        
        private void UpdateHeadlampIntensity(float intensity)
        {
            if (headlampStatusIcon != null)
            {
                float alpha = Mathf.Lerp(0.3f, 1f, intensity);
                Color color = headlampStatusIcon.color;
                color.a = alpha;
                headlampStatusIcon.color = color;
            }
        }
        
        #endregion
        
        #region 迷你地图
        
        private void UpdateMinimap()
        {
            if (!enableMinimap || minimapCamera == null) return;
            
            // 更新迷你地图相机位置
            if (PlayerController.Instance != null)
            {
                Vector3 playerPos = PlayerController.Instance.GetWorldPosition();
                minimapCamera.transform.position = new Vector3(playerPos.x, playerPos.y, -10f);
            }
            
            // 更新玩家标记
            if (playerMarker != null && PlayerController.Instance != null)
            {
                Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
                // 转换到迷你地图坐标
                Vector2 minimapPos = WorldToMinimapPosition(playerPos);
                playerMarker.localPosition = minimapPos;
            }
            
            // 更新出口标记
            if (exitMarker != null && MazeGenerator.Instance != null)
            {
                Vector2 exitPos = MazeGenerator.Instance.GridToWorld(MazeGenerator.Instance.EndPosition);
                Vector2 minimapPos = WorldToMinimapPosition(exitPos);
                exitMarker.localPosition = minimapPos;
            }
        }
        
        private Vector2 WorldToMinimapPosition(Vector2 worldPos)
        {
            // 简单的世界坐标到迷你地图坐标转换
            // 这里需要根据实际的迷你地图大小调整
            return worldPos * 0.1f; // 缩放因子
        }
        
        #endregion
        
        #region 警告效果
        
        private IEnumerator FlashWarning(GameObject warningPanel, Color flashColor, float flashSpeed)
        {
            if (warningPanel == null) yield break;
            
            Image warningImage = warningPanel.GetComponent<Image>();
            if (warningImage == null) yield break;
            
            Color originalColor = warningImage.color;
            
            while (warningPanel.activeInHierarchy)
            {
                float alpha = (Mathf.Sin(Time.time * flashSpeed) + 1f) * 0.5f;
                warningImage.color = new Color(flashColor.r, flashColor.g, flashColor.b, alpha * 0.5f);
                yield return null;
            }
            
            warningImage.color = originalColor;
        }
        
        #endregion
        
        #region 按钮事件
        
        private void OnPauseClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.PauseGame();
            }
        }
        
        private void OnHeadlampToggleClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.Toggle();
            }
        }
        
        private void OnSettingsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (LandscapeUIManager.Instance != null)
            {
                LandscapeUIManager.Instance.ShowPanel(UIPanel.Settings);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 显示目标提示
        /// </summary>
        public void ShowObjective(string objective)
        {
            if (objectiveText != null)
            {
                objectiveText.text = objective;
            }
            
            if (objectivePanel != null)
            {
                objectivePanel.SetActive(true);
                StartCoroutine(HideObjectiveAfterDelay(3f));
            }
        }
        
        private IEnumerator HideObjectiveAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (objectivePanel != null)
            {
                objectivePanel.SetActive(false);
            }
        }
        
        /// <summary>
        /// 启用/禁用迷你地图
        /// </summary>
        public void SetMinimapEnabled(bool enabled)
        {
            enableMinimap = enabled;
            
            if (minimapImage != null)
            {
                minimapImage.gameObject.SetActive(enabled);
            }
            
            if (minimapCamera != null)
            {
                minimapCamera.enabled = enabled;
            }
        }
        
        /// <summary>
        /// 获取HUD状态信息
        /// </summary>
        public string GetHUDStatus()
        {
            return $"HUD状态:\n" +
                   $"生命值警告: {isHealthWarningActive}\n" +
                   $"电量警告: {isBatteryWarningActive}\n" +
                   $"时间警告: {isTimeWarningActive}\n" +
                   $"迷你地图: {enableMinimap}\n" +
                   $"平滑更新: {enableSmoothUpdates}";
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 停止所有协程
            StopAllCoroutines();
            
            // 清理渲染纹理
            if (minimapTexture != null)
            {
                minimapTexture.Release();
                DestroyImmediate(minimapTexture);
            }
            
            // 取消订阅事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnHealthChanged -= UpdateHealthDisplay;
                PlayerData.Instance.OnBatteryChanged -= UpdateBatteryDisplay;
                PlayerData.Instance.OnLowHealth -= TriggerHealthWarning;
                PlayerData.Instance.OnLowBattery -= TriggerBatteryWarning;
            }
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameTimeChanged -= UpdateTimeDisplay;
            }
            
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnBatteryCountChanged -= UpdateBatteryCount;
                ItemSystem.Instance.OnHealthPackCountChanged -= UpdateHealthPackCount;
            }
            
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.OnHeadlampToggled -= UpdateHeadlampStatus;
                HeadlampSystem.Instance.OnIntensityChanged -= UpdateHeadlampIntensity;
            }
        }
        
        #endregion
    }
}
