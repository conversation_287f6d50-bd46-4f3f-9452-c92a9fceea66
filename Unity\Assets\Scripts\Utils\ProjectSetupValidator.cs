using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using System.IO;

#if UNITY_EDITOR
using UnityEditor;
#endif

namespace MazeAdventure.Utils
{
    /// <summary>
    /// 项目设置验证器 - 检查项目配置是否正确
    /// </summary>
    public class ProjectSetupValidator : MonoBehaviour
    {
        [Header("验证设置")]
        [SerializeField] private bool autoValidateOnStart = true;
        [SerializeField] private bool showDetailedLog = true;
        
        [Header("验证结果")]
        [SerializeField] private List<ValidationResult> validationResults = new List<ValidationResult>();
        
        // 验证状态
        public bool IsProjectSetupValid { get; private set; }
        public int PassedChecks { get; private set; }
        public int TotalChecks { get; private set; }
        
        #region Unity生命周期
        
        private void Start()
        {
            if (autoValidateOnStart)
            {
                ValidateProjectSetup();
            }
        }
        
        #endregion
        
        #region 项目验证
        
        [ContextMenu("验证项目设置")]
        public void ValidateProjectSetup()
        {
            validationResults.Clear();
            PassedChecks = 0;
            TotalChecks = 0;
            
            Debug.Log("=== 开始验证项目设置 ===");
            
            // 验证文件夹结构
            ValidateFolderStructure();
            
            // 验证脚本文件
            ValidateScriptFiles();
            
            // 验证场景设置
            ValidateSceneSetup();
            
            // 验证管理器对象
            ValidateManagerObjects();
            
            // 验证项目设置
            ValidateProjectSettings();
            
            // 验证Android设置
            ValidateAndroidSettings();
            
            // 计算总体结果
            IsProjectSetupValid = PassedChecks == TotalChecks;
            
            // 输出结果
            LogValidationResults();
            
            Debug.Log($"=== 验证完成: {PassedChecks}/{TotalChecks} 项通过 ===");
        }
        
        private void ValidateFolderStructure()
        {
            Debug.Log("验证文件夹结构...");
            
            string[] requiredFolders = {
                "Assets/Art",
                "Assets/Art/Characters",
                "Assets/Art/Environment",
                "Assets/Art/Items",
                "Assets/Art/UI",
                "Assets/Art/Effects",
                "Assets/Audio",
                "Assets/Audio/Music",
                "Assets/Audio/SFX",
                "Assets/Audio/Ambient",
                "Assets/Scripts",
                "Assets/Scripts/Core",
                "Assets/Scripts/Gameplay",
                "Assets/Scripts/UI",
                "Assets/Scripts/Audio",
                "Assets/Scripts/Utils",
                "Assets/Prefabs",
                "Assets/Prefabs/Characters",
                "Assets/Prefabs/Environment",
                "Assets/Prefabs/Items",
                "Assets/Prefabs/UI",
                "Assets/Scenes",
                "Assets/Materials",
                "Assets/Animations",
                "Assets/Resources",
                "Assets/Settings"
            };
            
            foreach (string folder in requiredFolders)
            {
                bool exists = Directory.Exists(folder);
                AddValidationResult($"文件夹: {folder}", exists, exists ? "存在" : "缺失");
            }
        }
        
        private void ValidateScriptFiles()
        {
            Debug.Log("验证脚本文件...");
            
            string[] requiredScripts = {
                "Assets/Scripts/Core/GameManager.cs",
                "Assets/Scripts/Core/GameConfig.cs",
                "Assets/Scripts/Core/GameData.cs",
                "Assets/Scripts/Core/SaveSystem.cs",
                "Assets/Scripts/Core/InputManager.cs",
                "Assets/Scripts/Core/ResolutionManager.cs",
                "Assets/Scripts/UI/VirtualJoystick.cs",
                "Assets/Scripts/UI/TouchInteractionManager.cs",
                "Assets/Scripts/UI/MobileUIManager.cs",
                "Assets/Scripts/UI/SafeAreaAdapter.cs",
                "Assets/Scripts/UI/ResponsiveUIElement.cs"
            };
            
            foreach (string script in requiredScripts)
            {
                bool exists = File.Exists(script);
                AddValidationResult($"脚本: {Path.GetFileName(script)}", exists, exists ? "存在" : "缺失");
            }
        }
        
        private void ValidateSceneSetup()
        {
            Debug.Log("验证场景设置...");
            
            // 检查主相机
            Camera mainCamera = Camera.main;
            bool hasCameraComponent = mainCamera != null;
            AddValidationResult("主相机", hasCameraComponent, hasCameraComponent ? "存在" : "缺失");
            
            if (hasCameraComponent)
            {
                bool isOrthographic = mainCamera.orthographic;
                AddValidationResult("相机投影模式", isOrthographic, isOrthographic ? "正交" : "透视");
            }
            
            // 检查Canvas
            Canvas mainCanvas = FindObjectOfType<Canvas>();
            bool hasCanvas = mainCanvas != null;
            AddValidationResult("主Canvas", hasCanvas, hasCanvas ? "存在" : "缺失");
            
            if (hasCanvas)
            {
                bool isScreenSpaceOverlay = mainCanvas.renderMode == RenderMode.ScreenSpaceOverlay;
                AddValidationResult("Canvas渲染模式", isScreenSpaceOverlay, 
                    isScreenSpaceOverlay ? "Screen Space - Overlay" : mainCanvas.renderMode.ToString());
                
                CanvasScaler scaler = mainCanvas.GetComponent<CanvasScaler>();
                bool hasScaler = scaler != null;
                AddValidationResult("Canvas Scaler", hasScaler, hasScaler ? "存在" : "缺失");
                
                if (hasScaler)
                {
                    bool correctScaleMode = scaler.uiScaleMode == CanvasScaler.ScaleMode.ScaleWithScreenSize;
                    AddValidationResult("UI缩放模式", correctScaleMode, 
                        correctScaleMode ? "Scale With Screen Size" : scaler.uiScaleMode.ToString());
                }
            }
            
            // 检查EventSystem
            UnityEngine.EventSystems.EventSystem eventSystem = FindObjectOfType<UnityEngine.EventSystems.EventSystem>();
            bool hasEventSystem = eventSystem != null;
            AddValidationResult("Event System", hasEventSystem, hasEventSystem ? "存在" : "缺失");
        }
        
        private void ValidateManagerObjects()
        {
            Debug.Log("验证管理器对象...");
            
            // 检查GameManager
            bool hasGameManager = FindObjectOfType<Core.GameManager>() != null;
            AddValidationResult("GameManager", hasGameManager, hasGameManager ? "存在" : "缺失");
            
            // 检查InputManager
            bool hasInputManager = FindObjectOfType<Core.InputManager>() != null;
            AddValidationResult("InputManager", hasInputManager, hasInputManager ? "存在" : "缺失");
            
            // 检查ResolutionManager
            bool hasResolutionManager = FindObjectOfType<Core.ResolutionManager>() != null;
            AddValidationResult("ResolutionManager", hasResolutionManager, hasResolutionManager ? "存在" : "缺失");
        }
        
        private void ValidateProjectSettings()
        {
            Debug.Log("验证项目设置...");
            
            #if UNITY_EDITOR
            // 检查公司名称
            bool hasCompanyName = !string.IsNullOrEmpty(PlayerSettings.companyName);
            AddValidationResult("公司名称", hasCompanyName, 
                hasCompanyName ? PlayerSettings.companyName : "未设置");
            
            // 检查产品名称
            bool hasProductName = !string.IsNullOrEmpty(PlayerSettings.productName);
            AddValidationResult("产品名称", hasProductName, 
                hasProductName ? PlayerSettings.productName : "未设置");
            
            // 检查默认屏幕方向
            bool isLandscape = PlayerSettings.defaultInterfaceOrientation == UIOrientation.LandscapeLeft;
            AddValidationResult("默认屏幕方向", isLandscape, 
                isLandscape ? "横屏" : PlayerSettings.defaultInterfaceOrientation.ToString());
            #endif
        }
        
        private void ValidateAndroidSettings()
        {
            Debug.Log("验证Android设置...");
            
            #if UNITY_EDITOR
            // 检查包名
            string packageName = PlayerSettings.GetApplicationIdentifier(BuildTargetGroup.Android);
            bool hasValidPackageName = !string.IsNullOrEmpty(packageName) && packageName.Contains(".");
            AddValidationResult("Android包名", hasValidPackageName, 
                hasValidPackageName ? packageName : "未设置或无效");
            
            // 检查最低API级别
            AndroidSdkVersions minSdkVersion = PlayerSettings.Android.minSdkVersion;
            bool validMinSdk = minSdkVersion >= AndroidSdkVersions.AndroidApiLevel21;
            AddValidationResult("最低API级别", validMinSdk, 
                validMinSdk ? minSdkVersion.ToString() : $"{minSdkVersion} (建议API 21+)");
            
            // 检查脚本后端
            ScriptingImplementation scriptingBackend = PlayerSettings.GetScriptingBackend(BuildTargetGroup.Android);
            bool isIL2CPP = scriptingBackend == ScriptingImplementation.IL2CPP;
            AddValidationResult("脚本后端", isIL2CPP, 
                isIL2CPP ? "IL2CPP" : scriptingBackend.ToString());
            #endif
        }
        
        #endregion
        
        #region 辅助方法
        
        private void AddValidationResult(string checkName, bool passed, string details)
        {
            ValidationResult result = new ValidationResult
            {
                checkName = checkName,
                passed = passed,
                details = details
            };
            
            validationResults.Add(result);
            TotalChecks++;
            
            if (passed)
            {
                PassedChecks++;
                if (showDetailedLog)
                    Debug.Log($"✓ {checkName}: {details}");
            }
            else
            {
                if (showDetailedLog)
                    Debug.LogWarning($"✗ {checkName}: {details}");
            }
        }
        
        private void LogValidationResults()
        {
            Debug.Log("\n=== 验证结果摘要 ===");
            
            foreach (var result in validationResults)
            {
                if (!result.passed)
                {
                    Debug.LogWarning($"需要修复: {result.checkName} - {result.details}");
                }
            }
            
            if (IsProjectSetupValid)
            {
                Debug.Log("🎉 项目设置验证通过！可以开始开发了。");
            }
            else
            {
                Debug.LogWarning($"⚠️ 项目设置不完整，请修复上述问题。({PassedChecks}/{TotalChecks} 项通过)");
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取验证结果摘要
        /// </summary>
        public string GetValidationSummary()
        {
            string summary = $"项目验证结果: {PassedChecks}/{TotalChecks} 项通过\n\n";
            
            summary += "失败的检查项:\n";
            foreach (var result in validationResults)
            {
                if (!result.passed)
                {
                    summary += $"• {result.checkName}: {result.details}\n";
                }
            }
            
            return summary;
        }
        
        /// <summary>
        /// 创建缺失的文件夹
        /// </summary>
        [ContextMenu("创建缺失的文件夹")]
        public void CreateMissingFolders()
        {
            #if UNITY_EDITOR
            string[] requiredFolders = {
                "Assets/Art/Characters",
                "Assets/Art/Environment", 
                "Assets/Art/Items",
                "Assets/Art/UI",
                "Assets/Art/Effects",
                "Assets/Audio/Music",
                "Assets/Audio/SFX",
                "Assets/Audio/Ambient",
                "Assets/Scripts/Core",
                "Assets/Scripts/Gameplay",
                "Assets/Scripts/UI",
                "Assets/Scripts/Audio",
                "Assets/Scripts/Utils",
                "Assets/Prefabs/Characters",
                "Assets/Prefabs/Environment",
                "Assets/Prefabs/Items",
                "Assets/Prefabs/UI",
                "Assets/Scenes",
                "Assets/Materials",
                "Assets/Animations",
                "Assets/Resources",
                "Assets/Settings"
            };
            
            int createdCount = 0;
            foreach (string folder in requiredFolders)
            {
                if (!Directory.Exists(folder))
                {
                    Directory.CreateDirectory(folder);
                    createdCount++;
                    Debug.Log($"创建文件夹: {folder}");
                }
            }
            
            if (createdCount > 0)
            {
                AssetDatabase.Refresh();
                Debug.Log($"创建了 {createdCount} 个缺失的文件夹");
            }
            else
            {
                Debug.Log("所有必需的文件夹都已存在");
            }
            #endif
        }
        
        #endregion
    }
    
    /// <summary>
    /// 验证结果数据结构
    /// </summary>
    [System.Serializable]
    public class ValidationResult
    {
        public string checkName;
        public bool passed;
        public string details;
    }
}
