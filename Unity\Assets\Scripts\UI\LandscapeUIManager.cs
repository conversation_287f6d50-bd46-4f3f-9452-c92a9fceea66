using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using MazeAdventure.Core;
using MazeAdventure.Gameplay;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 横屏UI管理器 - 专门管理手机横屏模式下的UI界面
    /// </summary>
    public class LandscapeUIManager : MonoBehaviour
    {
        [Header("UI面板引用")]
        [SerializeField] private GameObject mainMenuPanel;
        [SerializeField] private GameObject gameplayHUDPanel;
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private GameObject settingsPanel;
        [SerializeField] private GameObject gameOverPanel;
        [SerializeField] private GameObject victoryPanel;
        [SerializeField] private GameObject loadingPanel;
        
        [Header("HUD元素")]
        [SerializeField] private Slider healthBar;
        [SerializeField] private Slider batteryBar;
        [SerializeField] private Text timeText;
        [SerializeField] private Text batteryCountText;
        [SerializeField] private Text healthPackCountText;
        [SerializeField] private Image lowBatteryWarning;
        [SerializeField] private Image lowHealthWarning;
        
        [Header("虚拟控制")]
        [SerializeField] private VirtualJoystick virtualJoystick;
        [SerializeField] private Button headlampToggleButton;
        [SerializeField] private Button pauseButton;
        [SerializeField] private Button settingsButton;
        
        [Header("菜单按钮")]
        [SerializeField] private Button startGameButton;
        [SerializeField] private Button continueButton;
        [SerializeField] private Button newGameButton;
        [SerializeField] private Button mainMenuButton;
        [SerializeField] private Button quitButton;
        
        [Header("设置控件")]
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Dropdown qualityDropdown;
        [SerializeField] private Toggle fullscreenToggle;
        
        [Header("动画设置")]
        [SerializeField] private float panelTransitionTime = 0.3f;
        [SerializeField] private AnimationCurve transitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private bool enableUIAnimations = true;
        
        [Header("警告效果")]
        [SerializeField] private float warningFlashSpeed = 2f;
        [SerializeField] private Color warningColor = Color.red;
        [SerializeField] private float warningThreshold = 0.2f;
        
        // 单例模式
        public static LandscapeUIManager Instance { get; private set; }
        
        // 当前状态
        public UIPanel CurrentPanel { get; private set; }
        public bool IsTransitioning { get; private set; }
        
        // 警告状态
        private bool isLowBatteryWarning;
        private bool isLowHealthWarning;
        private Coroutine batteryWarningCoroutine;
        private Coroutine healthWarningCoroutine;
        
        // 动画协程
        private Coroutine panelTransitionCoroutine;
        
        // 事件系统
        public System.Action<UIPanel> OnPanelChanged;
        public System.Action<bool> OnUIVisibilityChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeLandscapeUI();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupLandscapeUI();
        }
        
        private void Update()
        {
            UpdateHUD();
            HandleInputs();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeLandscapeUI()
        {
            // 确保所有面板初始状态正确
            HideAllPanels();
            
            // 设置初始面板
            CurrentPanel = UIPanel.MainMenu;
            
            // 初始化警告状态
            isLowBatteryWarning = false;
            isLowHealthWarning = false;
        }
        
        private void SetupLandscapeUI()
        {
            // 设置按钮事件
            SetupButtonEvents();
            
            // 设置滑块事件
            SetupSliderEvents();
            
            // 订阅游戏事件
            SubscribeToGameEvents();
            
            // 显示主菜单
            ShowPanel(UIPanel.MainMenu);
            
            Debug.Log("横屏UI管理器初始化完成");
        }
        
        private void SetupButtonEvents()
        {
            // 主菜单按钮
            if (startGameButton != null)
                startGameButton.onClick.AddListener(OnStartGameClicked);
            if (continueButton != null)
                continueButton.onClick.AddListener(OnContinueClicked);
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            if (quitButton != null)
                quitButton.onClick.AddListener(OnQuitClicked);
            
            // 游戏内按钮
            if (pauseButton != null)
                pauseButton.onClick.AddListener(OnPauseClicked);
            if (headlampToggleButton != null)
                headlampToggleButton.onClick.AddListener(OnHeadlampToggleClicked);
            
            // 通用按钮
            if (newGameButton != null)
                newGameButton.onClick.AddListener(OnNewGameClicked);
            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(OnMainMenuClicked);
        }
        
        private void SetupSliderEvents()
        {
            // 音量滑块
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
        }
        
        private void SubscribeToGameEvents()
        {
            // 订阅游戏状态事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged += OnGameStateChanged;
                GameStateManager.Instance.OnGameTimeChanged += OnGameTimeChanged;
            }
            
            // 订阅玩家数据事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnHealthChanged += OnHealthChanged;
                PlayerData.Instance.OnBatteryChanged += OnBatteryChanged;
                PlayerData.Instance.OnLowBattery += OnLowBatteryWarning;
                PlayerData.Instance.OnLowHealth += OnLowHealthWarning;
            }
            
            // 订阅道具系统事件
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnBatteryCountChanged += OnBatteryCountChanged;
                ItemSystem.Instance.OnHealthPackCountChanged += OnHealthPackCountChanged;
            }
        }
        
        #endregion
        
        #region 面板管理
        
        /// <summary>
        /// 显示指定面板
        /// </summary>
        public void ShowPanel(UIPanel panel, bool animated = true)
        {
            if (IsTransitioning || CurrentPanel == panel) return;
            
            if (panelTransitionCoroutine != null)
            {
                StopCoroutine(panelTransitionCoroutine);
            }
            
            if (animated && enableUIAnimations)
            {
                panelTransitionCoroutine = StartCoroutine(TransitionToPanel(panel));
            }
            else
            {
                SetPanelActive(panel, true);
                SetPanelActive(CurrentPanel, false);
                CurrentPanel = panel;
                OnPanelChanged?.Invoke(panel);
            }
        }
        
        /// <summary>
        /// 隐藏所有面板
        /// </summary>
        public void HideAllPanels()
        {
            SetPanelActive(UIPanel.MainMenu, false);
            SetPanelActive(UIPanel.GameplayHUD, false);
            SetPanelActive(UIPanel.PauseMenu, false);
            SetPanelActive(UIPanel.Settings, false);
            SetPanelActive(UIPanel.GameOver, false);
            SetPanelActive(UIPanel.Victory, false);
            SetPanelActive(UIPanel.Loading, false);
        }
        
        private void SetPanelActive(UIPanel panel, bool active)
        {
            GameObject panelObject = GetPanelObject(panel);
            if (panelObject != null)
            {
                panelObject.SetActive(active);
            }
        }
        
        private GameObject GetPanelObject(UIPanel panel)
        {
            switch (panel)
            {
                case UIPanel.MainMenu: return mainMenuPanel;
                case UIPanel.GameplayHUD: return gameplayHUDPanel;
                case UIPanel.PauseMenu: return pauseMenuPanel;
                case UIPanel.Settings: return settingsPanel;
                case UIPanel.GameOver: return gameOverPanel;
                case UIPanel.Victory: return victoryPanel;
                case UIPanel.Loading: return loadingPanel;
                default: return null;
            }
        }
        
        private IEnumerator TransitionToPanel(UIPanel newPanel)
        {
            IsTransitioning = true;
            
            GameObject currentPanelObj = GetPanelObject(CurrentPanel);
            GameObject newPanelObj = GetPanelObject(newPanel);
            
            // 淡出当前面板
            if (currentPanelObj != null)
            {
                yield return StartCoroutine(FadePanel(currentPanelObj, 1f, 0f, panelTransitionTime * 0.5f));
                currentPanelObj.SetActive(false);
            }
            
            // 淡入新面板
            if (newPanelObj != null)
            {
                newPanelObj.SetActive(true);
                yield return StartCoroutine(FadePanel(newPanelObj, 0f, 1f, panelTransitionTime * 0.5f));
            }
            
            CurrentPanel = newPanel;
            IsTransitioning = false;
            OnPanelChanged?.Invoke(newPanel);
            
            panelTransitionCoroutine = null;
        }
        
        private IEnumerator FadePanel(GameObject panel, float fromAlpha, float toAlpha, float duration)
        {
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = panel.AddComponent<CanvasGroup>();
            }
            
            float elapsedTime = 0f;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / duration;
                float curveValue = transitionCurve.Evaluate(t);
                canvasGroup.alpha = Mathf.Lerp(fromAlpha, toAlpha, curveValue);
                yield return null;
            }
            
            canvasGroup.alpha = toAlpha;
        }
        
        #endregion
        
        #region HUD更新
        
        private void UpdateHUD()
        {
            if (CurrentPanel != UIPanel.GameplayHUD) return;
            
            UpdateHealthBar();
            UpdateBatteryBar();
            UpdateWarnings();
        }
        
        private void UpdateHealthBar()
        {
            if (healthBar != null && PlayerData.Instance != null)
            {
                float healthPercentage = PlayerData.Instance.HealthPercentage;
                healthBar.value = healthPercentage;
                
                // 根据生命值改变颜色
                Image fillImage = healthBar.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (healthPercentage > 0.6f)
                        fillImage.color = Color.green;
                    else if (healthPercentage > 0.3f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }
        
        private void UpdateBatteryBar()
        {
            if (batteryBar != null && PlayerData.Instance != null)
            {
                float batteryPercentage = PlayerData.Instance.BatteryPercentage;
                batteryBar.value = batteryPercentage;
                
                // 根据电量改变颜色
                Image fillImage = batteryBar.fillRect.GetComponent<Image>();
                if (fillImage != null)
                {
                    if (batteryPercentage > 0.5f)
                        fillImage.color = Color.cyan;
                    else if (batteryPercentage > 0.2f)
                        fillImage.color = Color.yellow;
                    else
                        fillImage.color = Color.red;
                }
            }
        }
        
        private void UpdateWarnings()
        {
            // 低电量警告
            if (lowBatteryWarning != null)
            {
                lowBatteryWarning.gameObject.SetActive(isLowBatteryWarning);
            }
            
            // 低生命值警告
            if (lowHealthWarning != null)
            {
                lowHealthWarning.gameObject.SetActive(isLowHealthWarning);
            }
        }
        
        #endregion
        
        #region 按钮事件处理
        
        private void OnStartGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.StartNewGame();
            }
        }
        
        private void OnContinueClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 加载存档并继续游戏
            if (GameManager.Instance != null)
            {
                GameManager.Instance.LoadGame();
            }
        }
        
        private void OnNewGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.StartNewGame();
            }
        }
        
        private void OnPauseClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.PauseGame();
            }
        }
        
        private void OnSettingsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            ShowPanel(UIPanel.Settings);
        }
        
        private void OnMainMenuClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.ExitToMenu();
            }
        }
        
        private void OnQuitClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 显示确认对话框
            ShowQuitConfirmation();
        }
        
        private void OnHeadlampToggleClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.Toggle();
            }
        }
        
        #endregion
        
        #region 设置事件处理
        
        private void OnMasterVolumeChanged(float value)
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(value);
            }
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMusicVolume(value);
            }
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetSFXVolume(value);
            }
        }
        
        #endregion
        
        #region 游戏事件处理
        
        private void OnGameStateChanged(GameState oldState, GameState newState)
        {
            switch (newState)
            {
                case GameState.Menu:
                    ShowPanel(UIPanel.MainMenu);
                    break;
                    
                case GameState.Playing:
                    ShowPanel(UIPanel.GameplayHUD);
                    break;
                    
                case GameState.Paused:
                    ShowPanel(UIPanel.PauseMenu);
                    break;
                    
                case GameState.GameOver:
                    ShowPanel(UIPanel.GameOver);
                    break;
                    
                case GameState.Victory:
                    ShowPanel(UIPanel.Victory);
                    break;
            }
        }
        
        private void OnGameTimeChanged(float gameTime)
        {
            if (timeText != null && GameStateManager.Instance != null)
            {
                float remainingTime = GameStateManager.Instance.RemainingTime;
                int minutes = Mathf.FloorToInt(remainingTime / 60f);
                int seconds = Mathf.FloorToInt(remainingTime % 60f);
                timeText.text = $"{minutes:00}:{seconds:00}";
                
                // 时间不足时变红
                if (remainingTime < 60f)
                {
                    timeText.color = Color.red;
                }
                else if (remainingTime < 120f)
                {
                    timeText.color = Color.yellow;
                }
                else
                {
                    timeText.color = Color.white;
                }
            }
        }
        
        private void OnHealthChanged(float current, float max)
        {
            // HUD更新在UpdateHUD中处理
        }
        
        private void OnBatteryChanged(float current, float max)
        {
            // HUD更新在UpdateHUD中处理
        }
        
        private void OnBatteryCountChanged(int count)
        {
            if (batteryCountText != null)
            {
                batteryCountText.text = count.ToString();
            }
        }
        
        private void OnHealthPackCountChanged(int count)
        {
            if (healthPackCountText != null)
            {
                healthPackCountText.text = count.ToString();
            }
        }
        
        private void OnLowBatteryWarning()
        {
            isLowBatteryWarning = true;
            
            if (batteryWarningCoroutine != null)
            {
                StopCoroutine(batteryWarningCoroutine);
            }
            
            batteryWarningCoroutine = StartCoroutine(FlashWarning(lowBatteryWarning));
        }
        
        private void OnLowHealthWarning()
        {
            isLowHealthWarning = true;
            
            if (healthWarningCoroutine != null)
            {
                StopCoroutine(healthWarningCoroutine);
            }
            
            healthWarningCoroutine = StartCoroutine(FlashWarning(lowHealthWarning));
        }
        
        private IEnumerator FlashWarning(Image warningImage)
        {
            if (warningImage == null) yield break;
            
            Color originalColor = warningImage.color;
            
            while (warningImage.gameObject.activeInHierarchy)
            {
                // 闪烁效果
                float alpha = (Mathf.Sin(Time.time * warningFlashSpeed) + 1f) * 0.5f;
                warningImage.color = new Color(warningColor.r, warningColor.g, warningColor.b, alpha);
                yield return null;
            }
            
            warningImage.color = originalColor;
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInputs()
        {
            // ESC键处理
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                HandleBackButton();
            }
            
            // 调试快捷键
            if (Application.isEditor)
            {
                if (Input.GetKeyDown(KeyCode.F1))
                {
                    ShowPanel(UIPanel.MainMenu);
                }
                else if (Input.GetKeyDown(KeyCode.F2))
                {
                    ShowPanel(UIPanel.GameplayHUD);
                }
                else if (Input.GetKeyDown(KeyCode.F3))
                {
                    ShowPanel(UIPanel.Settings);
                }
            }
        }
        
        private void HandleBackButton()
        {
            switch (CurrentPanel)
            {
                case UIPanel.GameplayHUD:
                    OnPauseClicked();
                    break;
                    
                case UIPanel.Settings:
                case UIPanel.PauseMenu:
                    if (GameStateManager.Instance != null && GameStateManager.Instance.IsGameActive)
                    {
                        ShowPanel(UIPanel.GameplayHUD);
                        GameStateManager.Instance.ResumeGame();
                    }
                    else
                    {
                        ShowPanel(UIPanel.MainMenu);
                    }
                    break;
                    
                case UIPanel.GameOver:
                case UIPanel.Victory:
                    ShowPanel(UIPanel.MainMenu);
                    break;
                    
                case UIPanel.MainMenu:
                    ShowQuitConfirmation();
                    break;
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        private void ShowQuitConfirmation()
        {
            // 这里可以显示退出确认对话框
            // 暂时直接退出
            Application.Quit();
        }
        
        /// <summary>
        /// 显示加载界面
        /// </summary>
        public void ShowLoading(bool show)
        {
            if (show)
            {
                ShowPanel(UIPanel.Loading, false);
            }
            else if (CurrentPanel == UIPanel.Loading)
            {
                ShowPanel(UIPanel.MainMenu);
            }
        }
        
        /// <summary>
        /// 更新继续按钮状态
        /// </summary>
        public void UpdateContinueButton()
        {
            if (continueButton != null && GameManager.Instance != null)
            {
                continueButton.interactable = GameManager.Instance.HasSaveData();
            }
        }
        
        /// <summary>
        /// 获取当前UI状态信息
        /// </summary>
        public string GetUIStatus()
        {
            return $"当前面板: {CurrentPanel}\n" +
                   $"转换中: {IsTransitioning}\n" +
                   $"低电量警告: {isLowBatteryWarning}\n" +
                   $"低生命值警告: {isLowHealthWarning}\n" +
                   $"UI动画: {enableUIAnimations}";
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 停止所有协程
            if (panelTransitionCoroutine != null)
            {
                StopCoroutine(panelTransitionCoroutine);
            }
            
            if (batteryWarningCoroutine != null)
            {
                StopCoroutine(batteryWarningCoroutine);
            }
            
            if (healthWarningCoroutine != null)
            {
                StopCoroutine(healthWarningCoroutine);
            }
            
            // 取消订阅事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged -= OnGameStateChanged;
                GameStateManager.Instance.OnGameTimeChanged -= OnGameTimeChanged;
            }
            
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnHealthChanged -= OnHealthChanged;
                PlayerData.Instance.OnBatteryChanged -= OnBatteryChanged;
                PlayerData.Instance.OnLowBattery -= OnLowBatteryWarning;
                PlayerData.Instance.OnLowHealth -= OnLowHealthWarning;
            }
            
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnBatteryCountChanged -= OnBatteryCountChanged;
                ItemSystem.Instance.OnHealthPackCountChanged -= OnHealthPackCountChanged;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// UI面板枚举
    /// </summary>
    public enum UIPanel
    {
        MainMenu,       // 主菜单
        GameplayHUD,    // 游戏HUD
        PauseMenu,      // 暂停菜单
        Settings,       // 设置界面
        GameOver,       // 游戏结束
        Victory,        // 胜利界面
        Loading         // 加载界面
    }
}
