using UnityEngine;
using System.Collections.Generic;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 道具系统 - 管理游戏中的各种道具
    /// </summary>
    public class ItemSystem : MonoBehaviour
    {
        [Header("道具生成设置")]
        [SerializeField] private int batteryCount = 10;
        [SerializeField] private int healthPackCount = 5;
        [SerializeField] private int specialItemCount = 3;
        [SerializeField] private float minDistanceFromPlayer = 3f;
        [SerializeField] private float minDistanceBetweenItems = 2f;
        
        [Header("道具预制体")]
        [SerializeField] private GameObject batteryPrefab;
        [SerializeField] private GameObject healthPackPrefab;
        [SerializeField] private GameObject speedBoostPrefab;
        [SerializeField] private GameObject shieldPrefab;
        
        [Header("临时道具（无预制体时）")]
        [SerializeField] private bool useTemporaryItems = true;
        [SerializeField] private Material batteryMaterial;
        [SerializeField] private Material healthMaterial;
        
        // 单例模式
        public static ItemSystem Instance { get; private set; }
        
        // 道具管理
        private List<ItemController> activeItems;
        private Transform itemContainer;
        
        // 事件系统
        public System.Action<ItemType, Vector2> OnItemSpawned;
        public System.Action<ItemType, Vector2> OnItemCollected;
        public System.Action<int> OnBatteryCountChanged;
        public System.Action<int> OnHealthPackCountChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeItemSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupItemSystem();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeItemSystem()
        {
            activeItems = new List<ItemController>();
            
            // 创建道具容器
            GameObject containerObj = new GameObject("ItemContainer");
            containerObj.transform.SetParent(transform);
            itemContainer = containerObj.transform;
        }
        
        private void SetupItemSystem()
        {
            // 订阅碰撞系统事件
            if (CollisionSystem.Instance != null)
            {
                CollisionSystem.Instance.OnItemCollected += HandleItemCollected;
            }
            
            // 订阅迷宫生成事件
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated += OnMazeGenerated;
            }
        }
        
        #endregion
        
        #region 道具生成
        
        /// <summary>
        /// 在迷宫中生成所有道具
        /// </summary>
        public void SpawnAllItems()
        {
            if (MazeGenerator.Instance == null || MazeGenerator.Instance.MazeGrid == null)
            {
                Debug.LogWarning("迷宫未生成，无法放置道具");
                return;
            }
            
            ClearAllItems();
            
            // 获取所有可放置位置
            List<Vector2Int> availablePositions = GetAvailablePositions();
            
            if (availablePositions.Count == 0)
            {
                Debug.LogWarning("没有可用的道具放置位置");
                return;
            }
            
            // 生成电池
            SpawnItemsOfType(ItemType.Battery, batteryCount, availablePositions);
            
            // 生成医疗包
            SpawnItemsOfType(ItemType.HealthPack, healthPackCount, availablePositions);
            
            // 生成特殊道具
            SpawnItemsOfType(ItemType.SpeedBoost, specialItemCount / 2, availablePositions);
            SpawnItemsOfType(ItemType.Shield, specialItemCount - specialItemCount / 2, availablePositions);
            
            Debug.Log($"道具生成完成，共生成 {activeItems.Count} 个道具");
        }
        
        private void SpawnItemsOfType(ItemType itemType, int count, List<Vector2Int> availablePositions)
        {
            for (int i = 0; i < count && availablePositions.Count > 0; i++)
            {
                // 随机选择位置
                int randomIndex = Random.Range(0, availablePositions.Count);
                Vector2Int gridPos = availablePositions[randomIndex];
                availablePositions.RemoveAt(randomIndex);
                
                // 转换为世界坐标
                Vector2 worldPos = MazeGenerator.Instance.GridToWorld(gridPos);
                
                // 生成道具
                SpawnItem(itemType, worldPos);
                
                // 移除附近的位置以保持间距
                RemoveNearbyPositions(gridPos, availablePositions);
            }
        }
        
        private GameObject SpawnItem(ItemType itemType, Vector2 position)
        {
            GameObject itemObj = null;
            GameObject prefab = GetPrefabForItemType(itemType);
            
            if (prefab != null)
            {
                itemObj = Instantiate(prefab, position, Quaternion.identity, itemContainer);
            }
            else if (useTemporaryItems)
            {
                itemObj = CreateTemporaryItem(itemType, position);
            }
            
            if (itemObj != null)
            {
                // 添加或获取ItemController组件
                ItemController itemController = itemObj.GetComponent<ItemController>();
                if (itemController == null)
                {
                    itemController = itemObj.AddComponent<ItemController>();
                }
                
                itemController.Initialize(itemType);
                activeItems.Add(itemController);
                
                // 设置碰撞层
                itemObj.layer = LayerMask.NameToLayer("Items");
                
                // 确保有碰撞体
                if (itemObj.GetComponent<Collider2D>() == null)
                {
                    CircleCollider2D collider = itemObj.AddComponent<CircleCollider2D>();
                    collider.isTrigger = true;
                    collider.radius = 0.3f;
                }
                
                OnItemSpawned?.Invoke(itemType, position);
            }
            
            return itemObj;
        }
        
        private GameObject CreateTemporaryItem(ItemType itemType, Vector2 position)
        {
            GameObject itemObj = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            itemObj.transform.position = position;
            itemObj.transform.localScale = Vector3.one * 0.5f;
            itemObj.name = $"TempItem_{itemType}";
            
            // 移除默认碰撞体，我们会添加触发器
            Collider collider = itemObj.GetComponent<Collider>();
            if (collider != null)
            {
                DestroyImmediate(collider);
            }
            
            // 设置材质和颜色
            MeshRenderer renderer = itemObj.GetComponent<MeshRenderer>();
            if (renderer != null)
            {
                renderer.material = GetMaterialForItemType(itemType);
            }
            
            return itemObj;
        }
        
        #endregion
        
        #region 道具收集
        
        private void HandleItemCollected(GameObject itemObj)
        {
            ItemController itemController = itemObj.GetComponent<ItemController>();
            if (itemController != null)
            {
                CollectItem(itemController);
            }
        }
        
        private void CollectItem(ItemController item)
        {
            if (item == null || !activeItems.Contains(item)) return;
            
            ItemType itemType = item.ItemType;
            Vector2 position = item.transform.position;
            
            // 应用道具效果
            ApplyItemEffect(itemType);
            
            // 从活跃列表中移除
            activeItems.Remove(item);
            
            // 销毁道具对象
            if (item.gameObject != null)
            {
                // 播放收集特效（如果有）
                PlayCollectionEffect(position, itemType);
                
                Destroy(item.gameObject);
            }
            
            // 触发事件
            OnItemCollected?.Invoke(itemType, position);
            
            // 更新统计
            UpdateItemStats(itemType);
            
            Debug.Log($"收集道具: {itemType} 在位置 {position}");
        }
        
        private void ApplyItemEffect(ItemType itemType)
        {
            PlayerData playerData = PlayerData.Instance;
            if (playerData == null) return;
            
            switch (itemType)
            {
                case ItemType.Battery:
                    playerData.RestoreBattery(25f); // 恢复25%电量
                    break;
                    
                case ItemType.HealthPack:
                    playerData.RestoreHealth(30f); // 恢复30点生命值
                    break;
                    
                case ItemType.SpeedBoost:
                    // 临时速度提升效果（需要在PlayerController中实现）
                    ApplySpeedBoost(5f); // 5秒速度提升
                    break;
                    
                case ItemType.Shield:
                    // 临时护盾效果（需要在PlayerData中实现）
                    ApplyShield(10f); // 10秒护盾
                    break;
            }
        }
        
        private void ApplySpeedBoost(float duration)
        {
            // 这里可以实现速度提升效果
            // 需要与PlayerController配合
            Debug.Log($"应用速度提升效果，持续 {duration} 秒");
        }
        
        private void ApplyShield(float duration)
        {
            // 这里可以实现护盾效果
            // 需要与PlayerData配合
            Debug.Log($"应用护盾效果，持续 {duration} 秒");
        }
        
        #endregion
        
        #region 辅助方法
        
        private List<Vector2Int> GetAvailablePositions()
        {
            List<Vector2Int> positions = new List<Vector2Int>();
            
            if (MazeGenerator.Instance == null) return positions;
            
            MazeCell[,] mazeGrid = MazeGenerator.Instance.MazeGrid;
            int width = mazeGrid.GetLength(0);
            int height = mazeGrid.GetLength(1);
            
            Vector2Int playerStart = MazeGenerator.Instance.StartPosition;
            Vector2Int mazeEnd = MazeGenerator.Instance.EndPosition;
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    Vector2Int pos = new Vector2Int(x, y);
                    
                    // 检查是否为可通行区域
                    if (MazeGenerator.Instance.IsWalkable(pos))
                    {
                        // 检查与玩家起始位置的距离
                        if (Vector2Int.Distance(pos, playerStart) >= minDistanceFromPlayer)
                        {
                            // 不要在终点放置道具
                            if (pos != mazeEnd)
                            {
                                positions.Add(pos);
                            }
                        }
                    }
                }
            }
            
            return positions;
        }
        
        private void RemoveNearbyPositions(Vector2Int centerPos, List<Vector2Int> positions)
        {
            float minDistSqr = minDistanceBetweenItems * minDistanceBetweenItems;
            
            for (int i = positions.Count - 1; i >= 0; i--)
            {
                if (Vector2Int.Distance(positions[i], centerPos) < minDistanceBetweenItems)
                {
                    positions.RemoveAt(i);
                }
            }
        }
        
        private GameObject GetPrefabForItemType(ItemType itemType)
        {
            switch (itemType)
            {
                case ItemType.Battery: return batteryPrefab;
                case ItemType.HealthPack: return healthPackPrefab;
                case ItemType.SpeedBoost: return speedBoostPrefab;
                case ItemType.Shield: return shieldPrefab;
                default: return null;
            }
        }
        
        private Material GetMaterialForItemType(ItemType itemType)
        {
            switch (itemType)
            {
                case ItemType.Battery:
                    return batteryMaterial ?? CreateColorMaterial(Color.green);
                case ItemType.HealthPack:
                    return healthMaterial ?? CreateColorMaterial(Color.red);
                case ItemType.SpeedBoost:
                    return CreateColorMaterial(Color.blue);
                case ItemType.Shield:
                    return CreateColorMaterial(Color.yellow);
                default:
                    return CreateColorMaterial(Color.white);
            }
        }
        
        private Material CreateColorMaterial(Color color)
        {
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = color;
            return mat;
        }
        
        private void PlayCollectionEffect(Vector2 position, ItemType itemType)
        {
            // 这里可以播放粒子效果或动画
            // 暂时使用简单的调试输出
            Debug.Log($"播放收集特效: {itemType} at {position}");
        }
        
        private void UpdateItemStats(ItemType itemType)
        {
            switch (itemType)
            {
                case ItemType.Battery:
                    OnBatteryCountChanged?.Invoke(GetRemainingItemCount(ItemType.Battery));
                    break;
                case ItemType.HealthPack:
                    OnHealthPackCountChanged?.Invoke(GetRemainingItemCount(ItemType.HealthPack));
                    break;
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 清除所有道具
        /// </summary>
        public void ClearAllItems()
        {
            foreach (ItemController item in activeItems)
            {
                if (item != null && item.gameObject != null)
                {
                    Destroy(item.gameObject);
                }
            }
            
            activeItems.Clear();
        }
        
        /// <summary>
        /// 获取剩余道具数量
        /// </summary>
        public int GetRemainingItemCount(ItemType itemType)
        {
            int count = 0;
            foreach (ItemController item in activeItems)
            {
                if (item != null && item.ItemType == itemType)
                {
                    count++;
                }
            }
            return count;
        }
        
        /// <summary>
        /// 获取所有活跃道具
        /// </summary>
        public List<ItemController> GetActiveItems()
        {
            // 清理空引用
            activeItems.RemoveAll(item => item == null);
            return new List<ItemController>(activeItems);
        }
        
        /// <summary>
        /// 在指定位置生成道具
        /// </summary>
        public GameObject SpawnItemAt(ItemType itemType, Vector2 position)
        {
            return SpawnItem(itemType, position);
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnMazeGenerated(MazeCell[,] mazeGrid)
        {
            // 迷宫生成完成后自动生成道具
            Invoke(nameof(SpawnAllItems), 0.1f); // 稍微延迟以确保迷宫完全设置好
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (CollisionSystem.Instance != null)
            {
                CollisionSystem.Instance.OnItemCollected -= HandleItemCollected;
            }
            
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated -= OnMazeGenerated;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 道具类型枚举
    /// </summary>
    public enum ItemType
    {
        Battery,     // 电池
        HealthPack,  // 医疗包
        SpeedBoost,  // 速度提升
        Shield       // 护盾
    }
    
    /// <summary>
    /// 道具控制器
    /// </summary>
    public class ItemController : MonoBehaviour
    {
        public ItemType ItemType { get; private set; }
        
        [Header("道具设置")]
        [SerializeField] private float bobSpeed = 2f;
        [SerializeField] private float bobHeight = 0.1f;
        [SerializeField] private float rotationSpeed = 90f;
        
        private Vector3 startPosition;
        private float bobTimer;
        
        public void Initialize(ItemType itemType)
        {
            ItemType = itemType;
            startPosition = transform.position;
            bobTimer = Random.Range(0f, Mathf.PI * 2f); // 随机起始相位
        }
        
        private void Update()
        {
            // 上下浮动动画
            bobTimer += Time.deltaTime * bobSpeed;
            float bobOffset = Mathf.Sin(bobTimer) * bobHeight;
            transform.position = startPosition + Vector3.up * bobOffset;
            
            // 旋转动画
            transform.Rotate(Vector3.forward, rotationSpeed * Time.deltaTime);
        }
    }
}
