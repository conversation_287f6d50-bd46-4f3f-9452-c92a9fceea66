using UnityEngine;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 2D光照渲染器 - 管理游戏中的光照效果和阴影
    /// </summary>
    public class LightingRenderer : MonoBehaviour
    {
        [Header("环境光照")]
        [SerializeField] private Color ambientColor = new Color(0.1f, 0.1f, 0.2f, 1f);
        [SerializeField] private float ambientIntensity = 0.3f;
        [SerializeField] private bool enableDynamicAmbient = true;
        
        [Header("阴影设置")]
        [SerializeField] private bool enableShadows = true;
        [SerializeField] private LayerMask shadowCasterLayers = -1;
        [SerializeField] private float shadowLength = 10f;
        [SerializeField] private Color shadowColor = new Color(0f, 0f, 0f, 0.8f);
        
        [Header("视野遮挡")]
        [SerializeField] private bool enableFogOfWar = true;
        [SerializeField] private Color fogColor = Color.black;
        [SerializeField] private float fogDensity = 0.9f;
        [SerializeField] private float revealRadius = 1f;
        
        [Header("光照优化")]
        [SerializeField] private bool enableLightCulling = true;
        [SerializeField] private float cullingDistance = 15f;
        [SerializeField] private int maxActiveLights = 8;
        [SerializeField] private bool useLightBatching = true;
        
        [Header("调试选项")]
        [SerializeField] private bool showLightBounds = false;
        [SerializeField] private bool showShadowRays = false;
        
        // 单例模式
        public static LightingRenderer Instance { get; private set; }
        
        // 光照管理
        private List<Light2D> activeLights;
        private List<ShadowCaster2D> shadowCasters;
        private Camera mainCamera;
        private Light2D globalLight;
        
        // 雾战系统
        private Texture2D fogTexture;
        private bool[,] exploredArea;
        private int fogTextureWidth;
        private int fogTextureHeight;
        
        // 性能优化
        private float lastLightUpdate;
        private float lightUpdateInterval = 0.1f;
        
        // 事件系统
        public System.Action<Vector2> OnAreaRevealed;
        public System.Action<float> OnAmbientChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeLightingRenderer();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupLightingRenderer();
        }
        
        private void Update()
        {
            UpdateLighting();
            UpdateFogOfWar();
            
            if (enableLightCulling && Time.time - lastLightUpdate > lightUpdateInterval)
            {
                CullDistantLights();
                lastLightUpdate = Time.time;
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeLightingRenderer()
        {
            activeLights = new List<Light2D>();
            shadowCasters = new List<ShadowCaster2D>();
            
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
            
            // 创建全局光照
            CreateGlobalLight();
            
            // 初始化雾战系统
            if (enableFogOfWar)
            {
                InitializeFogOfWar();
            }
        }
        
        private void SetupLightingRenderer()
        {
            // 设置URP渲染管线的2D光照
            SetupURPLighting();
            
            // 查找现有的光源和阴影投射器
            RefreshLightSources();
            RefreshShadowCasters();
            
            // 订阅迷宫生成事件
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated += OnMazeGenerated;
            }
            
            Debug.Log("2D光照渲染器初始化完成");
        }
        
        private void CreateGlobalLight()
        {
            GameObject globalLightObj = new GameObject("GlobalLight");
            globalLightObj.transform.SetParent(transform);
            
            globalLight = globalLightObj.AddComponent<Light2D>();
            globalLight.lightType = Light2D.LightType.Global;
            globalLight.intensity = ambientIntensity;
            globalLight.color = ambientColor;
            globalLight.lightOrder = -1; // 最低优先级
        }
        
        private void SetupURPLighting()
        {
            // 确保使用2D渲染器
            var renderPipelineAsset = GraphicsSettings.renderPipelineAsset as UniversalRenderPipelineAsset;
            if (renderPipelineAsset != null)
            {
                // 这里可以设置URP的2D光照参数
                // 需要通过代码或Inspector设置
            }
        }
        
        private void InitializeFogOfWar()
        {
            if (MazeGenerator.Instance != null)
            {
                fogTextureWidth = MazeGenerator.Instance.mazeWidth * 2; // 更高分辨率
                fogTextureHeight = MazeGenerator.Instance.mazeHeight * 2;
            }
            else
            {
                fogTextureWidth = 50;
                fogTextureHeight = 30;
            }
            
            exploredArea = new bool[fogTextureWidth, fogTextureHeight];
            CreateFogTexture();
        }
        
        private void CreateFogTexture()
        {
            fogTexture = new Texture2D(fogTextureWidth, fogTextureHeight, TextureFormat.RGBA32, false);
            fogTexture.filterMode = FilterMode.Bilinear;
            
            // 初始化为全黑（未探索）
            Color[] pixels = new Color[fogTextureWidth * fogTextureHeight];
            for (int i = 0; i < pixels.Length; i++)
            {
                pixels[i] = fogColor;
            }
            
            fogTexture.SetPixels(pixels);
            fogTexture.Apply();
        }
        
        #endregion
        
        #region 光照管理
        
        private void UpdateLighting()
        {
            // 更新环境光照
            if (enableDynamicAmbient)
            {
                UpdateAmbientLighting();
            }
            
            // 更新活跃光源
            UpdateActiveLights();
        }
        
        private void UpdateAmbientLighting()
        {
            if (globalLight == null) return;
            
            // 根据玩家电量动态调整环境光
            if (PlayerData.Instance != null)
            {
                float batteryPercentage = PlayerData.Instance.BatteryPercentage;
                float targetIntensity = ambientIntensity * (0.5f + batteryPercentage * 0.5f);
                
                globalLight.intensity = Mathf.Lerp(globalLight.intensity, targetIntensity, Time.deltaTime);
                
                // 根据电量调整环境光颜色
                Color targetColor = Color.Lerp(
                    new Color(ambientColor.r * 0.5f, ambientColor.g * 0.3f, ambientColor.b * 0.8f),
                    ambientColor,
                    batteryPercentage
                );
                
                globalLight.color = Color.Lerp(globalLight.color, targetColor, Time.deltaTime);
            }
        }
        
        private void UpdateActiveLights()
        {
            // 清理无效的光源
            activeLights.RemoveAll(light => light == null);
            
            // 根据距离排序光源
            if (PlayerController.Instance != null)
            {
                Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
                activeLights.Sort((a, b) => 
                {
                    float distA = Vector2.Distance(playerPos, a.transform.position);
                    float distB = Vector2.Distance(playerPos, b.transform.position);
                    return distA.CompareTo(distB);
                });
            }
        }
        
        private void CullDistantLights()
        {
            if (PlayerController.Instance == null) return;
            
            Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
            
            foreach (Light2D light in activeLights)
            {
                if (light == null) continue;
                
                float distance = Vector2.Distance(playerPos, light.transform.position);
                bool shouldBeActive = distance <= cullingDistance;
                
                if (light.enabled != shouldBeActive)
                {
                    light.enabled = shouldBeActive;
                }
            }
        }
        
        #endregion
        
        #region 阴影系统
        
        private void RefreshShadowCasters()
        {
            shadowCasters.Clear();
            
            // 查找所有阴影投射器
            ShadowCaster2D[] allCasters = FindObjectsOfType<ShadowCaster2D>();
            shadowCasters.AddRange(allCasters);
            
            Debug.Log($"找到 {shadowCasters.Count} 个阴影投射器");
        }
        
        /// <summary>
        /// 为迷宫墙壁添加阴影投射器
        /// </summary>
        public void AddShadowCastersToMaze()
        {
            if (MazeGenerator.Instance == null || !enableShadows) return;
            
            MazeCell[,] mazeGrid = MazeGenerator.Instance.MazeGrid;
            int width = mazeGrid.GetLength(0);
            int height = mazeGrid.GetLength(1);
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    if (mazeGrid[x, y].cellType == CellType.Wall)
                    {
                        CreateShadowCasterForWall(x, y);
                    }
                }
            }
        }
        
        private void CreateShadowCasterForWall(int x, int y)
        {
            Vector2 worldPos = MazeGenerator.Instance.GridToWorld(new Vector2Int(x, y));
            
            GameObject shadowObj = new GameObject($"ShadowCaster_{x}_{y}");
            shadowObj.transform.position = worldPos;
            shadowObj.transform.SetParent(transform);
            
            ShadowCaster2D shadowCaster = shadowObj.AddComponent<ShadowCaster2D>();
            shadowCaster.selfShadows = true;
            
            // 创建简单的方形阴影形状
            Vector3[] shadowShape = new Vector3[4];
            float halfSize = MazeGenerator.Instance.cellSize * 0.5f;
            
            shadowShape[0] = new Vector3(-halfSize, -halfSize, 0);
            shadowShape[1] = new Vector3(halfSize, -halfSize, 0);
            shadowShape[2] = new Vector3(halfSize, halfSize, 0);
            shadowShape[3] = new Vector3(-halfSize, halfSize, 0);
            
            shadowCaster.shapePath = shadowShape;
            
            shadowCasters.Add(shadowCaster);
        }
        
        #endregion
        
        #region 雾战系统
        
        private void UpdateFogOfWar()
        {
            if (!enableFogOfWar || PlayerController.Instance == null) return;
            
            Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
            RevealAreaAroundPosition(playerPos, revealRadius);
        }
        
        private void RevealAreaAroundPosition(Vector2 worldPos, float radius)
        {
            // 转换世界坐标到雾战纹理坐标
            Vector2Int fogPos = WorldToFogCoordinates(worldPos);
            int radiusInPixels = Mathf.RoundToInt(radius * 2); // 雾战纹理分辨率是迷宫的2倍
            
            bool hasNewReveal = false;
            
            for (int x = -radiusInPixels; x <= radiusInPixels; x++)
            {
                for (int y = -radiusInPixels; y <= radiusInPixels; y++)
                {
                    if (x * x + y * y <= radiusInPixels * radiusInPixels)
                    {
                        int fogX = fogPos.x + x;
                        int fogY = fogPos.y + y;
                        
                        if (IsValidFogCoordinate(fogX, fogY) && !exploredArea[fogX, fogY])
                        {
                            exploredArea[fogX, fogY] = true;
                            hasNewReveal = true;
                            
                            // 更新纹理像素
                            fogTexture.SetPixel(fogX, fogY, Color.clear);
                        }
                    }
                }
            }
            
            if (hasNewReveal)
            {
                fogTexture.Apply();
                OnAreaRevealed?.Invoke(worldPos);
            }
        }
        
        private Vector2Int WorldToFogCoordinates(Vector2 worldPos)
        {
            if (MazeGenerator.Instance == null) return Vector2Int.zero;
            
            Vector2Int gridPos = MazeGenerator.Instance.WorldToGrid(worldPos);
            return new Vector2Int(gridPos.x * 2, gridPos.y * 2);
        }
        
        private bool IsValidFogCoordinate(int x, int y)
        {
            return x >= 0 && x < fogTextureWidth && y >= 0 && y < fogTextureHeight;
        }
        
        #endregion
        
        #region 光源管理
        
        private void RefreshLightSources()
        {
            activeLights.Clear();
            
            // 查找所有2D光源
            Light2D[] allLights = FindObjectsOfType<Light2D>();
            activeLights.AddRange(allLights);
            
            Debug.Log($"找到 {activeLights.Count} 个光源");
        }
        
        /// <summary>
        /// 注册新光源
        /// </summary>
        public void RegisterLight(Light2D light)
        {
            if (light != null && !activeLights.Contains(light))
            {
                activeLights.Add(light);
            }
        }
        
        /// <summary>
        /// 注销光源
        /// </summary>
        public void UnregisterLight(Light2D light)
        {
            if (light != null)
            {
                activeLights.Remove(light);
            }
        }
        
        /// <summary>
        /// 在指定位置创建临时光源
        /// </summary>
        public Light2D CreateTemporaryLight(Vector2 position, float intensity, float range, Color color, float duration)
        {
            GameObject lightObj = new GameObject("TemporaryLight");
            lightObj.transform.position = position;
            
            Light2D light = lightObj.AddComponent<Light2D>();
            light.lightType = Light2D.LightType.Point;
            light.intensity = intensity;
            light.pointLightOuterRadius = range;
            light.color = color;
            
            RegisterLight(light);
            
            // 自动销毁
            Destroy(lightObj, duration);
            
            return light;
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnMazeGenerated(MazeCell[,] mazeGrid)
        {
            // 迷宫生成后添加阴影投射器
            if (enableShadows)
            {
                AddShadowCastersToMaze();
            }
            
            // 重新初始化雾战系统
            if (enableFogOfWar)
            {
                InitializeFogOfWar();
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置环境光照
        /// </summary>
        public void SetAmbientLighting(Color color, float intensity)
        {
            ambientColor = color;
            ambientIntensity = intensity;
            
            if (globalLight != null)
            {
                globalLight.color = color;
                globalLight.intensity = intensity;
            }
            
            OnAmbientChanged?.Invoke(intensity);
        }
        
        /// <summary>
        /// 启用/禁用阴影
        /// </summary>
        public void SetShadowsEnabled(bool enabled)
        {
            enableShadows = enabled;
            
            foreach (ShadowCaster2D caster in shadowCasters)
            {
                if (caster != null)
                {
                    caster.enabled = enabled;
                }
            }
        }
        
        /// <summary>
        /// 启用/禁用雾战
        /// </summary>
        public void SetFogOfWarEnabled(bool enabled)
        {
            enableFogOfWar = enabled;
            
            if (!enabled && fogTexture != null)
            {
                // 清除雾战效果
                Color[] pixels = new Color[fogTextureWidth * fogTextureHeight];
                for (int i = 0; i < pixels.Length; i++)
                {
                    pixels[i] = Color.clear;
                }
                fogTexture.SetPixels(pixels);
                fogTexture.Apply();
            }
        }
        
        /// <summary>
        /// 获取雾战纹理
        /// </summary>
        public Texture2D GetFogOfWarTexture()
        {
            return fogTexture;
        }
        
        /// <summary>
        /// 获取光照统计信息
        /// </summary>
        public string GetLightingStats()
        {
            int activeLightCount = activeLights.Count(light => light != null && light.enabled);
            
            return $"光照统计:\n" +
                   $"活跃光源: {activeLightCount}/{activeLights.Count}\n" +
                   $"阴影投射器: {shadowCasters.Count}\n" +
                   $"环境光强度: {globalLight?.intensity:F2}\n" +
                   $"雾战启用: {enableFogOfWar}\n" +
                   $"阴影启用: {enableShadows}";
        }
        
        #endregion
        
        #region 调试绘制
        
        private void OnDrawGizmosSelected()
        {
            if (showLightBounds)
            {
                foreach (Light2D light in activeLights)
                {
                    if (light == null || !light.enabled) continue;
                    
                    Gizmos.color = light.color;
                    
                    if (light.lightType == Light2D.LightType.Point)
                    {
                        Gizmos.DrawWireSphere(light.transform.position, light.pointLightOuterRadius);
                    }
                    else if (light.lightType == Light2D.LightType.Spot)
                    {
                        // 绘制聚光灯范围
                        Vector3 forward = light.transform.up;
                        Gizmos.DrawRay(light.transform.position, forward * light.pointLightOuterRadius);
                    }
                }
            }
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated -= OnMazeGenerated;
            }
            
            // 清理纹理
            if (fogTexture != null)
            {
                DestroyImmediate(fogTexture);
            }
        }
        
        #endregion
    }
}
