using UnityEngine;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 安全区域适配器 - 自动适配刘海屏和圆角屏幕
    /// </summary>
    [RequireComponent(typeof(RectTransform))]
    public class SafeAreaAdapter : MonoBehaviour
    {
        [Header("适配设置")]
        [SerializeField] private bool adaptLeft = true;
        [SerializeField] private bool adaptRight = true;
        [SerializeField] private bool adaptTop = true;
        [SerializeField] private bool adaptBottom = true;
        
        [Header("边距设置")]
        [SerializeField] private float minLeftMargin = 0f;
        [SerializeField] private float minRightMargin = 0f;
        [SerializeField] private float minTopMargin = 0f;
        [SerializeField] private float minBottomMargin = 0f;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private Color debugColor = Color.red;
        
        // 组件引用
        private RectTransform rectTransform;
        private Canvas parentCanvas;
        
        // 缓存数据
        private Rect lastSafeArea;
        private Vector2 lastScreenSize;
        
        #region Unity生命周期
        
        private void Awake()
        {
            rectTransform = GetComponent<RectTransform>();
            parentCanvas = GetComponentInParent<Canvas>();
        }
        
        private void Start()
        {
            ApplySafeArea();
        }
        
        private void Update()
        {
            // 检查安全区域或屏幕尺寸是否发生变化
            if (HasSafeAreaChanged() || HasScreenSizeChanged())
            {
                ApplySafeArea();
            }
        }
        
        #endregion
        
        #region 安全区域适配
        
        private void ApplySafeArea()
        {
            if (rectTransform == null) return;
            
            Rect safeArea = Screen.safeArea;
            Vector2 screenSize = new Vector2(Screen.width, Screen.height);
            
            // 计算安全区域的相对位置
            Vector2 anchorMin = safeArea.position;
            Vector2 anchorMax = safeArea.position + safeArea.size;
            
            // 转换为相对坐标 (0-1)
            anchorMin.x /= screenSize.x;
            anchorMin.y /= screenSize.y;
            anchorMax.x /= screenSize.x;
            anchorMax.y /= screenSize.y;
            
            // 应用适配设置
            Vector2 currentAnchorMin = rectTransform.anchorMin;
            Vector2 currentAnchorMax = rectTransform.anchorMax;
            
            if (adaptLeft)
            {
                currentAnchorMin.x = Mathf.Max(anchorMin.x, minLeftMargin / screenSize.x);
            }
            
            if (adaptRight)
            {
                currentAnchorMax.x = Mathf.Min(anchorMax.x, 1f - minRightMargin / screenSize.x);
            }
            
            if (adaptBottom)
            {
                currentAnchorMin.y = Mathf.Max(anchorMin.y, minBottomMargin / screenSize.y);
            }
            
            if (adaptTop)
            {
                currentAnchorMax.y = Mathf.Min(anchorMax.y, 1f - minTopMargin / screenSize.y);
            }
            
            // 应用新的锚点
            rectTransform.anchorMin = currentAnchorMin;
            rectTransform.anchorMax = currentAnchorMax;
            
            // 重置偏移
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            // 更新缓存
            lastSafeArea = safeArea;
            lastScreenSize = screenSize;
            
            if (showDebugInfo)
            {
                Debug.Log($"SafeAreaAdapter: 安全区域 {safeArea} -> 锚点 {currentAnchorMin} - {currentAnchorMax}");
            }
        }
        
        private bool HasSafeAreaChanged()
        {
            return Screen.safeArea != lastSafeArea;
        }
        
        private bool HasScreenSizeChanged()
        {
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            return currentScreenSize != lastScreenSize;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置适配方向
        /// </summary>
        public void SetAdaptDirections(bool left, bool right, bool top, bool bottom)
        {
            adaptLeft = left;
            adaptRight = right;
            adaptTop = top;
            adaptBottom = bottom;
            ApplySafeArea();
        }
        
        /// <summary>
        /// 设置最小边距
        /// </summary>
        public void SetMinMargins(float left, float right, float top, float bottom)
        {
            minLeftMargin = left;
            minRightMargin = right;
            minTopMargin = top;
            minBottomMargin = bottom;
            ApplySafeArea();
        }
        
        /// <summary>
        /// 强制刷新适配
        /// </summary>
        public void RefreshAdapter()
        {
            ApplySafeArea();
        }
        
        /// <summary>
        /// 获取当前安全区域信息
        /// </summary>
        public string GetSafeAreaInfo()
        {
            Rect safeArea = Screen.safeArea;
            Vector2 screenSize = new Vector2(Screen.width, Screen.height);
            
            return $"屏幕尺寸: {screenSize}\n" +
                   $"安全区域: {safeArea}\n" +
                   $"安全区域比例: {safeArea.width / screenSize.x:F3} x {safeArea.height / screenSize.y:F3}\n" +
                   $"当前锚点: {rectTransform.anchorMin} - {rectTransform.anchorMax}";
        }
        
        #endregion
        
        #region 调试绘制
        
        private void OnDrawGizmosSelected()
        {
            if (!showDebugInfo || rectTransform == null) return;
            
            // 绘制安全区域边界
            Gizmos.color = debugColor;
            
            Vector3[] corners = new Vector3[4];
            rectTransform.GetWorldCorners(corners);
            
            // 绘制矩形边框
            Gizmos.DrawLine(corners[0], corners[1]);
            Gizmos.DrawLine(corners[1], corners[2]);
            Gizmos.DrawLine(corners[2], corners[3]);
            Gizmos.DrawLine(corners[3], corners[0]);
        }
        
        #endregion
    }
}
