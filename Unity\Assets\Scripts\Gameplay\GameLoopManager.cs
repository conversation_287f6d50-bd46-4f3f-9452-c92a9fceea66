using UnityEngine;
using System.Collections;
using MazeAdventure.Core;
using MazeAdventure.UI;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 游戏循环管理器 - 协调所有核心系统的运行
    /// </summary>
    public class GameLoopManager : MonoBehaviour
    {
        [Header("系统初始化顺序")]
        [SerializeField] private bool autoInitialize = true;
        [SerializeField] private float initializationDelay = 0.5f;
        
        [Header("游戏循环设置")]
        [SerializeField] private float fixedUpdateRate = 50f; // 50Hz物理更新
        [SerializeField] private int targetFrameRate = 60;
        [SerializeField] private bool enableVSync = true;
        
        [Header("性能监控")]
        [SerializeField] private bool enablePerformanceMonitoring = true;
        [SerializeField] private float performanceUpdateInterval = 1f;
        
        [Header("调试选项")]
        [SerializeField] private bool showSystemStatus = false;
        [SerializeField] private bool enableSystemProfiling = false;
        
        // 单例模式
        public static GameLoopManager Instance { get; private set; }
        
        // 系统状态
        public bool IsInitialized { get; private set; }
        public bool IsRunning { get; private set; }
        public float CurrentFPS { get; private set; }
        public float AverageFrameTime { get; private set; }
        
        // 系统引用
        private GameManager gameManager;
        private InputManagerSimple inputManager;
        private ResolutionManager resolutionManager;
        private PlayerController playerController;
        private PlayerData playerData;
        private MazeGenerator mazeGenerator;
        private MazeRenderer mazeRenderer;
        private CollisionSystem collisionSystem;
        private ItemSystem itemSystem;
        private GameStateManager gameStateManager;
        private MobileUIManager uiManager;
        
        // 性能监控
        private float frameTimeAccumulator;
        private int frameCount;
        private float lastPerformanceUpdate;
        
        // 事件系统
        public System.Action OnSystemsInitialized;
        public System.Action OnGameLoopStarted;
        public System.Action OnGameLoopStopped;
        public System.Action<string> OnSystemError;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGameLoop();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (autoInitialize)
            {
                StartCoroutine(InitializeSystemsCoroutine());
            }
        }
        
        private void Update()
        {
            if (!IsInitialized || !IsRunning) return;
            
            UpdatePerformanceMetrics();
            UpdateSystems();
        }
        
        private void FixedUpdate()
        {
            if (!IsInitialized || !IsRunning) return;
            
            FixedUpdateSystems();
        }
        
        private void LateUpdate()
        {
            if (!IsInitialized || !IsRunning) return;
            
            LateUpdateSystems();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeGameLoop()
        {
            // 设置应用程序设置
            Application.targetFrameRate = targetFrameRate;
            QualitySettings.vSyncCount = enableVSync ? 1 : 0;
            Time.fixedDeltaTime = 1f / fixedUpdateRate;
            
            // 初始化性能监控
            frameTimeAccumulator = 0f;
            frameCount = 0;
            lastPerformanceUpdate = Time.time;
            
            Debug.Log("游戏循环管理器初始化完成");
        }
        
        /// <summary>
        /// 初始化所有系统
        /// </summary>
        public void InitializeSystems()
        {
            if (IsInitialized) return;
            
            StartCoroutine(InitializeSystemsCoroutine());
        }
        
        private IEnumerator InitializeSystemsCoroutine()
        {
            Debug.Log("开始初始化游戏系统...");
            
            yield return new WaitForSeconds(initializationDelay);
            
            // 按顺序初始化系统
            yield return StartCoroutine(InitializeCoreManagers());
            yield return StartCoroutine(InitializeGameplaySystems());
            yield return StartCoroutine(InitializeUISystems());
            yield return StartCoroutine(FinalizeInitialization());
            
            IsInitialized = true;
            OnSystemsInitialized?.Invoke();
            
            Debug.Log("所有游戏系统初始化完成");
            
            // 自动开始游戏循环
            StartGameLoop();
        }
        
        private IEnumerator InitializeCoreManagers()
        {
            Debug.Log("初始化核心管理器...");
            
            // GameManager
            gameManager = GameManager.Instance;
            if (gameManager == null)
            {
                ReportSystemError("GameManager未找到");
                yield break;
            }
            
            // InputManager
            inputManager = InputManagerSimple.Instance;
            if (inputManager == null)
            {
                ReportSystemError("InputManager未找到");
                yield break;
            }
            
            // ResolutionManager
            resolutionManager = ResolutionManager.Instance;
            if (resolutionManager == null)
            {
                ReportSystemError("ResolutionManager未找到");
                yield break;
            }
            
            yield return null;
        }
        
        private IEnumerator InitializeGameplaySystems()
        {
            Debug.Log("初始化游戏逻辑系统...");
            
            // PlayerController和PlayerData
            playerController = PlayerController.Instance;
            playerData = PlayerData.Instance;
            
            // MazeGenerator和MazeRenderer
            mazeGenerator = MazeGenerator.Instance;
            mazeRenderer = MazeRenderer.Instance;
            
            // CollisionSystem
            collisionSystem = CollisionSystem.Instance;
            
            // ItemSystem
            itemSystem = ItemSystem.Instance;
            
            // GameStateManager
            gameStateManager = GameStateManager.Instance;
            
            yield return null;
        }
        
        private IEnumerator InitializeUISystems()
        {
            Debug.Log("初始化UI系统...");
            
            // MobileUIManager
            uiManager = MobileUIManager.Instance;
            
            yield return null;
        }
        
        private IEnumerator FinalizeInitialization()
        {
            Debug.Log("完成系统初始化...");
            
            // 设置系统间的连接
            SetupSystemConnections();
            
            // 生成初始迷宫
            if (mazeGenerator != null)
            {
                mazeGenerator.GenerateMaze();
                
                // 等待迷宫生成完成
                while (mazeGenerator.IsGenerating)
                {
                    yield return null;
                }
            }
            
            // 设置玩家初始位置
            if (playerController != null && mazeGenerator != null)
            {
                Vector2 startWorldPos = mazeGenerator.GridToWorld(mazeGenerator.StartPosition);
                playerController.SetPosition(startWorldPos);
                
                // 设置移动边界
                playerController.SetMovementBounds(mazeGenerator.GetMazeBounds());
            }
            
            yield return null;
        }
        
        private void SetupSystemConnections()
        {
            // 这里可以设置系统间的特殊连接
            // 大部分连接已经通过事件系统自动建立
            
            Debug.Log("系统连接设置完成");
        }
        
        #endregion
        
        #region 游戏循环控制
        
        /// <summary>
        /// 开始游戏循环
        /// </summary>
        public void StartGameLoop()
        {
            if (!IsInitialized)
            {
                Debug.LogWarning("系统未初始化，无法启动游戏循环");
                return;
            }
            
            IsRunning = true;
            OnGameLoopStarted?.Invoke();
            
            Debug.Log("游戏循环已启动");
        }
        
        /// <summary>
        /// 停止游戏循环
        /// </summary>
        public void StopGameLoop()
        {
            IsRunning = false;
            OnGameLoopStopped?.Invoke();
            
            Debug.Log("游戏循环已停止");
        }
        
        /// <summary>
        /// 暂停游戏循环
        /// </summary>
        public void PauseGameLoop()
        {
            Time.timeScale = 0f;
            Debug.Log("游戏循环已暂停");
        }
        
        /// <summary>
        /// 恢复游戏循环
        /// </summary>
        public void ResumeGameLoop()
        {
            Time.timeScale = 1f;
            Debug.Log("游戏循环已恢复");
        }
        
        #endregion
        
        #region 系统更新
        
        private void UpdateSystems()
        {
            // 这里可以添加需要在Update中执行的系统逻辑
            // 大部分系统都有自己的Update方法
            
            // 检查系统健康状态
            if (enableSystemProfiling)
            {
                CheckSystemHealth();
            }
        }
        
        private void FixedUpdateSystems()
        {
            // 这里可以添加需要在FixedUpdate中执行的系统逻辑
            // 主要是物理相关的更新
        }
        
        private void LateUpdateSystems()
        {
            // 这里可以添加需要在LateUpdate中执行的系统逻辑
            // 主要是相机跟随、UI更新等
        }
        
        #endregion
        
        #region 性能监控
        
        private void UpdatePerformanceMetrics()
        {
            if (!enablePerformanceMonitoring) return;
            
            frameTimeAccumulator += Time.unscaledDeltaTime;
            frameCount++;
            
            if (Time.time - lastPerformanceUpdate >= performanceUpdateInterval)
            {
                CurrentFPS = frameCount / (Time.time - lastPerformanceUpdate);
                AverageFrameTime = frameTimeAccumulator / frameCount;
                
                frameTimeAccumulator = 0f;
                frameCount = 0;
                lastPerformanceUpdate = Time.time;
                
                // 检查性能警告
                if (CurrentFPS < targetFrameRate * 0.8f)
                {
                    Debug.LogWarning($"性能警告: FPS过低 ({CurrentFPS:F1})");
                }
            }
        }
        
        private void CheckSystemHealth()
        {
            // 检查各个系统是否正常运行
            if (playerController == null)
            {
                ReportSystemError("PlayerController丢失");
            }
            
            if (gameStateManager == null)
            {
                ReportSystemError("GameStateManager丢失");
            }
            
            // 可以添加更多系统健康检查
        }
        
        #endregion
        
        #region 错误处理
        
        private void ReportSystemError(string errorMessage)
        {
            Debug.LogError($"系统错误: {errorMessage}");
            OnSystemError?.Invoke(errorMessage);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 重新初始化所有系统
        /// </summary>
        public void ReinitializeSystems()
        {
            StopGameLoop();
            IsInitialized = false;
            
            StartCoroutine(InitializeSystemsCoroutine());
        }
        
        /// <summary>
        /// 获取系统状态报告
        /// </summary>
        public string GetSystemStatusReport()
        {
            return $"游戏循环状态报告:\n" +
                   $"初始化完成: {IsInitialized}\n" +
                   $"循环运行中: {IsRunning}\n" +
                   $"当前FPS: {CurrentFPS:F1}\n" +
                   $"平均帧时间: {AverageFrameTime * 1000:F1}ms\n" +
                   $"目标帧率: {targetFrameRate}\n" +
                   $"物理更新率: {fixedUpdateRate}Hz\n" +
                   $"时间缩放: {Time.timeScale:F2}";
        }
        
        /// <summary>
        /// 设置目标帧率
        /// </summary>
        public void SetTargetFrameRate(int frameRate)
        {
            targetFrameRate = frameRate;
            Application.targetFrameRate = targetFrameRate;
        }
        
        /// <summary>
        /// 设置垂直同步
        /// </summary>
        public void SetVSync(bool enabled)
        {
            enableVSync = enabled;
            QualitySettings.vSyncCount = enabled ? 1 : 0;
        }
        
        /// <summary>
        /// 检查系统是否准备就绪
        /// </summary>
        public bool IsSystemReady()
        {
            return IsInitialized && IsRunning &&
                   gameManager != null &&
                   inputManager != null &&
                   playerController != null &&
                   gameStateManager != null;
        }
        
        #endregion
        
        #region 调试和可视化
        
        private void OnGUI()
        {
            if (showSystemStatus && Application.isPlaying)
            {
                GUILayout.BeginArea(new Rect(Screen.width - 300, 10, 290, 300));
                GUILayout.Box("系统状态");
                
                GUILayout.Label($"FPS: {CurrentFPS:F1}");
                GUILayout.Label($"帧时间: {AverageFrameTime * 1000:F1}ms");
                GUILayout.Label($"初始化: {IsInitialized}");
                GUILayout.Label($"运行中: {IsRunning}");
                GUILayout.Label($"时间缩放: {Time.timeScale:F2}");
                
                GUILayout.Space(10);
                
                // 系统状态指示器
                GUILayout.Label("系统状态:");
                DrawSystemIndicator("GameManager", gameManager != null);
                DrawSystemIndicator("InputManager", inputManager != null);
                DrawSystemIndicator("PlayerController", playerController != null);
                DrawSystemIndicator("MazeGenerator", mazeGenerator != null);
                DrawSystemIndicator("CollisionSystem", collisionSystem != null);
                DrawSystemIndicator("ItemSystem", itemSystem != null);
                DrawSystemIndicator("GameStateManager", gameStateManager != null);
                DrawSystemIndicator("UIManager", uiManager != null);
                
                GUILayout.EndArea();
            }
        }
        
        private void DrawSystemIndicator(string systemName, bool isActive)
        {
            GUI.color = isActive ? Color.green : Color.red;
            GUILayout.Label($"• {systemName}: {(isActive ? "正常" : "异常")}");
            GUI.color = Color.white;
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            StopGameLoop();
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                // 应用暂停时暂停游戏
                if (gameStateManager != null && gameStateManager.IsGameActive)
                {
                    gameStateManager.PauseGame();
                }
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                // 失去焦点时暂停游戏
                if (gameStateManager != null && gameStateManager.IsGameActive)
                {
                    gameStateManager.PauseGame();
                }
            }
        }
        
        #endregion
    }
}
