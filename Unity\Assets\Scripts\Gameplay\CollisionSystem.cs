using UnityEngine;
using System.Collections.Generic;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 碰撞检测系统 - 处理游戏中的各种碰撞检测
    /// </summary>
    public class CollisionSystem : MonoBehaviour
    {
        [Header("碰撞设置")]
        [SerializeField] private LayerMask wallLayer = 1 << 8;
        [SerializeField] private LayerMask itemLayer = 1 << 9;
        [SerializeField] private LayerMask trapLayer = 1 << 10;
        [SerializeField] private LayerMask boundaryLayer = 1 << 11;
        
        [Header("检测参数")]
        [SerializeField] private float playerRadius = 0.4f;
        [SerializeField] private float wallCheckDistance = 0.1f;
        [SerializeField] private float itemPickupDistance = 0.5f;
        [SerializeField] private int maxCollisionChecks = 10;
        
        [Header("优化设置")]
        [SerializeField] private bool useGridOptimization = true;
        [SerializeField] private float gridCellSize = 2f;
        [SerializeField] private bool enableCollisionCaching = true;
        [SerializeField] private float cacheUpdateInterval = 0.1f;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugRays = false;
        [SerializeField] private bool showCollisionBounds = false;
        
        // 单例模式
        public static CollisionSystem Instance { get; private set; }
        
        // 碰撞缓存
        private Dictionary<Vector2Int, List<Collider2D>> gridColliders;
        private HashSet<Collider2D> nearbyColliders;
        private float lastCacheUpdate;
        
        // 碰撞结果缓存
        private RaycastHit2D[] raycastResults;
        private Collider2D[] overlapResults;
        
        // 事件系统
        public System.Action<Vector2, CollisionType> OnCollisionDetected;
        public System.Action<GameObject> OnItemCollected;
        public System.Action<GameObject> OnTrapTriggered;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeCollisionSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupCollisionSystem();
        }
        
        private void Update()
        {
            if (enableCollisionCaching && Time.time - lastCacheUpdate > cacheUpdateInterval)
            {
                UpdateCollisionCache();
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeCollisionSystem()
        {
            // 初始化数据结构
            gridColliders = new Dictionary<Vector2Int, List<Collider2D>>();
            nearbyColliders = new HashSet<Collider2D>();
            
            // 初始化结果数组
            raycastResults = new RaycastHit2D[maxCollisionChecks];
            overlapResults = new Collider2D[maxCollisionChecks];
            
            lastCacheUpdate = 0f;
        }
        
        private void SetupCollisionSystem()
        {
            // 从游戏配置加载设置
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                gridCellSize = config.cellSize * 2f; // 网格大小为单元格的2倍
            }
            
            // 初始化碰撞网格
            if (useGridOptimization)
            {
                InitializeCollisionGrid();
            }
            
            Debug.Log("碰撞检测系统初始化完成");
        }
        
        private void InitializeCollisionGrid()
        {
            // 清空现有网格
            gridColliders.Clear();
            
            // 获取所有碰撞体并分配到网格
            Collider2D[] allColliders = FindObjectsOfType<Collider2D>();
            
            foreach (Collider2D collider in allColliders)
            {
                if (collider != null && !collider.isTrigger)
                {
                    AddColliderToGrid(collider);
                }
            }
            
            Debug.Log($"碰撞网格初始化完成，共 {allColliders.Length} 个碰撞体");
        }
        
        #endregion
        
        #region 墙壁碰撞检测
        
        /// <summary>
        /// 检查玩家移动是否会与墙壁碰撞
        /// </summary>
        public bool CheckWallCollision(Vector2 currentPosition, Vector2 targetPosition)
        {
            Vector2 direction = (targetPosition - currentPosition).normalized;
            float distance = Vector2.Distance(currentPosition, targetPosition);
            
            // 使用圆形碰撞检测
            if (Physics2D.CircleCast(currentPosition, playerRadius, direction, distance, wallLayer))
            {
                if (showDebugRays)
                {
                    Debug.DrawRay(currentPosition, direction * distance, Color.red, 0.1f);
                }
                return true;
            }
            
            // 额外检查目标位置
            if (Physics2D.OverlapCircle(targetPosition, playerRadius, wallLayer))
            {
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 检查指定位置是否可通行
        /// </summary>
        public bool IsPositionWalkable(Vector2 position)
        {
            // 检查迷宫网格
            if (MazeGenerator.Instance != null)
            {
                Vector2Int gridPos = MazeGenerator.Instance.WorldToGrid(position);
                if (!MazeGenerator.Instance.IsWalkable(gridPos))
                {
                    return false;
                }
            }
            
            // 检查物理碰撞
            return !Physics2D.OverlapCircle(position, playerRadius, wallLayer);
        }
        
        /// <summary>
        /// 获取最近的可通行位置
        /// </summary>
        public Vector2 GetNearestWalkablePosition(Vector2 targetPosition, Vector2 currentPosition)
        {
            if (IsPositionWalkable(targetPosition))
            {
                return targetPosition;
            }
            
            // 尝试周围的位置
            float[] angles = { 0f, 45f, 90f, 135f, 180f, 225f, 270f, 315f };
            float[] distances = { 0.1f, 0.2f, 0.3f, 0.5f };
            
            foreach (float distance in distances)
            {
                foreach (float angle in angles)
                {
                    Vector2 offset = new Vector2(
                        Mathf.Cos(angle * Mathf.Deg2Rad),
                        Mathf.Sin(angle * Mathf.Deg2Rad)
                    ) * distance;
                    
                    Vector2 testPosition = targetPosition + offset;
                    
                    if (IsPositionWalkable(testPosition))
                    {
                        return testPosition;
                    }
                }
            }
            
            // 如果找不到，返回当前位置
            return currentPosition;
        }
        
        #endregion
        
        #region 道具碰撞检测
        
        /// <summary>
        /// 检测玩家附近的道具
        /// </summary>
        public List<GameObject> DetectNearbyItems(Vector2 playerPosition)
        {
            List<GameObject> nearbyItems = new List<GameObject>();
            
            // 使用重叠检测找到附近的道具
            int hitCount = Physics2D.OverlapCircleNonAlloc(
                playerPosition, 
                itemPickupDistance, 
                overlapResults, 
                itemLayer
            );
            
            for (int i = 0; i < hitCount; i++)
            {
                if (overlapResults[i] != null && overlapResults[i].gameObject != null)
                {
                    nearbyItems.Add(overlapResults[i].gameObject);
                }
            }
            
            return nearbyItems;
        }
        
        /// <summary>
        /// 尝试拾取道具
        /// </summary>
        public bool TryPickupItem(Vector2 playerPosition, out GameObject pickedItem)
        {
            pickedItem = null;
            
            Collider2D itemCollider = Physics2D.OverlapCircle(playerPosition, itemPickupDistance, itemLayer);
            
            if (itemCollider != null)
            {
                pickedItem = itemCollider.gameObject;
                
                // 触发拾取事件
                OnItemCollected?.Invoke(pickedItem);
                
                // 禁用碰撞体（道具被拾取）
                itemCollider.enabled = false;
                
                return true;
            }
            
            return false;
        }
        
        #endregion
        
        #region 陷阱碰撞检测
        
        /// <summary>
        /// 检测玩家是否触发陷阱
        /// </summary>
        public List<GameObject> DetectTriggeredTraps(Vector2 playerPosition)
        {
            List<GameObject> triggeredTraps = new List<GameObject>();
            
            int hitCount = Physics2D.OverlapCircleNonAlloc(
                playerPosition,
                playerRadius,
                overlapResults,
                trapLayer
            );
            
            for (int i = 0; i < hitCount; i++)
            {
                if (overlapResults[i] != null)
                {
                    GameObject trap = overlapResults[i].gameObject;
                    
                    // 检查陷阱是否可以被触发
                    TrapController trapController = trap.GetComponent<TrapController>();
                    if (trapController != null && trapController.CanTrigger())
                    {
                        triggeredTraps.Add(trap);
                        OnTrapTriggered?.Invoke(trap);
                    }
                }
            }
            
            return triggeredTraps;
        }
        
        #endregion
        
        #region 边界检测
        
        /// <summary>
        /// 检查位置是否在游戏边界内
        /// </summary>
        public bool IsWithinBounds(Vector2 position)
        {
            if (MazeGenerator.Instance != null)
            {
                Bounds mazeBounds = MazeGenerator.Instance.GetMazeBounds();
                return mazeBounds.Contains(position);
            }
            
            // 默认边界检查
            return Mathf.Abs(position.x) < 50f && Mathf.Abs(position.y) < 50f;
        }
        
        /// <summary>
        /// 将位置限制在边界内
        /// </summary>
        public Vector2 ClampToBounds(Vector2 position)
        {
            if (MazeGenerator.Instance != null)
            {
                Bounds mazeBounds = MazeGenerator.Instance.GetMazeBounds();
                return new Vector2(
                    Mathf.Clamp(position.x, mazeBounds.min.x + playerRadius, mazeBounds.max.x - playerRadius),
                    Mathf.Clamp(position.y, mazeBounds.min.y + playerRadius, mazeBounds.max.y - playerRadius)
                );
            }
            
            return position;
        }
        
        #endregion
        
        #region 网格优化
        
        private void AddColliderToGrid(Collider2D collider)
        {
            Vector2Int gridPos = WorldToGrid(collider.bounds.center);
            
            if (!gridColliders.ContainsKey(gridPos))
            {
                gridColliders[gridPos] = new List<Collider2D>();
            }
            
            gridColliders[gridPos].Add(collider);
        }
        
        private void RemoveColliderFromGrid(Collider2D collider)
        {
            Vector2Int gridPos = WorldToGrid(collider.bounds.center);
            
            if (gridColliders.ContainsKey(gridPos))
            {
                gridColliders[gridPos].Remove(collider);
                
                if (gridColliders[gridPos].Count == 0)
                {
                    gridColliders.Remove(gridPos);
                }
            }
        }
        
        private Vector2Int WorldToGrid(Vector2 worldPosition)
        {
            return new Vector2Int(
                Mathf.FloorToInt(worldPosition.x / gridCellSize),
                Mathf.FloorToInt(worldPosition.y / gridCellSize)
            );
        }
        
        private void UpdateCollisionCache()
        {
            if (PlayerController.Instance != null)
            {
                Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
                UpdateNearbyColliders(playerPos);
            }
            
            lastCacheUpdate = Time.time;
        }
        
        private void UpdateNearbyColliders(Vector2 centerPosition)
        {
            nearbyColliders.Clear();
            
            Vector2Int centerGrid = WorldToGrid(centerPosition);
            
            // 检查周围9个网格
            for (int x = -1; x <= 1; x++)
            {
                for (int y = -1; y <= 1; y++)
                {
                    Vector2Int gridPos = centerGrid + new Vector2Int(x, y);
                    
                    if (gridColliders.ContainsKey(gridPos))
                    {
                        foreach (Collider2D collider in gridColliders[gridPos])
                        {
                            if (collider != null)
                            {
                                nearbyColliders.Add(collider);
                            }
                        }
                    }
                }
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 注册新的碰撞体到系统
        /// </summary>
        public void RegisterCollider(Collider2D collider)
        {
            if (useGridOptimization && collider != null)
            {
                AddColliderToGrid(collider);
            }
        }
        
        /// <summary>
        /// 从系统中移除碰撞体
        /// </summary>
        public void UnregisterCollider(Collider2D collider)
        {
            if (useGridOptimization && collider != null)
            {
                RemoveColliderFromGrid(collider);
            }
        }
        
        /// <summary>
        /// 设置玩家碰撞半径
        /// </summary>
        public void SetPlayerRadius(float radius)
        {
            playerRadius = Mathf.Max(0.1f, radius);
        }
        
        /// <summary>
        /// 获取碰撞统计信息
        /// </summary>
        public string GetCollisionStats()
        {
            int totalColliders = 0;
            foreach (var kvp in gridColliders)
            {
                totalColliders += kvp.Value.Count;
            }
            
            return $"网格数量: {gridColliders.Count}\n" +
                   $"总碰撞体: {totalColliders}\n" +
                   $"附近碰撞体: {nearbyColliders.Count}\n" +
                   $"玩家半径: {playerRadius:F2}";
        }
        
        #endregion
        
        #region 调试绘制
        
        private void OnDrawGizmosSelected()
        {
            if (!showCollisionBounds) return;
            
            // 绘制玩家碰撞范围
            if (PlayerController.Instance != null)
            {
                Vector2 playerPos = PlayerController.Instance.GetWorldPosition();
                
                Gizmos.color = Color.blue;
                Gizmos.DrawWireSphere(playerPos, playerRadius);
                
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(playerPos, itemPickupDistance);
            }
            
            // 绘制网格
            if (useGridOptimization)
            {
                Gizmos.color = Color.yellow;
                foreach (var kvp in gridColliders)
                {
                    Vector2 gridCenter = new Vector2(
                        kvp.Key.x * gridCellSize + gridCellSize * 0.5f,
                        kvp.Key.y * gridCellSize + gridCellSize * 0.5f
                    );
                    
                    Gizmos.DrawWireCube(gridCenter, Vector3.one * gridCellSize);
                }
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 碰撞类型枚举
    /// </summary>
    public enum CollisionType
    {
        Wall,       // 墙壁
        Item,       // 道具
        Trap,       // 陷阱
        Boundary,   // 边界
        Other       // 其他
    }
    
    /// <summary>
    /// 简单的陷阱控制器接口
    /// </summary>
    public interface ITrapController
    {
        bool CanTrigger();
        void Trigger();
    }
    
    /// <summary>
    /// 基础陷阱控制器
    /// </summary>
    public class TrapController : MonoBehaviour, ITrapController
    {
        [SerializeField] private float cooldownTime = 1f;
        private float lastTriggerTime = 0f;
        
        public bool CanTrigger()
        {
            return Time.time - lastTriggerTime >= cooldownTime;
        }
        
        public void Trigger()
        {
            lastTriggerTime = Time.time;
            // 子类实现具体的陷阱逻辑
        }
    }
}
