using UnityEngine;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 游戏配置文件 - 存储游戏的各种配置参数
    /// </summary>
    [CreateAssetMenu(fileName = "GameConfig", menuName = "MazeAdventure/Game Config")]
    public class GameConfig : ScriptableObject
    {
        [Header("玩家设置")]
        [Tooltip("玩家移动速度")]
        public float playerMoveSpeed = 5f;
        
        [Tooltip("玩家最大生命值")]
        public float playerMaxHealth = 100f;
        
        [Tooltip("玩家最大电量")]
        public float playerMaxBattery = 100f;
        
        [Tooltip("电量消耗速率（每秒）")]
        public float batteryDrainRate = 1f;
        
        [Header("手电筒设置")]
        [Tooltip("手电筒最大照射范围")]
        public float flashlightMaxRange = 8f;
        
        [Tooltip("手电筒最小照射范围（无电时）")]
        public float flashlightMinRange = 2f;
        
        [Tooltip("手电筒光强度")]
        public float flashlightIntensity = 1.5f;
        
        [Tooltip("手电筒颜色")]
        public Color flashlightColor = Color.white;
        
        [Header("迷宫设置")]
        [Tooltip("迷宫宽度（格子数）")]
        public int mazeWidth = 25;
        
        [Tooltip("迷宫高度（格子数）")]
        public int mazeHeight = 15;
        
        [Tooltip("每个格子的像素大小")]
        public float cellSize = 1f;
        
        [Tooltip("房间生成概率")]
        [Range(0f, 1f)]
        public float roomGenerationChance = 0.3f;
        
        [Header("道具设置")]
        [Tooltip("电池恢复的电量")]
        public float batteryRestoreAmount = 30f;
        
        [Tooltip("医疗包恢复的生命值")]
        public float healthPackRestoreAmount = 50f;
        
        [Tooltip("道具生成数量")]
        public int itemSpawnCount = 8;
        
        [Header("机关设置")]
        [Tooltip("尖刺陷阱伤害")]
        public float spikeTrapDamage = 25f;
        
        [Tooltip("机关触发冷却时间")]
        public float trapCooldownTime = 1f;
        
        [Tooltip("机关生成数量")]
        public int trapSpawnCount = 6;
        
        [Header("UI设置")]
        [Tooltip("HUD更新频率（秒）")]
        public float hudUpdateRate = 0.1f;
        
        [Tooltip("低电量警告阈值")]
        [Range(0f, 1f)]
        public float lowBatteryWarningThreshold = 0.2f;
        
        [Tooltip("低生命值警告阈值")]
        [Range(0f, 1f)]
        public float lowHealthWarningThreshold = 0.3f;
        
        [Header("音频设置")]
        [Tooltip("主音量")]
        [Range(0f, 1f)]
        public float masterVolume = 1f;
        
        [Tooltip("音效音量")]
        [Range(0f, 1f)]
        public float sfxVolume = 0.8f;
        
        [Tooltip("背景音乐音量")]
        [Range(0f, 1f)]
        public float musicVolume = 0.6f;
        
        [Header("性能设置")]
        [Tooltip("目标帧率")]
        public int targetFrameRate = 60;
        
        [Tooltip("垂直同步")]
        public bool enableVSync = true;
        
        [Tooltip("抗锯齿级别")]
        public int antiAliasing = 2;
        
        [Header("输入设置")]
        [Tooltip("虚拟摇杆灵敏度")]
        [Range(0.1f, 2f)]
        public float joystickSensitivity = 1f;
        
        [Tooltip("虚拟摇杆死区")]
        [Range(0f, 0.5f)]
        public float joystickDeadZone = 0.1f;
        
        [Tooltip("触摸灵敏度")]
        [Range(0.5f, 3f)]
        public float touchSensitivity = 1.5f;
        
        [Header("调试设置")]
        [Tooltip("显示FPS")]
        public bool showFPS = false;
        
        [Tooltip("显示调试信息")]
        public bool showDebugInfo = false;
        
        [Tooltip("显示碰撞体")]
        public bool showColliders = false;
        
        [Header("分辨率适配")]
        [Tooltip("参考分辨率")]
        public Vector2 referenceResolution = new Vector2(1920, 1080);
        
        [Tooltip("屏幕匹配模式")]
        public UnityEngine.UI.CanvasScaler.ScreenMatchMode screenMatchMode = 
            UnityEngine.UI.CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        
        [Tooltip("匹配权重")]
        [Range(0f, 1f)]
        public float matchWidthOrHeight = 0.5f;
        
        #region 验证方法
        
        private void OnValidate()
        {
            // 确保数值在合理范围内
            playerMoveSpeed = Mathf.Max(0.1f, playerMoveSpeed);
            playerMaxHealth = Mathf.Max(1f, playerMaxHealth);
            playerMaxBattery = Mathf.Max(1f, playerMaxBattery);
            batteryDrainRate = Mathf.Max(0.01f, batteryDrainRate);
            
            flashlightMaxRange = Mathf.Max(1f, flashlightMaxRange);
            flashlightMinRange = Mathf.Max(0.5f, flashlightMinRange);
            flashlightMinRange = Mathf.Min(flashlightMinRange, flashlightMaxRange);
            
            mazeWidth = Mathf.Max(5, mazeWidth);
            mazeHeight = Mathf.Max(5, mazeHeight);
            cellSize = Mathf.Max(0.1f, cellSize);
            
            batteryRestoreAmount = Mathf.Max(1f, batteryRestoreAmount);
            healthPackRestoreAmount = Mathf.Max(1f, healthPackRestoreAmount);
            
            spikeTrapDamage = Mathf.Max(1f, spikeTrapDamage);
            trapCooldownTime = Mathf.Max(0.1f, trapCooldownTime);
            
            hudUpdateRate = Mathf.Max(0.01f, hudUpdateRate);
            
            targetFrameRate = Mathf.Max(30, targetFrameRate);
            antiAliasing = Mathf.Max(0, antiAliasing);
            
            referenceResolution.x = Mathf.Max(640, referenceResolution.x);
            referenceResolution.y = Mathf.Max(480, referenceResolution.y);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取基于电量百分比的手电筒范围
        /// </summary>
        public float GetFlashlightRange(float batteryPercentage)
        {
            batteryPercentage = Mathf.Clamp01(batteryPercentage);
            return Mathf.Lerp(flashlightMinRange, flashlightMaxRange, batteryPercentage);
        }
        
        /// <summary>
        /// 检查是否为低电量状态
        /// </summary>
        public bool IsLowBattery(float batteryPercentage)
        {
            return batteryPercentage <= lowBatteryWarningThreshold;
        }
        
        /// <summary>
        /// 检查是否为低生命值状态
        /// </summary>
        public bool IsLowHealth(float healthPercentage)
        {
            return healthPercentage <= lowHealthWarningThreshold;
        }
        
        /// <summary>
        /// 获取迷宫总大小（世界坐标）
        /// </summary>
        public Vector2 GetMazeWorldSize()
        {
            return new Vector2(mazeWidth * cellSize, mazeHeight * cellSize);
        }
        
        #endregion
    }
}
