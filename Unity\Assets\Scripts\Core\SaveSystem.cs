using System.IO;
using UnityEngine;
using System.Security.Cryptography;
using System.Text;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 保存系统 - 负责游戏数据的保存和加载
    /// </summary>
    public static class SaveSystem
    {
        private const string SAVE_FILE_NAME = "gamedata.save";
        private const string BACKUP_FILE_NAME = "gamedata.backup";
        private const string ENCRYPTION_KEY = "MazeAdventure2024"; // 简单的加密密钥
        
        private static string SavePath => Path.Combine(Application.persistentDataPath, SAVE_FILE_NAME);
        private static string BackupPath => Path.Combine(Application.persistentDataPath, BACKUP_FILE_NAME);
        
        /// <summary>
        /// 保存游戏数据
        /// </summary>
        public static bool SaveGameData(GameData gameData)
        {
            try
            {
                // 验证数据
                gameData.ValidateData();
                
                // 序列化数据
                string jsonData = JsonUtility.ToJson(gameData, true);
                
                // 加密数据
                string encryptedData = EncryptString(jsonData);
                
                // 创建备份
                if (File.Exists(SavePath))
                {
                    File.Copy(SavePath, BackupPath, true);
                }
                
                // 保存到文件
                File.WriteAllText(SavePath, encryptedData);
                
                Debug.Log($"游戏数据保存成功: {SavePath}");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"保存游戏数据失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 加载游戏数据
        /// </summary>
        public static GameData LoadGameData()
        {
            try
            {
                string filePath = SavePath;
                
                // 如果主文件不存在，尝试加载备份
                if (!File.Exists(filePath))
                {
                    if (File.Exists(BackupPath))
                    {
                        filePath = BackupPath;
                        Debug.LogWarning("主存档文件不存在，使用备份文件");
                    }
                    else
                    {
                        Debug.Log("未找到存档文件，返回新的游戏数据");
                        return null;
                    }
                }
                
                // 读取文件
                string encryptedData = File.ReadAllText(filePath);
                
                // 解密数据
                string jsonData = DecryptString(encryptedData);
                
                // 反序列化数据
                GameData gameData = JsonUtility.FromJson<GameData>(jsonData);
                
                // 验证数据
                gameData.ValidateData();
                
                Debug.Log($"游戏数据加载成功: {filePath}");
                return gameData;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载游戏数据失败: {e.Message}");
                
                // 尝试加载备份
                if (File.Exists(BackupPath) && SavePath != BackupPath)
                {
                    Debug.Log("尝试从备份文件恢复数据");
                    try
                    {
                        string backupData = File.ReadAllText(BackupPath);
                        string jsonData = DecryptString(backupData);
                        GameData gameData = JsonUtility.FromJson<GameData>(jsonData);
                        gameData.ValidateData();
                        
                        Debug.Log("从备份文件恢复数据成功");
                        return gameData;
                    }
                    catch (System.Exception backupException)
                    {
                        Debug.LogError($"备份文件也损坏: {backupException.Message}");
                    }
                }
                
                return null;
            }
        }
        
        /// <summary>
        /// 删除存档文件
        /// </summary>
        public static bool DeleteSaveData()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    File.Delete(SavePath);
                }
                
                if (File.Exists(BackupPath))
                {
                    File.Delete(BackupPath);
                }
                
                Debug.Log("存档文件删除成功");
                return true;
            }
            catch (System.Exception e)
            {
                Debug.LogError($"删除存档文件失败: {e.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 检查存档文件是否存在
        /// </summary>
        public static bool SaveFileExists()
        {
            return File.Exists(SavePath) || File.Exists(BackupPath);
        }
        
        /// <summary>
        /// 获取存档文件大小
        /// </summary>
        public static long GetSaveFileSize()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    FileInfo fileInfo = new FileInfo(SavePath);
                    return fileInfo.Length;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"获取存档文件大小失败: {e.Message}");
            }
            
            return 0;
        }
        
        /// <summary>
        /// 获取存档文件修改时间
        /// </summary>
        public static System.DateTime GetSaveFileLastWriteTime()
        {
            try
            {
                if (File.Exists(SavePath))
                {
                    return File.GetLastWriteTime(SavePath);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"获取存档文件时间失败: {e.Message}");
            }
            
            return System.DateTime.MinValue;
        }
        
        /// <summary>
        /// 导出存档数据（用于调试）
        /// </summary>
        public static string ExportSaveData()
        {
            try
            {
                GameData gameData = LoadGameData();
                if (gameData != null)
                {
                    return JsonUtility.ToJson(gameData, true);
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"导出存档数据失败: {e.Message}");
            }
            
            return null;
        }
        
        /// <summary>
        /// 导入存档数据（用于调试）
        /// </summary>
        public static bool ImportSaveData(string jsonData)
        {
            try
            {
                GameData gameData = JsonUtility.FromJson<GameData>(jsonData);
                return SaveGameData(gameData);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"导入存档数据失败: {e.Message}");
                return false;
            }
        }
        
        #region 加密解密方法
        
        /// <summary>
        /// 加密字符串
        /// </summary>
        private static string EncryptString(string plainText)
        {
            try
            {
                byte[] plainBytes = Encoding.UTF8.GetBytes(plainText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                // 简单的XOR加密
                for (int i = 0; i < plainBytes.Length; i++)
                {
                    plainBytes[i] = (byte)(plainBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }
                
                return System.Convert.ToBase64String(plainBytes);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加密失败: {e.Message}");
                return plainText; // 如果加密失败，返回原文
            }
        }
        
        /// <summary>
        /// 解密字符串
        /// </summary>
        private static string DecryptString(string encryptedText)
        {
            try
            {
                byte[] encryptedBytes = System.Convert.FromBase64String(encryptedText);
                byte[] keyBytes = Encoding.UTF8.GetBytes(ENCRYPTION_KEY);
                
                // 简单的XOR解密
                for (int i = 0; i < encryptedBytes.Length; i++)
                {
                    encryptedBytes[i] = (byte)(encryptedBytes[i] ^ keyBytes[i % keyBytes.Length]);
                }
                
                return Encoding.UTF8.GetString(encryptedBytes);
            }
            catch (System.Exception e)
            {
                Debug.LogError($"解密失败: {e.Message}");
                return encryptedText; // 如果解密失败，返回原文
            }
        }
        
        #endregion
        
        #region 自动保存功能
        
        private static float autoSaveInterval = 30f; // 30秒自动保存一次
        private static float lastAutoSaveTime = 0f;
        
        /// <summary>
        /// 更新自动保存（需要在GameManager中调用）
        /// </summary>
        public static void UpdateAutoSave()
        {
            if (Time.time - lastAutoSaveTime >= autoSaveInterval)
            {
                GameData gameData = GameManager.Instance?.GetGameData();
                if (gameData != null)
                {
                    SaveGameData(gameData);
                    lastAutoSaveTime = Time.time;
                    Debug.Log("自动保存完成");
                }
            }
        }
        
        /// <summary>
        /// 设置自动保存间隔
        /// </summary>
        public static void SetAutoSaveInterval(float interval)
        {
            autoSaveInterval = Mathf.Max(10f, interval);
        }
        
        #endregion
    }
}
