using UnityEngine;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 玩家控制器 - 处理玩家移动、动画和状态
    /// </summary>
    [RequireComponent(typeof(Rigidbody2D))]
    [RequireComponent(typeof(Collider2D))]
    public class PlayerController : MonoBehaviour
    {
        [Header("移动设置")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float acceleration = 20f;
        [SerializeField] private float deceleration = 15f;
        [SerializeField] private bool usePhysicsMovement = true;
        
        [Header("动画设置")]
        [SerializeField] private Animator animator;
        [SerializeField] private SpriteRenderer spriteRenderer;
        [SerializeField] private bool flipSpriteForDirection = true;
        
        [Header("边界设置")]
        [SerializeField] private bool constrainToBounds = true;
        [SerializeField] private Bounds movementBounds = new Bounds(Vector3.zero, Vector3.one * 20f);
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool showMovementBounds = true;
        
        // 单例模式
        public static PlayerController Instance { get; private set; }
        
        // 组件引用
        private Rigidbody2D rb2d;
        private Collider2D playerCollider;
        
        // 移动状态
        public Vector2 MoveInput { get; private set; }
        public Vector2 Velocity { get; private set; }
        public bool IsMoving { get; private set; }
        public PlayerDirection CurrentDirection { get; private set; }
        
        // 玩家状态
        public PlayerState CurrentState { get; private set; }
        public bool CanMove { get; set; } = true;
        
        // 事件系统
        public System.Action<Vector2> OnPositionChanged;
        public System.Action<PlayerDirection> OnDirectionChanged;
        public System.Action<PlayerState> OnStateChanged;
        
        // 私有变量
        private Vector2 currentVelocity;
        private Vector2 targetVelocity;
        private PlayerDirection lastDirection;
        private float lastMoveTime;
        
        // 动画参数哈希
        private int animMoveX;
        private int animMoveY;
        private int animIsMoving;
        private int animDirection;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializePlayer();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupPlayer();
        }
        
        private void Update()
        {
            HandleInput();
            UpdateMovement();
            UpdateAnimation();
            UpdateState();
        }
        
        private void FixedUpdate()
        {
            if (usePhysicsMovement)
            {
                ApplyPhysicsMovement();
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializePlayer()
        {
            // 获取组件引用
            rb2d = GetComponent<Rigidbody2D>();
            playerCollider = GetComponent<Collider2D>();
            
            if (animator == null)
                animator = GetComponent<Animator>();
            
            if (spriteRenderer == null)
                spriteRenderer = GetComponent<SpriteRenderer>();
            
            // 设置物理属性
            if (rb2d != null)
            {
                rb2d.gravityScale = 0f; // 2D顶视图不需要重力
                rb2d.drag = 0f;
                rb2d.angularDrag = 0f;
                rb2d.freezeRotation = true;
            }
            
            // 缓存动画参数
            if (animator != null)
            {
                animMoveX = Animator.StringToHash("MoveX");
                animMoveY = Animator.StringToHash("MoveY");
                animIsMoving = Animator.StringToHash("IsMoving");
                animDirection = Animator.StringToHash("Direction");
            }
            
            // 初始化状态
            CurrentState = PlayerState.Idle;
            CurrentDirection = PlayerDirection.Down;
            lastDirection = CurrentDirection;
        }
        
        private void SetupPlayer()
        {
            // 订阅输入事件
            if (InputManagerSimple.Instance != null)
            {
                InputManagerSimple.Instance.OnMoveInputChanged += HandleMoveInput;
            }
            
            // 从游戏配置加载设置
            LoadPlayerSettings();
            
            Debug.Log("玩家控制器初始化完成");
        }
        
        private void LoadPlayerSettings()
        {
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                moveSpeed = config.playerMoveSpeed;
            }
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInput()
        {
            // 如果有专用的输入管理器，优先使用
            if (InputManagerSimple.Instance != null)
            {
                MoveInput = InputManagerSimple.Instance.MoveInput;
            }
            else
            {
                // 备用输入处理
                HandleFallbackInput();
            }
        }
        
        private void HandleFallbackInput()
        {
            Vector2 input = Vector2.zero;
            
            // 键盘输入（编辑器测试）
            if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
                input.y += 1;
            if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
                input.y -= 1;
            if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
                input.x -= 1;
            if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
                input.x += 1;
            
            MoveInput = input.normalized;
        }
        
        private void HandleMoveInput(Vector2 input)
        {
            MoveInput = input;
        }
        
        #endregion
        
        #region 移动逻辑
        
        private void UpdateMovement()
        {
            if (!CanMove)
            {
                targetVelocity = Vector2.zero;
                return;
            }
            
            // 计算目标速度
            targetVelocity = MoveInput * moveSpeed;
            
            // 平滑加速/减速
            if (targetVelocity.magnitude > 0.1f)
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, targetVelocity, acceleration * Time.deltaTime);
                lastMoveTime = Time.time;
            }
            else
            {
                currentVelocity = Vector2.MoveTowards(currentVelocity, Vector2.zero, deceleration * Time.deltaTime);
            }
            
            Velocity = currentVelocity;
            IsMoving = Velocity.magnitude > 0.1f;
            
            // 更新方向
            UpdateDirection();
            
            // 应用移动
            if (!usePhysicsMovement)
            {
                ApplyDirectMovement();
            }
        }
        
        private void UpdateDirection()
        {
            if (MoveInput.magnitude > 0.1f)
            {
                PlayerDirection newDirection = GetDirectionFromInput(MoveInput);
                if (newDirection != CurrentDirection)
                {
                    lastDirection = CurrentDirection;
                    CurrentDirection = newDirection;
                    OnDirectionChanged?.Invoke(CurrentDirection);
                }
            }
        }
        
        private PlayerDirection GetDirectionFromInput(Vector2 input)
        {
            // 确定主要方向
            if (Mathf.Abs(input.x) > Mathf.Abs(input.y))
            {
                return input.x > 0 ? PlayerDirection.Right : PlayerDirection.Left;
            }
            else
            {
                return input.y > 0 ? PlayerDirection.Up : PlayerDirection.Down;
            }
        }
        
        private void ApplyDirectMovement()
        {
            Vector3 newPosition = transform.position + (Vector3)Velocity * Time.deltaTime;
            
            // 边界约束
            if (constrainToBounds)
            {
                newPosition = ConstrainToBounds(newPosition);
            }
            
            // 应用位置
            if (Vector3.Distance(transform.position, newPosition) > 0.001f)
            {
                transform.position = newPosition;
                OnPositionChanged?.Invoke(transform.position);
            }
        }
        
        private void ApplyPhysicsMovement()
        {
            if (rb2d != null)
            {
                Vector2 newPosition = rb2d.position + Velocity * Time.fixedDeltaTime;
                
                // 边界约束
                if (constrainToBounds)
                {
                    newPosition = ConstrainToBounds(newPosition);
                }
                
                rb2d.MovePosition(newPosition);
                OnPositionChanged?.Invoke(newPosition);
            }
        }
        
        private Vector2 ConstrainToBounds(Vector2 position)
        {
            return new Vector2(
                Mathf.Clamp(position.x, movementBounds.min.x, movementBounds.max.x),
                Mathf.Clamp(position.y, movementBounds.min.y, movementBounds.max.y)
            );
        }
        
        #endregion
        
        #region 动画控制
        
        private void UpdateAnimation()
        {
            if (animator == null) return;
            
            // 设置移动参数
            animator.SetFloat(animMoveX, MoveInput.x);
            animator.SetFloat(animMoveY, MoveInput.y);
            animator.SetBool(animIsMoving, IsMoving);
            animator.SetInteger(animDirection, (int)CurrentDirection);
            
            // 处理精灵翻转
            if (flipSpriteForDirection && spriteRenderer != null)
            {
                if (CurrentDirection == PlayerDirection.Left)
                {
                    spriteRenderer.flipX = true;
                }
                else if (CurrentDirection == PlayerDirection.Right)
                {
                    spriteRenderer.flipX = false;
                }
            }
        }
        
        #endregion
        
        #region 状态管理
        
        private void UpdateState()
        {
            PlayerState newState = DeterminePlayerState();
            
            if (newState != CurrentState)
            {
                PlayerState previousState = CurrentState;
                CurrentState = newState;
                OnStateChanged?.Invoke(CurrentState);
                
                if (showDebugInfo)
                {
                    Debug.Log($"玩家状态改变: {previousState} -> {CurrentState}");
                }
            }
        }
        
        private PlayerState DeterminePlayerState()
        {
            if (!CanMove)
                return PlayerState.Disabled;
            
            if (IsMoving)
                return PlayerState.Moving;
            
            if (Time.time - lastMoveTime < 0.1f)
                return PlayerState.Stopping;
            
            return PlayerState.Idle;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置玩家位置
        /// </summary>
        public void SetPosition(Vector2 position)
        {
            if (usePhysicsMovement && rb2d != null)
            {
                rb2d.position = position;
            }
            else
            {
                transform.position = position;
            }
            
            OnPositionChanged?.Invoke(position);
        }
        
        /// <summary>
        /// 设置移动边界
        /// </summary>
        public void SetMovementBounds(Bounds bounds)
        {
            movementBounds = bounds;
        }
        
        /// <summary>
        /// 启用/禁用移动
        /// </summary>
        public void SetCanMove(bool canMove)
        {
            CanMove = canMove;
            if (!canMove)
            {
                currentVelocity = Vector2.zero;
                targetVelocity = Vector2.zero;
            }
        }
        
        /// <summary>
        /// 获取玩家世界位置
        /// </summary>
        public Vector2 GetWorldPosition()
        {
            return transform.position;
        }
        
        /// <summary>
        /// 获取玩家网格位置
        /// </summary>
        public Vector2Int GetGridPosition(float cellSize = 1f)
        {
            Vector2 worldPos = GetWorldPosition();
            return new Vector2Int(
                Mathf.RoundToInt(worldPos.x / cellSize),
                Mathf.RoundToInt(worldPos.y / cellSize)
            );
        }
        
        #endregion
        
        #region 调试
        
        private void OnDrawGizmosSelected()
        {
            if (showMovementBounds)
            {
                // 绘制移动边界
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireCube(movementBounds.center, movementBounds.size);
            }
            
            if (showDebugInfo && Application.isPlaying)
            {
                // 绘制速度向量
                Gizmos.color = Color.red;
                Gizmos.DrawRay(transform.position, Velocity);
                
                // 绘制输入向量
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(transform.position, MoveInput * 2f);
            }
        }
        
        private void OnGUI()
        {
            if (showDebugInfo && Application.isPlaying)
            {
                GUILayout.BeginArea(new Rect(10, 10, 300, 200));
                GUILayout.Label($"位置: {transform.position}");
                GUILayout.Label($"速度: {Velocity}");
                GUILayout.Label($"输入: {MoveInput}");
                GUILayout.Label($"方向: {CurrentDirection}");
                GUILayout.Label($"状态: {CurrentState}");
                GUILayout.Label($"可移动: {CanMove}");
                GUILayout.EndArea();
            }
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (InputManagerSimple.Instance != null)
            {
                InputManagerSimple.Instance.OnMoveInputChanged -= HandleMoveInput;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 玩家方向枚举
    /// </summary>
    public enum PlayerDirection
    {
        Down = 0,
        Left = 1,
        Right = 2,
        Up = 3
    }
    
    /// <summary>
    /// 玩家状态枚举
    /// </summary>
    public enum PlayerState
    {
        Idle,       // 待机
        Moving,     // 移动中
        Stopping,   // 停止中
        Disabled    // 禁用
    }
}
