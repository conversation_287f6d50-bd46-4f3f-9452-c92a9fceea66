# Unity项目创建和设置指南

## 第一步：创建新的Unity项目

### 1. Unity版本要求
- **推荐版本**: Unity 2022.3 LTS
- **最低版本**: Unity 2021.3 LTS
- **模板**: 2D (URP) 或 2D Core

### 2. 项目创建步骤
1. 打开Unity Hub
2. 点击"新建项目"
3. 选择"2D (URP)"模板
4. 项目名称：`MazeAdventure`
5. 位置：选择合适的文件夹
6. 点击"创建项目"

### 3. 初始项目设置
```
项目设置路径：Edit > Project Settings

Player Settings:
- Company Name: MazeAdventure Studio
- Product Name: 迷宫探险
- Version: 0.1.0
- Default Icon: (稍后设置)

XR Settings:
- Virtual Reality Supported: 取消勾选

Resolution and Presentation:
- Default Orientation: Landscape Left
- Allowed Orientations for Auto Rotation:
  ✓ Landscape Left
  ✓ Landscape Right
  ✗ Portrait
  ✗ Portrait Upside Down
```

## 第二步：配置Android平台

### 1. 切换到Android平台
1. File > Build Settings
2. 选择Android
3. 点击"Switch Platform"

### 2. Android Player Settings
```
Player Settings > Android:

Identification:
- Package Name: com.mazeadventure.game
- Version: 0.1.0
- Bundle Version Code: 1
- Minimum API Level: Android 5.0 'Lollipop' (API level 21)
- Target API Level: Automatic (highest installed)

Configuration:
- Scripting Backend: IL2CPP
- Api Compatibility Level: .NET Standard 2.1
- Target Architectures: ARM64 ✓, ARMv7 ✓

Optimization:
- Managed Stripping Level: Medium
- Strip Engine Code: ✓
```

### 3. Graphics Settings
```
Project Settings > Graphics:

Scriptable Render Pipeline Settings:
- 使用Universal Render Pipeline Asset

Built-in Shader Settings:
- Always Included Shaders: 保持默认

Shader Stripping:
- Lightmap Modes: Manual
- Fog Modes: Manual
```

## 第三步：创建文件夹结构

在Assets文件夹下创建以下结构：

```
Assets/
├── Art/                    # 美术资源
│   ├── Characters/         # 角色精灵
│   ├── Environment/        # 环境贴图
│   ├── Items/             # 道具图标
│   ├── UI/                # UI元素
│   └── Effects/           # 特效资源
├── Audio/                 # 音频资源
│   ├── Music/             # 背景音乐
│   ├── SFX/               # 音效
│   └── Ambient/           # 环境音
├── Scripts/               # 代码文件
│   ├── Core/              # 核心系统
│   ├── Gameplay/          # 游戏逻辑
│   ├── UI/                # 界面系统
│   ├── Audio/             # 音频管理
│   └── Utils/             # 工具类
├── Prefabs/               # 预制体
│   ├── Characters/        # 角色预制体
│   ├── Environment/       # 环境预制体
│   ├── Items/             # 道具预制体
│   └── UI/                # UI预制体
├── Scenes/                # 场景文件
├── Materials/             # 材质文件
├── Animations/            # 动画文件
├── Resources/             # 运行时加载资源
└── Settings/              # 配置文件
```

## 第四步：导入脚本文件

### 1. 复制脚本文件
将之前创建的所有脚本文件复制到对应的文件夹：

```
Scripts/Core/:
- GameManager.cs
- GameConfig.cs
- GameData.cs
- SaveSystem.cs
- InputManager.cs
- ResolutionManager.cs

Scripts/UI/:
- VirtualJoystick.cs
- TouchInteractionManager.cs
- MobileUIManager.cs
- SafeAreaAdapter.cs
- ResponsiveUIElement.cs
```

### 2. 解决编译错误
导入后可能会有一些编译错误，这是正常的，因为还缺少一些依赖：

1. 打开Unity Console (Window > General > Console)
2. 查看错误信息
3. 暂时注释掉有错误的代码行，稍后会补充

### 3. 创建必需的ScriptableObject
1. 在Assets/Settings/文件夹下
2. 右键 > Create > MazeAdventure > Game Config
3. 命名为"GameConfig"

## 第五步：设置基础场景

### 1. 创建主场景
1. File > New Scene
2. 选择"2D (URP)"模板
3. 保存为"Assets/Scenes/GamePlay.unity"

### 2. 设置主相机
```
Main Camera设置:
- Position: (0, 0, -10)
- Projection: Orthographic
- Size: 5
- Clipping Planes: Near 0.3, Far 1000
- Culling Mask: Everything
- Rendering Path: Use Graphics Settings
```

### 3. 创建Canvas
1. 右键Hierarchy > UI > Canvas
2. 重命名为"MainCanvas"
3. Canvas设置：
   - Render Mode: Screen Space - Overlay
   - Pixel Perfect: ✓
   - Sort Order: 0

4. Canvas Scaler设置：
   - UI Scale Mode: Scale With Screen Size
   - Reference Resolution: 1920 x 1080
   - Screen Match Mode: Match Width Or Height
   - Match: 0.5

### 4. 创建EventSystem
如果没有自动创建，手动添加：
1. 右键Hierarchy > UI > Event System

## 第六步：创建管理器对象

### 1. 创建GameManager
1. 创建空GameObject，命名为"GameManager"
2. 添加GameManager脚本
3. 在Inspector中设置GameConfig引用

### 2. 创建InputManager
1. 创建空GameObject，命名为"InputManager"
2. 添加InputManager脚本

### 3. 创建ResolutionManager
1. 创建空GameObject，命名为"ResolutionManager"
2. 添加ResolutionManager脚本

### 4. 创建管理器容器
1. 创建空GameObject，命名为"Managers"
2. 将所有管理器作为子对象
3. 设置为DontDestroyOnLoad

## 第七步：配置输入系统

### 1. 安装Input System包（可选）
如果要使用新的Input System：
1. Window > Package Manager
2. 搜索"Input System"
3. 点击Install

### 2. 配置触摸输入
在Player Settings中：
- Simulate Mouse With Touches: ✓
- Default Touch Type: Finger

## 第八步：测试基础设置

### 1. 构建测试
1. File > Build Settings
2. 添加当前场景
3. 点击"Build And Run"
4. 选择输出文件夹
5. 等待构建完成

### 2. 检查项目
- [ ] 项目可以正常编译
- [ ] 可以切换到Android平台
- [ ] 基础场景设置正确
- [ ] 管理器脚本无编译错误

## 常见问题解决

### 问题1：编译错误
**解决方案**：
1. 检查命名空间是否正确
2. 确保所有依赖的类都已创建
3. 暂时注释掉有问题的代码

### 问题2：Android构建失败
**解决方案**：
1. 检查Android SDK路径
2. 更新Android Build Tools
3. 确保Java JDK版本正确

### 问题3：UI显示异常
**解决方案**：
1. 检查Canvas Scaler设置
2. 确保Reference Resolution正确
3. 验证锚点设置

## 下一步

项目设置完成后，我们将进入第二步：制作基础的像素艺术资源。

### 准备工作
1. 安装Aseprite或其他像素艺术工具
2. 准备32x32像素的画布
3. 设置调色板
4. 开始制作玩家角色精灵

### 检查清单
- [ ] Unity项目创建完成
- [ ] Android平台配置完成
- [ ] 文件夹结构创建完成
- [ ] 脚本文件导入完成
- [ ] 基础场景设置完成
- [ ] 管理器对象创建完成
- [ ] 项目可以正常构建

完成这些步骤后，我们就有了一个完整的Unity项目基础，可以开始制作游戏内容了。
