# Android构建设置指南

## 基础设置

### Player Settings
1. **Company Name**: MazeAdventure Studio
2. **Product Name**: 迷宫探险
3. **Package Name**: com.mazeadventure.game
4. **Version**: 1.0.0
5. **Bundle Version Code**: 1

### 平台设置
1. **Target API Level**: API Level 30 (Android 11)
2. **Minimum API Level**: API Level 21 (Android 5.0)
3. **Scripting Backend**: IL2CPP
4. **Target Architectures**: ARM64 (推荐) + ARMv7 (兼容性)

### 图形设置
1. **Graphics APIs**: 
   - OpenGL ES 3.2 (主要)
   - OpenGL ES 3.1 (备用)
   - OpenGL ES 3.0 (最低要求)
2. **Color Space**: Linear
3. **Multithreaded Rendering**: 启用
4. **Static Batching**: 启用
5. **Dynamic Batching**: 启用

### 渲染设置
1. **Rendering Path**: Forward
2. **Color Format**: RGB 565 (节省内存)
3. **Depth Format**: 16 bit
4. **Anti Aliasing**: 2x Multi Sampling

## 优化设置

### 内存优化
1. **Texture Compression**: 
   - ASTC 6x6 (高质量设备)
   - ETC2 (中等设备)
   - ETC (低端设备)
2. **Audio Compression**: Vorbis
3. **Mesh Compression**: Medium
4. **Animation Compression**: Keyframe Reduction

### 性能优化
1. **Stripping Level**: Medium
2. **Script Call Optimization**: Fast but no Exceptions
3. **Vertex Compression**: Mixed
4. **Optimize Mesh Data**: 启用

### 电池优化
1. **Target Frame Rate**: 60 FPS
2. **V-Sync**: 启用
3. **Run In Background**: 禁用
4. **Sustained Performance Mode**: 启用

## 权限设置

### 必需权限
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### 可选权限
```xml
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

## 构建配置

### Development Build
- **Development Build**: 启用
- **Script Debugging**: 启用
- **Deep Profiling**: 禁用
- **Autoconnect Profiler**: 启用

### Release Build
- **Development Build**: 禁用
- **Script Debugging**: 禁用
- **Create symbols.zip**: 启用（用于崩溃分析）

## 签名设置

### Debug签名
使用Unity默认的debug.keystore

### Release签名
1. 创建release.keystore
2. 设置密钥别名
3. 配置密码
4. 启用"Use Custom Keystore"

## 图标和启动画面

### 应用图标
- **Adaptive Icon**: 启用
- **Legacy Icon**: 提供多种尺寸
- **Round Icon**: 提供圆形图标

### 启动画面
- **Background**: 纯黑色
- **Logo**: 游戏Logo
- **Scaling**: Center
- **Animation**: 淡入效果

## 质量设置

### 低端设备 (Quality Level 0)
- **Pixel Light Count**: 1
- **Texture Quality**: Half Res
- **Anisotropic Textures**: Disabled
- **Anti Aliasing**: Disabled
- **Soft Particles**: Disabled
- **Shadows**: Disabled

### 中端设备 (Quality Level 1)
- **Pixel Light Count**: 2
- **Texture Quality**: Full Res
- **Anisotropic Textures**: Per Texture
- **Anti Aliasing**: 2x Multi Sampling
- **Soft Particles**: Enabled
- **Shadows**: Hard Shadows Only

### 高端设备 (Quality Level 2)
- **Pixel Light Count**: 4
- **Texture Quality**: Full Res
- **Anisotropic Textures**: Forced On
- **Anti Aliasing**: 4x Multi Sampling
- **Soft Particles**: Enabled
- **Shadows**: All

## 输入设置

### 触摸输入
- **Simulate Mouse With Touches**: 启用
- **Default Touch Type**: Finger
- **Multi Touch**: 启用

### 传感器
- **Accelerometer Frequency**: 60 Hz
- **Gyroscope**: 启用（如果需要）

## 音频设置

### 音频配置
- **DSP Buffer Size**: Best Performance
- **Sample Rate**: 44100 Hz
- **Speaker Mode**: Stereo
- **Disable Unity Audio**: 禁用

### 音频压缩
- **Background Music**: Vorbis, Quality 70
- **Sound Effects**: PCM (短音效) / Vorbis (长音效)
- **Ambient Sounds**: Vorbis, Quality 50

## 网络设置

### 网络配置
- **Internet Reachability**: Via Carrier Data Network
- **Use HTTP**: 启用
- **Use HTTPS**: 启用

## 构建后处理

### APK优化
1. **Zipalign**: 自动执行
2. **Proguard**: 启用（Release构建）
3. **Split APKs by target architecture**: 考虑启用

### 测试检查清单
- [ ] 在不同分辨率设备上测试
- [ ] 检查内存使用情况
- [ ] 验证触摸输入响应
- [ ] 测试音频播放
- [ ] 检查电池消耗
- [ ] 验证存档功能
- [ ] 测试网络连接（如果有）

## 发布准备

### Google Play Console
1. 上传APK或AAB
2. 填写应用信息
3. 设置内容分级
4. 配置定价和分发
5. 提交审核

### 应用商店资源
- 应用截图（至少2张）
- 功能图片
- 应用描述
- 简短描述
- 隐私政策链接

## 常见问题

### 构建失败
1. 检查Android SDK路径
2. 更新Build Tools
3. 清理项目缓存

### 性能问题
1. 使用Profiler分析
2. 检查Draw Calls
3. 优化纹理大小

### 兼容性问题
1. 测试不同API级别
2. 检查硬件要求
3. 验证权限使用
