<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>迷宫探险</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        
        #gameContainer {
            position: relative;
            border: 2px solid #333;
        }
        
        #gameCanvas {
            display: block;
            background: #111;
        }
        
        #ui {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
        }
        
        .ui-bar {
            margin-bottom: 10px;
        }
        
        .bar-fill {
            height: 8px;
            background: #333;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 2px;
        }
        
        .battery-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }
        
        .health-fill {
            height: 100%;
            background: linear-gradient(90deg, #F44336, #FF9800);
            transition: width 0.3s ease;
        }
        
        #countdown {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ff4444;
            font-size: 48px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            display: none;
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        #gameOver {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-align: center;
            display: none;
        }
        
        #restartBtn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            margin-top: 10px;
        }
        
        #restartBtn:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div id="ui">
            <div class="ui-bar">
                <div>电量: <span id="batteryText">100%</span></div>
                <div class="bar-fill">
                    <div class="battery-fill" id="batteryBar" style="width: 100%"></div>
                </div>
            </div>
            <div class="ui-bar">
                <div>生命: <span id="healthText">100%</span></div>
                <div class="bar-fill">
                    <div class="health-fill" id="healthBar" style="width: 100%"></div>
                </div>
            </div>
        </div>
        
        <div id="countdown"></div>
        <div id="gameOver">
            <div>游戏结束</div>
            <button id="restartBtn">重新开始</button>
        </div>
    </div>

    <script>
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');
        
        // 游戏状态
        const game = {
            width: canvas.width,
            height: canvas.height,
            isRunning: true,
            isPaused: false
        };
        
        // 玩家对象
        const player = {
            x: 100,
            y: 100,
            width: 20,
            height: 20,
            speed: 3,
            health: 100,
            maxHealth: 100,
            battery: 100,
            maxBattery: 100,
            batteryDrainRate: 0.05,
            flashlightRadius: 150,
            flashlightAngle: Math.PI / 3,
            direction: 0
        };
        
        // 输入状态
        const keys = {};
        
        // 迷宫地图 (1=墙, 0=路)
        const maze = [
            [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1],
            [1,0,0,0,0,0,0,0,0,1,0,0,0,0,0,1],
            [1,0,1,1,0,1,1,1,0,1,0,1,1,1,0,1],
            [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
            [1,1,1,0,1,1,1,0,1,1,0,1,1,0,1,1],
            [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
            [1,0,1,1,0,1,0,1,1,1,0,1,0,1,1,1],
            [1,0,0,0,0,1,0,0,0,0,0,1,0,0,0,1],
            [1,1,1,1,0,1,1,1,0,1,1,1,0,1,0,1],
            [1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1],
            [1,0,1,1,1,1,0,1,1,1,1,1,0,1,0,1],
            [1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1],
            [1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1]
        ];
        
        const cellSize = 50;
        
        // 道具数组
        const items = [];
        
        // 机关数组
        const traps = [];
        
        // 初始化游戏
        function init() {
            // 生成电池
            items.push(
                { x: 200, y: 200, type: 'battery', width: 15, height: 15 },
                { x: 500, y: 300, type: 'battery', width: 15, height: 15 },
                { x: 650, y: 450, type: 'battery', width: 15, height: 15 }
            );
            
            // 生成机关
            traps.push(
                { x: 300, y: 150, type: 'spike', width: 20, height: 20, damage: 20 },
                { x: 400, y: 350, type: 'spike', width: 20, height: 20, damage: 20 },
                { x: 600, y: 200, type: 'spike', width: 20, height: 20, damage: 20 }
            );
            
            gameLoop();
        }
        
        // 更新游戏逻辑
        function update() {
            if (!game.isRunning) return;
            
            // 玩家移动
            if (keys['ArrowUp'] || keys['w']) {
                player.y -= player.speed;
            }
            if (keys['ArrowDown'] || keys['s']) {
                player.y += player.speed;
            }
            if (keys['ArrowLeft'] || keys['a']) {
                player.x -= player.speed;
            }
            if (keys['ArrowRight'] || keys['d']) {
                player.x += player.speed;
            }
            
            // 边界检查
            player.x = Math.max(player.width/2, Math.min(game.width - player.width/2, player.x));
            player.y = Math.max(player.height/2, Math.min(game.height - player.height/2, player.y));
            
            // 墙壁碰撞检测
            checkWallCollision();
            
            // 电量消耗
            player.battery -= player.batteryDrainRate;
            player.battery = Math.max(0, player.battery);
            
            // 更新头灯范围
            if (player.battery > 0) {
                player.flashlightRadius = 150 * (player.battery / player.maxBattery);
            } else {
                player.flashlightRadius = 30;
            }
            
            // 检查道具拾取
            checkItemPickup();
            
            // 检查机关碰撞
            checkTrapCollision();
            
            // 更新UI
            updateUI();
            
            // 检查游戏结束
            if (player.health <= 0) {
                gameOver();
            }
        }
        
        // 墙壁碰撞检测
        function checkWallCollision() {
            const mazeX = Math.floor(player.x / cellSize);
            const mazeY = Math.floor(player.y / cellSize);
            
            if (mazeX >= 0 && mazeX < maze[0].length && mazeY >= 0 && mazeY < maze.length) {
                if (maze[mazeY][mazeX] === 1) {
                    // 简单的推开机制
                    if (keys['ArrowUp'] || keys['w']) player.y += player.speed;
                    if (keys['ArrowDown'] || keys['s']) player.y -= player.speed;
                    if (keys['ArrowLeft'] || keys['a']) player.x += player.speed;
                    if (keys['ArrowRight'] || keys['d']) player.x -= player.speed;
                }
            }
        }
        
        // 检查道具拾取
        function checkItemPickup() {
            for (let i = items.length - 1; i >= 0; i--) {
                const item = items[i];
                if (Math.abs(player.x - item.x) < (player.width + item.width) / 2 &&
                    Math.abs(player.y - item.y) < (player.height + item.height) / 2) {
                    
                    if (item.type === 'battery') {
                        player.battery = Math.min(player.maxBattery, player.battery + 30);
                        items.splice(i, 1);
                    }
                }
            }
        }
        
        // 检查机关碰撞
        function checkTrapCollision() {
            for (const trap of traps) {
                if (Math.abs(player.x - trap.x) < (player.width + trap.width) / 2 &&
                    Math.abs(player.y - trap.y) < (player.height + trap.height) / 2) {
                    
                    player.health -= trap.damage * 0.01;
                    player.health = Math.max(0, player.health);
                }
            }
        }
        
        // 渲染游戏画面
        function render() {
            // 清空画布
            ctx.fillStyle = '#000';
            ctx.fillRect(0, 0, game.width, game.height);
            
            // 绘制迷宫
            ctx.fillStyle = '#333';
            for (let y = 0; y < maze.length; y++) {
                for (let x = 0; x < maze[y].length; x++) {
                    if (maze[y][x] === 1) {
                        ctx.fillRect(x * cellSize, y * cellSize, cellSize, cellSize);
                    }
                }
            }
            
            // 绘制道具
            ctx.fillStyle = '#4CAF50';
            for (const item of items) {
                if (item.type === 'battery') {
                    ctx.fillRect(item.x - item.width/2, item.y - item.height/2, item.width, item.height);
                }
            }
            
            // 绘制机关
            ctx.fillStyle = '#F44336';
            for (const trap of traps) {
                ctx.fillRect(trap.x - trap.width/2, trap.y - trap.height/2, trap.width, trap.height);
            }
            
            // 创建头灯效果
            if (player.battery > 0) {
                // 创建径向渐变
                const gradient = ctx.createRadialGradient(
                    player.x, player.y, 0,
                    player.x, player.y, player.flashlightRadius
                );
                gradient.addColorStop(0, 'rgba(255, 255, 200, 0.8)');
                gradient.addColorStop(0.7, 'rgba(255, 255, 150, 0.3)');
                gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
                
                // 绘制头灯光束
                ctx.save();
                ctx.globalCompositeOperation = 'lighter';
                ctx.fillStyle = gradient;
                ctx.fillRect(0, 0, game.width, game.height);
                ctx.restore();
            }
            
            // 绘制玩家
            ctx.fillStyle = '#00BCD4';
            ctx.fillRect(player.x - player.width/2, player.y - player.height/2, player.width, player.height);
            
            // 如果没有电，绘制微弱视野
            if (player.battery <= 0) {
                const weakGradient = ctx.createRadialGradient(
                    player.x, player.y, 0,
                    player.x, player.y, 30
                );
                weakGradient.addColorStop(0, 'rgba(100, 100, 100, 0.2)');
                weakGradient.addColorStop(1, 'rgba(0, 0, 0, 0)');
                
                ctx.save();
                ctx.globalCompositeOperation = 'lighter';
                ctx.fillStyle = weakGradient;
                ctx.fillRect(0, 0, game.width, game.height);
                ctx.restore();
            }
        }
        
        // 更新UI
        function updateUI() {
            document.getElementById('batteryText').textContent = Math.round(player.battery) + '%';
            document.getElementById('batteryBar').style.width = player.battery + '%';
            
            document.getElementById('healthText').textContent = Math.round(player.health) + '%';
            document.getElementById('healthBar').style.width = player.health + '%';
            
            // 电量警告
            if (player.battery > 0 && player.battery <= 10) {
                const countdown = document.getElementById('countdown');
                countdown.style.display = 'block';
                countdown.textContent = Math.ceil(player.battery / player.batteryDrainRate / 60);
            } else {
                document.getElementById('countdown').style.display = 'none';
            }
        }
        
        // 游戏结束
        function gameOver() {
            game.isRunning = false;
            document.getElementById('gameOver').style.display = 'block';
        }
        
        // 重新开始游戏
        function restart() {
            player.x = 100;
            player.y = 100;
            player.health = player.maxHealth;
            player.battery = player.maxBattery;
            game.isRunning = true;
            document.getElementById('gameOver').style.display = 'none';
            
            // 重新生成道具
            items.length = 0;
            items.push(
                { x: 200, y: 200, type: 'battery', width: 15, height: 15 },
                { x: 500, y: 300, type: 'battery', width: 15, height: 15 },
                { x: 650, y: 450, type: 'battery', width: 15, height: 15 }
            );
        }
        
        // 游戏主循环
        function gameLoop() {
            update();
            render();
            requestAnimationFrame(gameLoop);
        }
        
        // 事件监听
        document.addEventListener('keydown', (e) => {
            keys[e.key] = true;
        });
        
        document.addEventListener('keyup', (e) => {
            keys[e.key] = false;
        });
        
        document.getElementById('restartBtn').addEventListener('click', restart);
        
        // 启动游戏
        init();
    </script>
</body>
</html>