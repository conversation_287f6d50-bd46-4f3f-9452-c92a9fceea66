using UnityEngine;
using System.Collections;
using MazeAdventure.Core;
using MazeAdventure.UI;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 游戏状态管理器 - 管理游戏的各种状态和转换
    /// </summary>
    public class GameStateManager : MonoBehaviour
    {
        [Header("游戏设置")]
        [SerializeField] private float gameTimeLimit = 300f; // 5分钟
        [SerializeField] private bool enableTimeLimit = true;
        [SerializeField] private float pauseTransitionTime = 0.2f;
        
        [Header("胜利条件")]
        [SerializeField] private bool requireAllBatteries = false;
        [SerializeField] private int minimumBatteriesForWin = 5;
        
        [Header("失败条件")]
        [SerializeField] private bool gameOverOnZeroBattery = true;
        [SerializeField] private bool gameOverOnZeroHealth = true;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool enableCheatKeys = false;
        
        // 单例模式
        public static GameStateManager Instance { get; private set; }
        
        // 游戏状态
        public GameState CurrentState { get; private set; }
        public float GameTime { get; private set; }
        public float RemainingTime => Mathf.Max(0f, gameTimeLimit - GameTime);
        public bool IsGameActive => CurrentState == GameState.Playing;
        public bool IsGamePaused => CurrentState == GameState.Paused;
        
        // 游戏统计
        public int BatteriesCollected { get; private set; }
        public int HealthPacksCollected { get; private set; }
        public int DeathCount { get; private set; }
        public float BestTime { get; private set; }
        
        // 事件系统
        public System.Action<GameState, GameState> OnGameStateChanged;
        public System.Action<float> OnGameTimeChanged;
        public System.Action OnGameStarted;
        public System.Action OnGamePaused;
        public System.Action OnGameResumed;
        public System.Action<GameEndReason> OnGameEnded;
        public System.Action OnGameWon;
        public System.Action OnGameLost;
        
        // 私有变量
        private GameState previousState;
        private bool isTransitioning = false;
        private Coroutine gameTimeCoroutine;
        private float pauseStartTime;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeGameStateManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupGameStateManager();
        }
        
        private void Update()
        {
            HandleInput();
            CheckGameConditions();
            
            if (showDebugInfo)
            {
                UpdateDebugInfo();
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeGameStateManager()
        {
            CurrentState = GameState.Menu;
            previousState = GameState.Menu;
            GameTime = 0f;
            
            // 从存档加载最佳时间
            BestTime = PlayerPrefs.GetFloat("BestTime", float.MaxValue);
        }
        
        private void SetupGameStateManager()
        {
            // 订阅相关事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnPlayerDied += HandlePlayerDeath;
                PlayerData.Instance.OnBatteryEmpty += HandleBatteryEmpty;
            }
            
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnItemCollected += HandleItemCollected;
            }
            
            // 从游戏配置加载设置
            LoadGameSettings();
        }
        
        private void LoadGameSettings()
        {
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                gameTimeLimit = config.gameTimeLimit;
                enableTimeLimit = config.enableTimeLimit;
                minimumBatteriesForWin = config.minimumBatteriesForWin;
            }
        }
        
        #endregion
        
        #region 状态管理
        
        /// <summary>
        /// 改变游戏状态
        /// </summary>
        public void ChangeState(GameState newState)
        {
            if (isTransitioning || CurrentState == newState) return;
            
            StartCoroutine(TransitionToState(newState));
        }
        
        private IEnumerator TransitionToState(GameState newState)
        {
            isTransitioning = true;
            
            // 退出当前状态
            yield return StartCoroutine(ExitCurrentState());
            
            // 更新状态
            previousState = CurrentState;
            CurrentState = newState;
            
            // 进入新状态
            yield return StartCoroutine(EnterNewState(newState));
            
            // 触发状态改变事件
            OnGameStateChanged?.Invoke(previousState, CurrentState);
            
            isTransitioning = false;
            
            if (showDebugInfo)
            {
                Debug.Log($"游戏状态改变: {previousState} -> {CurrentState}");
            }
        }
        
        private IEnumerator ExitCurrentState()
        {
            switch (CurrentState)
            {
                case GameState.Playing:
                    // 停止游戏时间计时
                    if (gameTimeCoroutine != null)
                    {
                        StopCoroutine(gameTimeCoroutine);
                        gameTimeCoroutine = null;
                    }
                    break;
                    
                case GameState.Paused:
                    // 恢复时间缩放
                    Time.timeScale = 1f;
                    break;
            }
            
            yield return null;
        }
        
        private IEnumerator EnterNewState(GameState newState)
        {
            switch (newState)
            {
                case GameState.Menu:
                    yield return StartCoroutine(EnterMenuState());
                    break;
                    
                case GameState.Playing:
                    yield return StartCoroutine(EnterPlayingState());
                    break;
                    
                case GameState.Paused:
                    yield return StartCoroutine(EnterPausedState());
                    break;
                    
                case GameState.GameOver:
                    yield return StartCoroutine(EnterGameOverState());
                    break;
                    
                case GameState.Victory:
                    yield return StartCoroutine(EnterVictoryState());
                    break;
            }
        }
        
        #endregion
        
        #region 状态处理
        
        private IEnumerator EnterMenuState()
        {
            // 重置游戏数据
            ResetGameData();
            
            // 显示主菜单UI
            if (MobileUIManager.Instance != null)
            {
                // 这里应该显示主菜单，暂时跳过
            }
            
            yield return null;
        }
        
        private IEnumerator EnterPlayingState()
        {
            // 确保时间缩放正常
            Time.timeScale = 1f;
            
            // 启用玩家控制
            if (PlayerController.Instance != null)
            {
                PlayerController.Instance.SetCanMove(true);
            }
            
            // 开始计时
            if (enableTimeLimit)
            {
                gameTimeCoroutine = StartCoroutine(GameTimeCoroutine());
            }
            
            // 显示游戏UI
            if (MobileUIManager.Instance != null)
            {
                MobileUIManager.Instance.ShowGameplayUI();
            }
            
            // 触发游戏开始事件
            OnGameStarted?.Invoke();
            
            yield return null;
        }
        
        private IEnumerator EnterPausedState()
        {
            pauseStartTime = Time.realtimeSinceStartup;
            
            // 暂停时间
            Time.timeScale = 0f;
            
            // 禁用玩家控制
            if (PlayerController.Instance != null)
            {
                PlayerController.Instance.SetCanMove(false);
            }
            
            // 显示暂停UI
            if (MobileUIManager.Instance != null)
            {
                MobileUIManager.Instance.ShowPauseMenu();
            }
            
            // 触发暂停事件
            OnGamePaused?.Invoke();
            
            yield return new WaitForSecondsRealtime(pauseTransitionTime);
        }
        
        private IEnumerator EnterGameOverState()
        {
            // 禁用玩家控制
            if (PlayerController.Instance != null)
            {
                PlayerController.Instance.SetCanMove(false);
            }
            
            // 显示游戏结束UI
            if (MobileUIManager.Instance != null)
            {
                MobileUIManager.Instance.ShowGameOverScreen();
            }
            
            // 触发游戏失败事件
            OnGameLost?.Invoke();
            OnGameEnded?.Invoke(GameEndReason.Failed);
            
            // 保存统计数据
            SaveGameStats();
            
            yield return null;
        }
        
        private IEnumerator EnterVictoryState()
        {
            // 禁用玩家控制
            if (PlayerController.Instance != null)
            {
                PlayerController.Instance.SetCanMove(false);
            }
            
            // 检查是否创造了新纪录
            if (GameTime < BestTime)
            {
                BestTime = GameTime;
                PlayerPrefs.SetFloat("BestTime", BestTime);
                PlayerPrefs.Save();
            }
            
            // 显示胜利UI
            if (MobileUIManager.Instance != null)
            {
                MobileUIManager.Instance.ShowVictoryScreen();
            }
            
            // 触发胜利事件
            OnGameWon?.Invoke();
            OnGameEnded?.Invoke(GameEndReason.Victory);
            
            // 保存统计数据
            SaveGameStats();
            
            yield return null;
        }
        
        #endregion
        
        #region 游戏逻辑
        
        /// <summary>
        /// 开始新游戏
        /// </summary>
        public void StartNewGame()
        {
            ResetGameData();
            ChangeState(GameState.Playing);
        }
        
        /// <summary>
        /// 暂停游戏
        /// </summary>
        public void PauseGame()
        {
            if (CurrentState == GameState.Playing)
            {
                ChangeState(GameState.Paused);
            }
        }
        
        /// <summary>
        /// 恢复游戏
        /// </summary>
        public void ResumeGame()
        {
            if (CurrentState == GameState.Paused)
            {
                ChangeState(GameState.Playing);
                OnGameResumed?.Invoke();
            }
        }
        
        /// <summary>
        /// 重新开始游戏
        /// </summary>
        public void RestartGame()
        {
            StartNewGame();
        }
        
        /// <summary>
        /// 退出到主菜单
        /// </summary>
        public void ExitToMenu()
        {
            ChangeState(GameState.Menu);
        }
        
        private void ResetGameData()
        {
            GameTime = 0f;
            BatteriesCollected = 0;
            HealthPacksCollected = 0;
            DeathCount = 0;
            
            // 重置玩家数据
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.ResetToDefault();
            }
        }
        
        private IEnumerator GameTimeCoroutine()
        {
            while (CurrentState == GameState.Playing)
            {
                GameTime += Time.deltaTime;
                OnGameTimeChanged?.Invoke(GameTime);
                
                // 检查时间限制
                if (enableTimeLimit && GameTime >= gameTimeLimit)
                {
                    HandleTimeUp();
                    break;
                }
                
                yield return null;
            }
        }
        
        #endregion
        
        #region 游戏条件检查
        
        private void CheckGameConditions()
        {
            if (CurrentState != GameState.Playing) return;
            
            // 检查胜利条件
            if (CheckVictoryConditions())
            {
                ChangeState(GameState.Victory);
                return;
            }
            
            // 检查失败条件
            if (CheckFailureConditions())
            {
                ChangeState(GameState.GameOver);
                return;
            }
        }
        
        private bool CheckVictoryConditions()
        {
            // 检查玩家是否到达终点
            if (PlayerController.Instance != null && MazeGenerator.Instance != null)
            {
                Vector2Int playerGridPos = PlayerController.Instance.GetGridPosition();
                Vector2Int endPos = MazeGenerator.Instance.EndPosition;
                
                if (playerGridPos == endPos)
                {
                    // 检查是否收集了足够的电池
                    if (requireAllBatteries)
                    {
                        int totalBatteries = ItemSystem.Instance?.batteryCount ?? 0;
                        return BatteriesCollected >= totalBatteries;
                    }
                    else
                    {
                        return BatteriesCollected >= minimumBatteriesForWin;
                    }
                }
            }
            
            return false;
        }
        
        private bool CheckFailureConditions()
        {
            if (PlayerData.Instance == null) return false;
            
            // 检查生命值
            if (gameOverOnZeroHealth && !PlayerData.Instance.IsAlive)
            {
                return true;
            }
            
            // 检查电量
            if (gameOverOnZeroBattery && PlayerData.Instance.Battery <= 0f)
            {
                return true;
            }
            
            return false;
        }
        
        #endregion
        
        #region 事件处理
        
        private void HandlePlayerDeath()
        {
            DeathCount++;
            
            if (gameOverOnZeroHealth)
            {
                ChangeState(GameState.GameOver);
            }
        }
        
        private void HandleBatteryEmpty()
        {
            if (gameOverOnZeroBattery)
            {
                ChangeState(GameState.GameOver);
            }
        }
        
        private void HandleTimeUp()
        {
            ChangeState(GameState.GameOver);
        }
        
        private void HandleItemCollected(ItemType itemType, Vector2 position)
        {
            switch (itemType)
            {
                case ItemType.Battery:
                    BatteriesCollected++;
                    break;
                case ItemType.HealthPack:
                    HealthPacksCollected++;
                    break;
            }
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInput()
        {
            if (enableCheatKeys && Application.isEditor)
            {
                // 调试快捷键
                if (Input.GetKeyDown(KeyCode.F1))
                {
                    StartNewGame();
                }
                else if (Input.GetKeyDown(KeyCode.F2))
                {
                    ChangeState(GameState.Victory);
                }
                else if (Input.GetKeyDown(KeyCode.F3))
                {
                    ChangeState(GameState.GameOver);
                }
            }
            
            // ESC键暂停/恢复
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (CurrentState == GameState.Playing)
                {
                    PauseGame();
                }
                else if (CurrentState == GameState.Paused)
                {
                    ResumeGame();
                }
            }
        }
        
        #endregion
        
        #region 数据保存
        
        private void SaveGameStats()
        {
            GameData gameData = GameManager.Instance?.GetGameData();
            if (gameData != null)
            {
                gameData.RecordGameSession(GameTime, BatteriesCollected, HealthPacksCollected, DeathCount);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取游戏统计信息
        /// </summary>
        public string GetGameStats()
        {
            return $"游戏时间: {GameTime:F1}s\n" +
                   $"剩余时间: {RemainingTime:F1}s\n" +
                   $"电池收集: {BatteriesCollected}\n" +
                   $"医疗包收集: {HealthPacksCollected}\n" +
                   $"死亡次数: {DeathCount}\n" +
                   $"最佳时间: {(BestTime < float.MaxValue ? BestTime.ToString("F1") + "s" : "无")}";
        }
        
        /// <summary>
        /// 检查是否可以暂停
        /// </summary>
        public bool CanPause()
        {
            return CurrentState == GameState.Playing && !isTransitioning;
        }
        
        /// <summary>
        /// 检查是否可以恢复
        /// </summary>
        public bool CanResume()
        {
            return CurrentState == GameState.Paused && !isTransitioning;
        }
        
        #endregion
        
        #region 调试
        
        private void UpdateDebugInfo()
        {
            // 这里可以更新调试信息显示
        }
        
        private void OnGUI()
        {
            if (showDebugInfo && Application.isPlaying)
            {
                GUILayout.BeginArea(new Rect(10, 100, 300, 200));
                GUILayout.Label($"游戏状态: {CurrentState}");
                GUILayout.Label($"游戏时间: {GameTime:F1}s");
                GUILayout.Label($"剩余时间: {RemainingTime:F1}s");
                GUILayout.Label($"电池收集: {BatteriesCollected}");
                GUILayout.Label($"死亡次数: {DeathCount}");
                
                if (enableCheatKeys)
                {
                    GUILayout.Label("F1: 开始游戏");
                    GUILayout.Label("F2: 胜利");
                    GUILayout.Label("F3: 失败");
                }
                
                GUILayout.EndArea();
            }
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnPlayerDied -= HandlePlayerDeath;
                PlayerData.Instance.OnBatteryEmpty -= HandleBatteryEmpty;
            }
            
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnItemCollected -= HandleItemCollected;
            }
            
            // 恢复时间缩放
            Time.timeScale = 1f;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 游戏结束原因枚举
    /// </summary>
    public enum GameEndReason
    {
        Victory,        // 胜利
        Failed,         // 失败
        TimeUp,         // 时间到
        PlayerDeath,    // 玩家死亡
        BatteryEmpty,   // 电量耗尽
        UserQuit        // 用户退出
    }
}
