using UnityEngine;
using UnityEngine.Rendering.Universal;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 头灯系统 - 管理玩家头灯的光照效果
    /// </summary>
    public class HeadlampSystem : MonoBehaviour
    {
        [Header("头灯设置")]
        [SerializeField] private Light2D headlampLight;
        [SerializeField] private float maxIntensity = 1.5f;
        [SerializeField] private float minIntensity = 0.3f;
        [SerializeField] private float maxRange = 8f;
        [SerializeField] private float minRange = 3f;
        [SerializeField] private float coneAngle = 60f;
        
        [Header("电量消耗")]
        [SerializeField] private float batteryDrainRate = 2f; // 每秒消耗
        [SerializeField] private float lowBatteryThreshold = 20f;
        [SerializeField] private bool enableBatteryDrain = true;
        
        [Header("头灯行为")]
        [SerializeField] private float rotationSpeed = 5f;
        [SerializeField] private bool smoothRotation = true;
        [SerializeField] private float flickerIntensity = 0.1f;
        [SerializeField] private float flickerSpeed = 10f;
        
        [Header("视觉效果")]
        [SerializeField] private Color normalColor = Color.white;
        [SerializeField] private Color lowBatteryColor = Color.yellow;
        [SerializeField] private Color criticalBatteryColor = Color.red;
        [SerializeField] private Gradient intensityGradient;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugRays = false;
        [SerializeField] private bool enableManualControl = false;
        
        // 单例模式
        public static HeadlampSystem Instance { get; private set; }
        
        // 状态属性
        public bool IsOn { get; private set; } = true;
        public float CurrentIntensity { get; private set; }
        public float CurrentRange { get; private set; }
        public Vector2 LightDirection { get; private set; }
        
        // 组件引用
        private PlayerController playerController;
        private PlayerData playerData;
        private Transform playerTransform;
        
        // 私有变量
        private float targetRotation;
        private float currentRotation;
        private float baseIntensity;
        private float flickerTimer;
        private bool isFlickering;
        
        // 事件系统
        public System.Action<bool> OnHeadlampToggled;
        public System.Action<float> OnIntensityChanged;
        public System.Action OnLowBattery;
        public System.Action OnBatteryDepleted;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeHeadlamp();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupHeadlamp();
        }
        
        private void Update()
        {
            if (!IsOn) return;
            
            UpdateHeadlampDirection();
            UpdateHeadlampIntensity();
            UpdateBatteryConsumption();
            UpdateVisualEffects();
            
            if (enableManualControl)
            {
                HandleManualInput();
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeHeadlamp()
        {
            // 创建头灯光源（如果不存在）
            if (headlampLight == null)
            {
                CreateHeadlampLight();
            }
            
            // 初始化渐变色
            if (intensityGradient == null)
            {
                CreateDefaultGradient();
            }
            
            // 设置初始值
            baseIntensity = maxIntensity;
            CurrentIntensity = baseIntensity;
            CurrentRange = maxRange;
            currentRotation = 0f;
            targetRotation = 0f;
        }
        
        private void SetupHeadlamp()
        {
            // 获取玩家组件引用
            playerController = PlayerController.Instance;
            playerData = PlayerData.Instance;
            
            if (playerController != null)
            {
                playerTransform = playerController.transform;
                
                // 订阅玩家方向改变事件
                playerController.OnDirectionChanged += HandlePlayerDirectionChanged;
            }
            
            if (playerData != null)
            {
                // 订阅电量变化事件
                playerData.OnBatteryChanged += HandleBatteryChanged;
            }
            
            // 配置头灯光源
            ConfigureHeadlampLight();
            
            Debug.Log("头灯系统初始化完成");
        }
        
        private void CreateHeadlampLight()
        {
            GameObject lightObj = new GameObject("HeadlampLight");
            lightObj.transform.SetParent(transform);
            lightObj.transform.localPosition = Vector3.zero;
            
            headlampLight = lightObj.AddComponent<Light2D>();
            headlampLight.lightType = Light2D.LightType.Spot;
        }
        
        private void ConfigureHeadlampLight()
        {
            if (headlampLight == null) return;
            
            headlampLight.intensity = CurrentIntensity;
            headlampLight.pointLightOuterRadius = CurrentRange;
            headlampLight.spotAngle = coneAngle;
            headlampLight.color = normalColor;
            headlampLight.lightOrder = 1; // 确保在其他光源之上
            
            // 设置光照层
            headlampLight.targetSortingLayers = new SortingLayer[] { SortingLayer.GetSortingLayer(0) };
        }
        
        private void CreateDefaultGradient()
        {
            intensityGradient = new Gradient();
            GradientColorKey[] colorKeys = new GradientColorKey[3];
            GradientAlphaKey[] alphaKeys = new GradientAlphaKey[3];
            
            // 颜色键：绿色(满电) -> 黄色(低电) -> 红色(极低)
            colorKeys[0] = new GradientColorKey(Color.green, 0f);
            colorKeys[1] = new GradientColorKey(Color.yellow, 0.3f);
            colorKeys[2] = new GradientColorKey(Color.red, 1f);
            
            // 透明度键
            alphaKeys[0] = new GradientAlphaKey(1f, 0f);
            alphaKeys[1] = new GradientAlphaKey(1f, 0.5f);
            alphaKeys[2] = new GradientAlphaKey(1f, 1f);
            
            intensityGradient.SetKeys(colorKeys, alphaKeys);
        }
        
        #endregion
        
        #region 头灯方向控制
        
        private void UpdateHeadlampDirection()
        {
            if (playerController == null || headlampLight == null) return;
            
            // 根据玩家朝向计算目标旋转角度
            PlayerDirection direction = playerController.CurrentDirection;
            targetRotation = GetRotationFromDirection(direction);
            
            // 平滑旋转到目标角度
            if (smoothRotation)
            {
                currentRotation = Mathf.LerpAngle(currentRotation, targetRotation, rotationSpeed * Time.deltaTime);
            }
            else
            {
                currentRotation = targetRotation;
            }
            
            // 应用旋转
            headlampLight.transform.rotation = Quaternion.Euler(0, 0, currentRotation);
            
            // 更新光照方向向量
            float radians = currentRotation * Mathf.Deg2Rad;
            LightDirection = new Vector2(Mathf.Cos(radians), Mathf.Sin(radians));
        }
        
        private float GetRotationFromDirection(PlayerDirection direction)
        {
            switch (direction)
            {
                case PlayerDirection.Right: return 0f;
                case PlayerDirection.Up: return 90f;
                case PlayerDirection.Left: return 180f;
                case PlayerDirection.Down: return 270f;
                default: return 0f;
            }
        }
        
        private void HandlePlayerDirectionChanged(PlayerDirection newDirection)
        {
            // 玩家方向改变时，头灯方向也会在Update中自动更新
            if (showDebugRays)
            {
                Debug.Log($"玩家朝向改变: {newDirection}，头灯跟随转向");
            }
        }
        
        #endregion
        
        #region 头灯强度控制
        
        private void UpdateHeadlampIntensity()
        {
            if (playerData == null || headlampLight == null) return;
            
            float batteryPercentage = playerData.BatteryPercentage;
            
            // 根据电量计算基础强度
            float intensityMultiplier = Mathf.Lerp(minIntensity / maxIntensity, 1f, batteryPercentage);
            baseIntensity = maxIntensity * intensityMultiplier;
            
            // 根据电量计算光照范围
            CurrentRange = Mathf.Lerp(minRange, maxRange, batteryPercentage);
            
            // 应用闪烁效果（低电量时）
            float finalIntensity = baseIntensity;
            if (batteryPercentage < lowBatteryThreshold / 100f)
            {
                flickerTimer += Time.deltaTime * flickerSpeed;
                float flicker = Mathf.Sin(flickerTimer) * flickerIntensity;
                finalIntensity += flicker;
                isFlickering = true;
            }
            else
            {
                isFlickering = false;
            }
            
            CurrentIntensity = Mathf.Max(0f, finalIntensity);
            
            // 应用到光源
            headlampLight.intensity = CurrentIntensity;
            headlampLight.pointLightOuterRadius = CurrentRange;
            
            // 触发强度变化事件
            OnIntensityChanged?.Invoke(CurrentIntensity);
        }
        
        private void UpdateVisualEffects()
        {
            if (playerData == null || headlampLight == null) return;
            
            float batteryPercentage = playerData.BatteryPercentage;
            
            // 根据电量改变光照颜色
            Color targetColor;
            if (batteryPercentage > 0.5f)
            {
                targetColor = normalColor;
            }
            else if (batteryPercentage > 0.2f)
            {
                targetColor = lowBatteryColor;
            }
            else
            {
                targetColor = criticalBatteryColor;
            }
            
            // 使用渐变色
            if (intensityGradient != null)
            {
                float gradientTime = 1f - batteryPercentage;
                targetColor = intensityGradient.Evaluate(gradientTime);
            }
            
            headlampLight.color = targetColor;
        }
        
        #endregion
        
        #region 电量消耗
        
        private void UpdateBatteryConsumption()
        {
            if (!enableBatteryDrain || playerData == null || !IsOn) return;
            
            // 根据光照强度调整消耗率
            float consumptionRate = batteryDrainRate * (CurrentIntensity / maxIntensity);
            
            // 消耗电量
            playerData.ConsumeBattery(consumptionRate * Time.deltaTime);
            
            // 检查电量状态
            CheckBatteryStatus();
        }
        
        private void CheckBatteryStatus()
        {
            if (playerData == null) return;
            
            float batteryPercentage = playerData.BatteryPercentage;
            
            // 低电量警告
            if (batteryPercentage <= lowBatteryThreshold / 100f && batteryPercentage > 0f)
            {
                if (!isFlickering) // 避免重复触发
                {
                    OnLowBattery?.Invoke();
                }
            }
            
            // 电量耗尽
            if (batteryPercentage <= 0f && IsOn)
            {
                TurnOff();
                OnBatteryDepleted?.Invoke();
            }
        }
        
        private void HandleBatteryChanged(float current, float max)
        {
            // 电量变化时的响应已在UpdateHeadlampIntensity中处理
        }
        
        #endregion
        
        #region 头灯控制
        
        /// <summary>
        /// 开启头灯
        /// </summary>
        public void TurnOn()
        {
            if (playerData != null && playerData.Battery <= 0f)
            {
                Debug.Log("电量不足，无法开启头灯");
                return;
            }
            
            IsOn = true;
            if (headlampLight != null)
            {
                headlampLight.enabled = true;
            }
            
            OnHeadlampToggled?.Invoke(true);
            Debug.Log("头灯已开启");
        }
        
        /// <summary>
        /// 关闭头灯
        /// </summary>
        public void TurnOff()
        {
            IsOn = false;
            if (headlampLight != null)
            {
                headlampLight.enabled = false;
            }
            
            OnHeadlampToggled?.Invoke(false);
            Debug.Log("头灯已关闭");
        }
        
        /// <summary>
        /// 切换头灯开关
        /// </summary>
        public void Toggle()
        {
            if (IsOn)
            {
                TurnOff();
            }
            else
            {
                TurnOn();
            }
        }
        
        /// <summary>
        /// 设置头灯强度
        /// </summary>
        public void SetIntensity(float intensity)
        {
            maxIntensity = Mathf.Clamp(intensity, 0.1f, 3f);
            baseIntensity = maxIntensity;
        }
        
        /// <summary>
        /// 设置头灯范围
        /// </summary>
        public void SetRange(float range)
        {
            maxRange = Mathf.Clamp(range, 1f, 15f);
            CurrentRange = maxRange;
        }
        
        /// <summary>
        /// 设置头灯角度
        /// </summary>
        public void SetConeAngle(float angle)
        {
            coneAngle = Mathf.Clamp(angle, 30f, 120f);
            if (headlampLight != null)
            {
                headlampLight.spotAngle = coneAngle;
            }
        }
        
        #endregion
        
        #region 手动控制（调试用）
        
        private void HandleManualInput()
        {
            if (Input.GetKeyDown(KeyCode.L))
            {
                Toggle();
            }
            
            if (Input.GetKeyDown(KeyCode.LeftBracket))
            {
                SetIntensity(maxIntensity - 0.2f);
            }
            
            if (Input.GetKeyDown(KeyCode.RightBracket))
            {
                SetIntensity(maxIntensity + 0.2f);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取头灯状态信息
        /// </summary>
        public string GetHeadlampStatus()
        {
            return $"头灯状态: {(IsOn ? "开启" : "关闭")}\n" +
                   $"当前强度: {CurrentIntensity:F2}\n" +
                   $"当前范围: {CurrentRange:F1}\n" +
                   $"光照方向: {LightDirection}\n" +
                   $"电量消耗: {batteryDrainRate:F1}/秒\n" +
                   $"闪烁状态: {(isFlickering ? "是" : "否")}";
        }
        
        /// <summary>
        /// 检查指定位置是否在头灯照射范围内
        /// </summary>
        public bool IsPositionIlluminated(Vector2 position)
        {
            if (!IsOn || headlampLight == null) return false;
            
            Vector2 lightPos = headlampLight.transform.position;
            Vector2 toPosition = position - lightPos;
            float distance = toPosition.magnitude;
            
            // 检查距离
            if (distance > CurrentRange) return false;
            
            // 检查角度
            float angle = Vector2.Angle(LightDirection, toPosition);
            return angle <= coneAngle * 0.5f;
        }
        
        /// <summary>
        /// 获取指定位置的光照强度
        /// </summary>
        public float GetLightIntensityAtPosition(Vector2 position)
        {
            if (!IsPositionIlluminated(position)) return 0f;
            
            Vector2 lightPos = headlampLight.transform.position;
            float distance = Vector2.Distance(lightPos, position);
            
            // 距离衰减
            float distanceAttenuation = 1f - (distance / CurrentRange);
            
            // 角度衰减
            Vector2 toPosition = (position - lightPos).normalized;
            float angle = Vector2.Angle(LightDirection, toPosition);
            float angleAttenuation = 1f - (angle / (coneAngle * 0.5f));
            
            return CurrentIntensity * distanceAttenuation * angleAttenuation;
        }
        
        #endregion
        
        #region 调试绘制
        
        private void OnDrawGizmosSelected()
        {
            if (!showDebugRays || headlampLight == null) return;
            
            Vector3 lightPos = headlampLight.transform.position;
            
            // 绘制光照方向
            Gizmos.color = Color.yellow;
            Gizmos.DrawRay(lightPos, LightDirection * CurrentRange);
            
            // 绘制光锥边界
            float halfAngle = coneAngle * 0.5f * Mathf.Deg2Rad;
            Vector2 leftBoundary = new Vector2(
                Mathf.Cos(currentRotation * Mathf.Deg2Rad + halfAngle),
                Mathf.Sin(currentRotation * Mathf.Deg2Rad + halfAngle)
            );
            Vector2 rightBoundary = new Vector2(
                Mathf.Cos(currentRotation * Mathf.Deg2Rad - halfAngle),
                Mathf.Sin(currentRotation * Mathf.Deg2Rad - halfAngle)
            );
            
            Gizmos.color = Color.cyan;
            Gizmos.DrawRay(lightPos, leftBoundary * CurrentRange);
            Gizmos.DrawRay(lightPos, rightBoundary * CurrentRange);
            
            // 绘制光照范围圆弧
            Gizmos.color = Color.white;
            Gizmos.DrawWireSphere(lightPos, CurrentRange);
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (playerController != null)
            {
                playerController.OnDirectionChanged -= HandlePlayerDirectionChanged;
            }
            
            if (playerData != null)
            {
                playerData.OnBatteryChanged -= HandleBatteryChanged;
            }
        }
        
        #endregion
    }
}
