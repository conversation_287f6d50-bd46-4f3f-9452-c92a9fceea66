# 像素艺术制作快速参考

## 🎨 Aseprite快速设置

### 新建项目
```
文件 > 新建
宽度: 32
高度: 32
色彩模式: RGB
背景: 透明
```

### 导入调色板
```
1. 窗口 > 调色板
2. 点击调色板菜单 > 加载调色板
3. 选择 MazeAdventure_Palette.gpl
```

### 基础工具设置
```
铅笔工具 (B): 主要绘制工具
橡皮擦 (E): 删除像素
选择工具 (M): 选择区域
移动工具 (V): 移动选中内容
缩放工具 (Z): 放大查看细节
```

## 📐 32x32角色布局

```
像素坐标参考 (从左上角0,0开始):

头部区域: (8,4) 到 (24,12)
├─ 头灯: (14,6) 到 (18,8)
├─ 眼睛: (13,9) 和 (19,9)
└─ 轮廓: 8x8像素

身体区域: (8,12) 到 (24,24)
├─ 肩膀: (10,13) 到 (22,15)
├─ 躯干: (12,16) 到 (20,22)
└─ 手臂: (8,16) 到 (24,20)

腿部区域: (8,24) 到 (24,32)
├─ 左腿: (10,25) 到 (14,32)
└─ 右腿: (18,25) 到 (22,32)
```

## 🎬 动画制作流程

### 1. 创建时间轴
```
窗口 > 时间轴
右键时间轴 > 新建帧
设置帧持续时间 (毫秒)
```

### 2. 动画帧设置
```
待机动画: 2帧
- 帧1: 700ms
- 帧2: 300ms

移动动画: 4帧
- 每帧: 125ms

受伤动画: 3帧
- 每帧: 100ms
```

### 3. 洋葱皮设置
```
时间轴 > 洋葱皮
前一帧: 红色 50%透明度
后一帧: 蓝色 50%透明度
```

## 🎯 绘制技巧

### 轮廓绘制
```
1. 使用最深色 (#1a1a1a)
2. 保持1像素线宽
3. 确保轮廓闭合
4. 避免锯齿状边缘
```

### 填充技巧
```
1. 使用油漆桶工具 (G)
2. 设置容差为0
3. 先填充大面积
4. 再添加细节
```

### 阴影和高光
```
光源方向: 左上角 (135度)

阴影位置:
- 右侧边缘
- 下方边缘
- 内凹部分

高光位置:
- 左上边缘
- 凸出部分
- 反光表面
```

## 🎨 色彩使用指南

### 主要颜色分配
```
#1a1a1a (轮廓) - 所有边缘线条
#404040 (阴影) - 深度阴影区域
#606060 (基础) - 皮肤、基础色调
#808080 (高光) - 亮部、金属质感
#00bcd4 (主色) - 衣服、主要装备
#2196f3 (装备) - 次要装备、细节
#fff8dc (光源) - 头灯光线
#ffeb3b (高光) - 头灯高光点
```

### 色彩搭配原则
```
1. 相邻色彩对比度要足够
2. 暖色用于光源
3. 冷色用于阴影
4. 主色占比最大
5. 高光色谨慎使用
```

## 📁 导出设置

### 单帧导出
```
文件 > 导出 > 导出为...
格式: PNG
设置: 
- 调整大小: 100%
- 应用像素比: 关闭
- 透明背景: 开启
```

### 动画序列导出
```
文件 > 导出 > 导出动画为...
格式: PNG文件
设置:
- 输出文件: {title}_{frame}.png
- 区域: 全部帧
- 图层: 可见图层
```

## ⚡ 快捷键

### 常用快捷键
```
B - 铅笔工具
E - 橡皮擦
G - 油漆桶
M - 选择工具
V - 移动工具
Z - 缩放工具
I - 吸管工具
[ ] - 调整笔刷大小
Ctrl+Z - 撤销
Ctrl+Y - 重做
Space - 平移画布
```

### 动画快捷键
```
, - 上一帧
. - 下一帧
Enter - 播放/暂停
Ctrl+M - 新建帧
Ctrl+Shift+M - 复制帧
```

## ✅ 质量检查清单

### 绘制质量
- [ ] 像素完美对齐
- [ ] 轮廓线连续
- [ ] 色彩使用正确
- [ ] 无抗锯齿
- [ ] 透明背景

### 动画质量
- [ ] 循环流畅
- [ ] 时序合适
- [ ] 无跳跃感
- [ ] 关键帧清晰
- [ ] 过渡自然

### 文件规范
- [ ] 32x32尺寸
- [ ] PNG格式
- [ ] 正确命名
- [ ] 透明背景
- [ ] 无压缩

## 🚨 常见错误

### 避免这些问题
```
❌ 使用抗锯齿
❌ 超出32x32边界
❌ 使用调色板外颜色
❌ 轮廓线不连续
❌ 动画帧数不一致
❌ 文件命名不规范
```

### 解决方案
```
✅ 关闭所有平滑选项
✅ 使用网格对齐
✅ 严格使用调色板
✅ 检查轮廓完整性
✅ 统一动画帧设置
✅ 遵循命名规范
```

## 💡 专业技巧

### 提高效率
1. **使用参考线**: 帮助对齐关键位置
2. **复制基础帧**: 避免重复绘制
3. **分层绘制**: 轮廓、填充、细节分层
4. **批量操作**: 同时调整多帧

### 提升质量
1. **放大检查**: 100%和400%缩放检查
2. **动画预览**: 频繁播放检查流畅度
3. **对比参考**: 与优秀作品对比
4. **休息眼睛**: 避免视觉疲劳

记住：像素艺术是精确的艺术，每个像素都很重要！
