# 玩家角色制作详细指南

## 🎯 设计目标

创建一个32x32像素的探险者角色，具有以下特征：
- 头戴矿工头灯的探险家形象
- 清晰可辨的轮廓和特征
- 流畅的4方向移动动画
- 符合迷宫探险主题

## 🎨 设计规范

### 基础规格
- **画布尺寸**: 32x32像素
- **色彩数量**: 最多8种颜色
- **动画帧率**: 8 FPS
- **文件格式**: PNG (无压缩)

### 调色板
```
主要颜色 (8色)：
#1a1a1a - 轮廓线/深阴影
#404040 - 中等阴影
#606060 - 基础色调
#808080 - 亮部
#00bcd4 - 角色主色 (青色衣服)
#2196f3 - 装备色 (蓝色装备)
#fff8dc - 头灯光色 (暖白)
#ffeb3b - 头灯高光 (黄色)
```

## 📐 角色比例设计

### 32x32画布分配
```
像素分配：
- 头部: 8x8像素 (行5-12)
- 身体: 8x12像素 (行13-24)  
- 腿部: 8x8像素 (行25-32)
- 左右边距: 各4像素
```

### 关键特征位置
```
头灯位置: (14,6) 到 (18,8)
眼睛位置: (13,9) 和 (19,9)
身体中心: (16,18)
脚部位置: (13,31) 和 (19,31)
```

## 🎬 动画状态设计

### 1. 待机动画 (2帧)
**player_idle_01.png**
- 基础站立姿势
- 头灯正常亮度
- 双脚并拢

**player_idle_02.png**
- 轻微上下移动 (1像素)
- 头灯稍微变亮
- 呼吸效果

**时序**: 帧1(0.7s) → 帧2(0.3s) → 循环

### 2. 向右移动动画 (4帧)
**player_walk_right_01.png**
- 左脚向前，右脚向后
- 左臂向后，右臂向前
- 头灯指向右侧

**player_walk_right_02.png**
- 双脚接近并拢
- 手臂回到中性位置
- 过渡帧

**player_walk_right_03.png**
- 右脚向前，左脚向后
- 右臂向后，左臂向前
- 身体重心转移

**player_walk_right_04.png**
- 双脚接近并拢
- 手臂回到中性位置
- 过渡帧

**时序**: 每帧0.125s，总循环0.5s

### 3. 其他方向
- **向左**: 水平翻转右移动画
- **向上**: 背面视图，类似移动节奏
- **向下**: 正面视图，类似移动节奏

### 4. 受伤动画 (3帧)
**player_hurt_01.png**
- 正常颜色
- 轻微后退姿势

**player_hurt_02.png**
- 红色叠加效果
- 身体向后倾斜

**player_hurt_03.png**
- 恢复正常
- 回到标准姿势

**时序**: 帧1(0.1s) → 帧2(0.1s) → 帧3(0.1s)

## 🛠️ Aseprite制作步骤

### 第1步：项目设置
1. 创建新文件：32x32像素
2. 设置调色板：导入上述8色调色板
3. 启用网格：1x1像素网格
4. 设置图层结构：
   ```
   图层4: 特效 (头灯光晕)
   图层3: 细节 (头灯、装备)
   图层2: 主体 (身体、四肢)
   图层1: 轮廓 (黑色边框)
   ```

### 第2步：绘制基础轮廓
1. 使用#1a1a1a绘制角色轮廓
2. 确保轮廓闭合且像素对齐
3. 头部：圆形轮廓 (8x8)
4. 身体：矩形轮廓 (8x12)
5. 腿部：两个矩形 (各3x8)

### 第3步：填充基础色彩
1. 头部填充：#606060
2. 身体填充：#00bcd4 (青色衣服)
3. 腿部填充：#404040 (深色裤子)
4. 手臂填充：#606060 (皮肤色)

### 第4步：添加细节
1. **头灯**：
   - 基础形状：#808080 (3x2像素)
   - 光源：#fff8dc (中心1像素)
   - 高光：#ffeb3b (边缘点缀)

2. **面部**：
   - 眼睛：#1a1a1a (各1像素)
   - 可选：简单的嘴部

3. **装备**：
   - 背包轮廓：#2196f3
   - 装备细节：#808080

### 第5步：添加阴影和高光
1. **阴影** (光源来自左上)：
   - 右侧和下方使用#404040
   - 深度阴影使用#1a1a1a

2. **高光**：
   - 左上方使用#808080
   - 头灯周围使用#fff8dc

### 第6步：制作动画
1. 复制基础帧
2. 逐帧调整：
   - 移动四肢位置
   - 调整身体角度
   - 更新头灯方向
3. 使用洋葱皮功能检查连贯性

## 📁 文件命名规范

```
待机动画：
player_idle_01.png
player_idle_02.png

移动动画：
player_walk_right_01.png
player_walk_right_02.png
player_walk_right_03.png
player_walk_right_04.png

player_walk_left_01.png (或使用翻转)
player_walk_up_01.png
player_walk_down_01.png

受伤动画：
player_hurt_01.png
player_hurt_02.png
player_hurt_03.png
```

## 🎯 制作技巧

### 像素完美技巧
1. **始终对齐网格**：每个像素都要精确对齐
2. **避免抗锯齿**：使用铅笔工具，关闭平滑
3. **一致的线宽**：轮廓线保持1像素宽度
4. **对称绘制**：使用镜像工具确保对称

### 动画流畅性
1. **关键帧原则**：先画极端姿势
2. **弧线运动**：四肢沿弧线移动
3. **重叠动作**：不同部位有时间差
4. **缓入缓出**：避免匀速运动

### 色彩运用
1. **限制色彩**：严格使用8色调色板
2. **对比度**：确保足够的明暗对比
3. **色彩层次**：使用3-4个明度层次
4. **统一光源**：保持左上角光照

## ✅ 质量检查

### 技术检查
- [ ] 32x32像素尺寸正确
- [ ] 透明背景
- [ ] 像素完美对齐
- [ ] 无抗锯齿
- [ ] 使用指定调色板

### 艺术检查
- [ ] 轮廓清晰可辨
- [ ] 头灯特征明显
- [ ] 色彩对比充足
- [ ] 风格统一一致
- [ ] 细节适当简化

### 动画检查
- [ ] 循环流畅无跳跃
- [ ] 时序节奏合适
- [ ] 各方向动画一致
- [ ] 关键帧姿势清晰
- [ ] 过渡自然

## 🚀 下一步

完成玩家角色后：
1. 导出所有帧为PNG文件
2. 在Unity中创建Sprite动画
3. 设置动画控制器
4. 测试像素完美显示
5. 开始制作环境瓦片

## 💡 创意建议

### 角色个性化
- 可以添加简单的表情变化
- 不同的装备颜色变体
- 特殊状态的视觉效果

### 扩展动画
- 拾取道具动画
- 开门动画
- 胜利姿势动画

记住：简洁胜过复杂，清晰胜过华丽！
