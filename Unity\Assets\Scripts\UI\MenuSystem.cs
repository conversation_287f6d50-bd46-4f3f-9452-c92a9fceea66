using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using MazeAdventure.Core;
using MazeAdventure.Gameplay;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 菜单系统 - 管理所有菜单界面的显示和交互
    /// </summary>
    public class MenuSystem : MonoBehaviour
    {
        [Header("主菜单")]
        [SerializeField] private GameObject mainMenuPanel;
        [SerializeField] private Button startGameButton;
        [SerializeField] private Button continueGameButton;
        [SerializeField] private Button settingsButton;
        [SerializeField] private Button creditsButton;
        [SerializeField] private Button quitGameButton;
        [SerializeField] private Text versionText;
        
        [Header("暂停菜单")]
        [SerializeField] private GameObject pauseMenuPanel;
        [SerializeField] private Button resumeButton;
        [SerializeField] private Button restartButton;
        [SerializeField] private Button pauseSettingsButton;
        [SerializeField] private Button mainMenuButton;
        [SerializeField] private Text pauseTimeText;
        
        [Header("游戏结束菜单")]
        [SerializeField] private GameObject gameOverPanel;
        [SerializeField] private Text gameOverTitleText;
        [SerializeField] private Text finalScoreText;
        [SerializeField] private Text finalTimeText;
        [SerializeField] private Text statisticsText;
        [SerializeField] private Button retryButton;
        [SerializeField] private Button gameOverMainMenuButton;
        
        [Header("胜利菜单")]
        [SerializeField] private GameObject victoryPanel;
        [SerializeField] private Text victoryTitleText;
        [SerializeField] private Text completionTimeText;
        [SerializeField] private Text batteryCollectedText;
        [SerializeField] private Text newRecordText;
        [SerializeField] private Button nextLevelButton;
        [SerializeField] private Button victoryMainMenuButton;
        
        [Header("确认对话框")]
        [SerializeField] private GameObject confirmationDialog;
        [SerializeField] private Text confirmationText;
        [SerializeField] private Button confirmYesButton;
        [SerializeField] private Button confirmNoButton;
        
        [Header("加载界面")]
        [SerializeField] private GameObject loadingPanel;
        [SerializeField] private Slider loadingProgressBar;
        [SerializeField] private Text loadingStatusText;
        [SerializeField] private Image loadingSpinner;
        
        [Header("动画设置")]
        [SerializeField] private float menuTransitionTime = 0.3f;
        [SerializeField] private AnimationCurve menuTransitionCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        [SerializeField] private bool enableMenuAnimations = true;
        
        [Header("背景效果")]
        [SerializeField] private ParticleSystem backgroundParticles;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private bool enableBackgroundEffects = true;
        
        // 单例模式
        public static MenuSystem Instance { get; private set; }
        
        // 当前状态
        public MenuType CurrentMenu { get; private set; }
        public bool IsMenuTransitioning { get; private set; }
        
        // 确认对话框回调
        private System.Action onConfirmYes;
        private System.Action onConfirmNo;
        
        // 动画协程
        private Coroutine menuTransitionCoroutine;
        private Coroutine loadingCoroutine;
        
        // 事件系统
        public System.Action<MenuType> OnMenuChanged;
        public System.Action<bool> OnMenuVisibilityChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeMenuSystem();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupMenuSystem();
        }
        
        private void Update()
        {
            UpdateMenuEffects();
            HandleMenuInput();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeMenuSystem()
        {
            // 隐藏所有菜单
            HideAllMenus();
            
            // 设置版本信息
            if (versionText != null)
            {
                versionText.text = $"v{Application.version}";
            }
            
            CurrentMenu = MenuType.None;
        }
        
        private void SetupMenuSystem()
        {
            // 设置按钮事件
            SetupButtonEvents();
            
            // 订阅游戏事件
            SubscribeToGameEvents();
            
            // 启用背景效果
            if (enableBackgroundEffects)
            {
                SetupBackgroundEffects();
            }
            
            // 显示主菜单
            ShowMenu(MenuType.MainMenu);
            
            Debug.Log("菜单系统初始化完成");
        }
        
        private void SetupButtonEvents()
        {
            // 主菜单按钮
            if (startGameButton != null)
                startGameButton.onClick.AddListener(OnStartGameClicked);
            if (continueGameButton != null)
                continueGameButton.onClick.AddListener(OnContinueGameClicked);
            if (settingsButton != null)
                settingsButton.onClick.AddListener(OnSettingsClicked);
            if (creditsButton != null)
                creditsButton.onClick.AddListener(OnCreditsClicked);
            if (quitGameButton != null)
                quitGameButton.onClick.AddListener(OnQuitGameClicked);
            
            // 暂停菜单按钮
            if (resumeButton != null)
                resumeButton.onClick.AddListener(OnResumeClicked);
            if (restartButton != null)
                restartButton.onClick.AddListener(OnRestartClicked);
            if (pauseSettingsButton != null)
                pauseSettingsButton.onClick.AddListener(OnSettingsClicked);
            if (mainMenuButton != null)
                mainMenuButton.onClick.AddListener(OnMainMenuClicked);
            
            // 游戏结束菜单按钮
            if (retryButton != null)
                retryButton.onClick.AddListener(OnRetryClicked);
            if (gameOverMainMenuButton != null)
                gameOverMainMenuButton.onClick.AddListener(OnMainMenuClicked);
            
            // 胜利菜单按钮
            if (nextLevelButton != null)
                nextLevelButton.onClick.AddListener(OnNextLevelClicked);
            if (victoryMainMenuButton != null)
                victoryMainMenuButton.onClick.AddListener(OnMainMenuClicked);
            
            // 确认对话框按钮
            if (confirmYesButton != null)
                confirmYesButton.onClick.AddListener(OnConfirmYesClicked);
            if (confirmNoButton != null)
                confirmNoButton.onClick.AddListener(OnConfirmNoClicked);
        }
        
        private void SubscribeToGameEvents()
        {
            // 订阅游戏状态事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged += OnGameStateChanged;
                GameStateManager.Instance.OnGameWon += OnGameWon;
                GameStateManager.Instance.OnGameLost += OnGameLost;
            }
        }
        
        private void SetupBackgroundEffects()
        {
            // 设置背景粒子效果
            if (backgroundParticles != null)
            {
                var main = backgroundParticles.main;
                main.loop = true;
                main.startLifetime = 5f;
                main.startSpeed = 0.5f;
                main.maxParticles = 50;
            }
        }
        
        #endregion
        
        #region 菜单管理
        
        /// <summary>
        /// 显示指定菜单
        /// </summary>
        public void ShowMenu(MenuType menuType, bool animated = true)
        {
            if (IsMenuTransitioning || CurrentMenu == menuType) return;
            
            if (menuTransitionCoroutine != null)
            {
                StopCoroutine(menuTransitionCoroutine);
            }
            
            if (animated && enableMenuAnimations)
            {
                menuTransitionCoroutine = StartCoroutine(TransitionToMenu(menuType));
            }
            else
            {
                SetMenuActive(menuType, true);
                SetMenuActive(CurrentMenu, false);
                CurrentMenu = menuType;
                OnMenuChanged?.Invoke(menuType);
            }
        }
        
        /// <summary>
        /// 隐藏所有菜单
        /// </summary>
        public void HideAllMenus()
        {
            SetMenuActive(MenuType.MainMenu, false);
            SetMenuActive(MenuType.PauseMenu, false);
            SetMenuActive(MenuType.GameOver, false);
            SetMenuActive(MenuType.Victory, false);
            SetMenuActive(MenuType.Loading, false);
            
            if (confirmationDialog != null)
            {
                confirmationDialog.SetActive(false);
            }
        }
        
        private void SetMenuActive(MenuType menuType, bool active)
        {
            GameObject menuObject = GetMenuObject(menuType);
            if (menuObject != null)
            {
                menuObject.SetActive(active);
            }
        }
        
        private GameObject GetMenuObject(MenuType menuType)
        {
            switch (menuType)
            {
                case MenuType.MainMenu: return mainMenuPanel;
                case MenuType.PauseMenu: return pauseMenuPanel;
                case MenuType.GameOver: return gameOverPanel;
                case MenuType.Victory: return victoryPanel;
                case MenuType.Loading: return loadingPanel;
                default: return null;
            }
        }
        
        private IEnumerator TransitionToMenu(MenuType newMenuType)
        {
            IsMenuTransitioning = true;
            
            GameObject currentMenuObj = GetMenuObject(CurrentMenu);
            GameObject newMenuObj = GetMenuObject(newMenuType);
            
            // 淡出当前菜单
            if (currentMenuObj != null)
            {
                yield return StartCoroutine(FadeMenu(currentMenuObj, 1f, 0f, menuTransitionTime * 0.5f));
                currentMenuObj.SetActive(false);
            }
            
            // 淡入新菜单
            if (newMenuObj != null)
            {
                newMenuObj.SetActive(true);
                yield return StartCoroutine(FadeMenu(newMenuObj, 0f, 1f, menuTransitionTime * 0.5f));
            }
            
            CurrentMenu = newMenuType;
            IsMenuTransitioning = false;
            OnMenuChanged?.Invoke(newMenuType);
            
            menuTransitionCoroutine = null;
        }
        
        private IEnumerator FadeMenu(GameObject menu, float fromAlpha, float toAlpha, float duration)
        {
            CanvasGroup canvasGroup = menu.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = menu.AddComponent<CanvasGroup>();
            }
            
            float elapsedTime = 0f;
            
            while (elapsedTime < duration)
            {
                elapsedTime += Time.deltaTime;
                float t = elapsedTime / duration;
                float curveValue = menuTransitionCurve.Evaluate(t);
                canvasGroup.alpha = Mathf.Lerp(fromAlpha, toAlpha, curveValue);
                yield return null;
            }
            
            canvasGroup.alpha = toAlpha;
        }
        
        #endregion
        
        #region 特殊菜单处理
        
        /// <summary>
        /// 显示加载界面
        /// </summary>
        public void ShowLoading(string statusText = "加载中...")
        {
            ShowMenu(MenuType.Loading, false);
            
            if (loadingStatusText != null)
            {
                loadingStatusText.text = statusText;
            }
            
            if (loadingProgressBar != null)
            {
                loadingProgressBar.value = 0f;
            }
            
            // 开始加载动画
            if (loadingCoroutine != null)
            {
                StopCoroutine(loadingCoroutine);
            }
            loadingCoroutine = StartCoroutine(LoadingAnimation());
        }
        
        /// <summary>
        /// 更新加载进度
        /// </summary>
        public void UpdateLoadingProgress(float progress, string statusText = null)
        {
            if (loadingProgressBar != null)
            {
                loadingProgressBar.value = progress;
            }
            
            if (!string.IsNullOrEmpty(statusText) && loadingStatusText != null)
            {
                loadingStatusText.text = statusText;
            }
        }
        
        /// <summary>
        /// 隐藏加载界面
        /// </summary>
        public void HideLoading()
        {
            if (loadingCoroutine != null)
            {
                StopCoroutine(loadingCoroutine);
                loadingCoroutine = null;
            }
            
            if (CurrentMenu == MenuType.Loading)
            {
                ShowMenu(MenuType.MainMenu);
            }
        }
        
        private IEnumerator LoadingAnimation()
        {
            while (CurrentMenu == MenuType.Loading)
            {
                if (loadingSpinner != null)
                {
                    loadingSpinner.transform.Rotate(0, 0, -90f * Time.deltaTime);
                }
                yield return null;
            }
        }
        
        /// <summary>
        /// 显示确认对话框
        /// </summary>
        public void ShowConfirmation(string message, System.Action onYes, System.Action onNo = null)
        {
            if (confirmationDialog != null)
            {
                confirmationDialog.SetActive(true);
                
                if (confirmationText != null)
                {
                    confirmationText.text = message;
                }
                
                onConfirmYes = onYes;
                onConfirmNo = onNo;
            }
        }
        
        /// <summary>
        /// 隐藏确认对话框
        /// </summary>
        public void HideConfirmation()
        {
            if (confirmationDialog != null)
            {
                confirmationDialog.SetActive(false);
            }
            
            onConfirmYes = null;
            onConfirmNo = null;
        }
        
        #endregion
        
        #region 按钮事件处理
        
        private void OnStartGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                ShowLoading("开始新游戏...");
                GameStateManager.Instance.StartNewGame();
            }
        }
        
        private void OnContinueGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameManager.Instance != null && GameManager.Instance.HasSaveData())
            {
                ShowLoading("加载游戏...");
                GameManager.Instance.LoadGame();
            }
        }
        
        private void OnSettingsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (LandscapeUIManager.Instance != null)
            {
                LandscapeUIManager.Instance.ShowPanel(UIPanel.Settings);
            }
        }
        
        private void OnCreditsClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 显示制作人员名单
            ShowConfirmation("迷宫探险游戏\n\n开发：AI助手\n引擎：Unity\n\n感谢您的游玩！", null);
        }
        
        private void OnQuitGameClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            ShowConfirmation("确定要退出游戏吗？", () => {
                Application.Quit();
            });
        }
        
        private void OnResumeClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.ResumeGame();
            }
        }
        
        private void OnRestartClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            ShowConfirmation("确定要重新开始吗？当前进度将丢失。", () => {
                if (GameStateManager.Instance != null)
                {
                    GameStateManager.Instance.RestartGame();
                }
            });
        }
        
        private void OnMainMenuClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            ShowConfirmation("确定要返回主菜单吗？当前进度将丢失。", () => {
                if (GameStateManager.Instance != null)
                {
                    GameStateManager.Instance.ExitToMenu();
                }
            });
        }
        
        private void OnRetryClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.RestartGame();
            }
        }
        
        private void OnNextLevelClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 这里可以实现下一关功能
            ShowConfirmation("恭喜通关！\n\n更多关卡敬请期待...", () => {
                if (GameStateManager.Instance != null)
                {
                    GameStateManager.Instance.ExitToMenu();
                }
            });
        }
        
        private void OnConfirmYesClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            onConfirmYes?.Invoke();
            HideConfirmation();
        }
        
        private void OnConfirmNoClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            onConfirmNo?.Invoke();
            HideConfirmation();
        }
        
        #endregion
        
        #region 游戏事件处理
        
        private void OnGameStateChanged(GameState oldState, GameState newState)
        {
            switch (newState)
            {
                case GameState.Menu:
                    ShowMenu(MenuType.MainMenu);
                    UpdateContinueButton();
                    break;
                    
                case GameState.Playing:
                    HideAllMenus();
                    break;
                    
                case GameState.Paused:
                    ShowMenu(MenuType.PauseMenu);
                    UpdatePauseMenuInfo();
                    break;
            }
        }
        
        private void OnGameWon()
        {
            ShowMenu(MenuType.Victory);
            UpdateVictoryInfo();
        }
        
        private void OnGameLost()
        {
            ShowMenu(MenuType.GameOver);
            UpdateGameOverInfo();
        }
        
        private void UpdateContinueButton()
        {
            if (continueGameButton != null && GameManager.Instance != null)
            {
                continueGameButton.interactable = GameManager.Instance.HasSaveData();
            }
        }
        
        private void UpdatePauseMenuInfo()
        {
            if (pauseTimeText != null && GameStateManager.Instance != null)
            {
                float gameTime = GameStateManager.Instance.GameTime;
                int minutes = Mathf.FloorToInt(gameTime / 60f);
                int seconds = Mathf.FloorToInt(gameTime % 60f);
                pauseTimeText.text = $"游戏时间: {minutes:00}:{seconds:00}";
            }
        }
        
        private void UpdateVictoryInfo()
        {
            if (GameStateManager.Instance != null)
            {
                float completionTime = GameStateManager.Instance.GameTime;
                int minutes = Mathf.FloorToInt(completionTime / 60f);
                int seconds = Mathf.FloorToInt(completionTime % 60f);
                
                if (completionTimeText != null)
                {
                    completionTimeText.text = $"完成时间: {minutes:00}:{seconds:00}";
                }
                
                if (batteryCollectedText != null)
                {
                    int batteryCount = GameStateManager.Instance.BatteriesCollected;
                    batteryCollectedText.text = $"收集电池: {batteryCount}";
                }
                
                // 检查是否创造新纪录
                if (newRecordText != null)
                {
                    bool isNewRecord = completionTime < GameStateManager.Instance.BestTime;
                    newRecordText.gameObject.SetActive(isNewRecord);
                    if (isNewRecord)
                    {
                        newRecordText.text = "新纪录！";
                    }
                }
            }
        }
        
        private void UpdateGameOverInfo()
        {
            if (GameStateManager.Instance != null)
            {
                float gameTime = GameStateManager.Instance.GameTime;
                int minutes = Mathf.FloorToInt(gameTime / 60f);
                int seconds = Mathf.FloorToInt(gameTime % 60f);
                
                if (finalTimeText != null)
                {
                    finalTimeText.text = $"存活时间: {minutes:00}:{seconds:00}";
                }
                
                if (statisticsText != null)
                {
                    string stats = $"收集电池: {GameStateManager.Instance.BatteriesCollected}\n" +
                                  $"收集医疗包: {GameStateManager.Instance.HealthPacksCollected}\n" +
                                  $"死亡次数: {GameStateManager.Instance.DeathCount}";
                    statisticsText.text = stats;
                }
            }
        }
        
        #endregion
        
        #region 菜单效果更新
        
        private void UpdateMenuEffects()
        {
            // 更新背景粒子效果
            if (enableBackgroundEffects && backgroundParticles != null)
            {
                bool shouldPlay = CurrentMenu == MenuType.MainMenu;
                
                if (shouldPlay && !backgroundParticles.isPlaying)
                {
                    backgroundParticles.Play();
                }
                else if (!shouldPlay && backgroundParticles.isPlaying)
                {
                    backgroundParticles.Stop();
                }
            }
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleMenuInput()
        {
            // ESC键处理
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                HandleBackButton();
            }
        }
        
        private void HandleBackButton()
        {
            if (confirmationDialog != null && confirmationDialog.activeInHierarchy)
            {
                OnConfirmNoClicked();
                return;
            }
            
            switch (CurrentMenu)
            {
                case MenuType.PauseMenu:
                    OnResumeClicked();
                    break;
                    
                case MenuType.GameOver:
                case MenuType.Victory:
                    OnMainMenuClicked();
                    break;
                    
                case MenuType.MainMenu:
                    OnQuitGameClicked();
                    break;
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取菜单状态信息
        /// </summary>
        public string GetMenuStatus()
        {
            return $"当前菜单: {CurrentMenu}\n" +
                   $"转换中: {IsMenuTransitioning}\n" +
                   $"菜单动画: {enableMenuAnimations}\n" +
                   $"背景效果: {enableBackgroundEffects}";
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 停止所有协程
            StopAllCoroutines();
            
            // 取消订阅事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged -= OnGameStateChanged;
                GameStateManager.Instance.OnGameWon -= OnGameWon;
                GameStateManager.Instance.OnGameLost -= OnGameLost;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 菜单类型枚举
    /// </summary>
    public enum MenuType
    {
        None,           // 无菜单
        MainMenu,       // 主菜单
        PauseMenu,      // 暂停菜单
        GameOver,       // 游戏结束
        Victory,        // 胜利菜单
        Loading         // 加载界面
    }
}
