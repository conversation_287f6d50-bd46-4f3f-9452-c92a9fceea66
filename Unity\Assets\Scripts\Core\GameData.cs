using System;
using UnityEngine;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 游戏数据类 - 存储需要持久化的游戏数据
    /// </summary>
    [Serializable]
    public class GameData
    {
        [Header("玩家进度")]
        public int currentLevel = 1;
        public int maxUnlockedLevel = 1;
        public int totalScore = 0;
        public int highScore = 0;
        
        [Header("游戏统计")]
        public int totalPlayTime = 0; // 总游戏时间（秒）
        public int totalDeaths = 0;   // 总死亡次数
        public int totalWins = 0;     // 总胜利次数
        public int batteriesCollected = 0; // 收集的电池总数
        public int trapsTriggered = 0;     // 触发的陷阱总数
        
        [Header("设置选项")]
        public float masterVolume = 1f;
        public float sfxVolume = 0.8f;
        public float musicVolume = 0.6f;
        public bool enableVibration = true;
        public int qualityLevel = 2; // 0=低, 1=中, 2=高
        
        [Header("输入设置")]
        public float joystickSensitivity = 1f;
        public float touchSensitivity = 1.5f;
        public bool leftHandedMode = false;
        
        [Header("成就数据")]
        public bool[] achievements = new bool[20]; // 预留20个成就位
        
        [Header("关卡数据")]
        public LevelData[] levelData = new LevelData[10]; // 预留10个关卡
        
        // 构造函数
        public GameData()
        {
            InitializeDefaultValues();
        }
        
        /// <summary>
        /// 初始化默认值
        /// </summary>
        private void InitializeDefaultValues()
        {
            currentLevel = 1;
            maxUnlockedLevel = 1;
            totalScore = 0;
            highScore = 0;
            
            totalPlayTime = 0;
            totalDeaths = 0;
            totalWins = 0;
            batteriesCollected = 0;
            trapsTriggered = 0;
            
            masterVolume = 1f;
            sfxVolume = 0.8f;
            musicVolume = 0.6f;
            enableVibration = true;
            qualityLevel = 2;
            
            joystickSensitivity = 1f;
            touchSensitivity = 1.5f;
            leftHandedMode = false;
            
            achievements = new bool[20];
            levelData = new LevelData[10];
            
            // 初始化关卡数据
            for (int i = 0; i < levelData.Length; i++)
            {
                levelData[i] = new LevelData();
            }
        }
        
        /// <summary>
        /// 更新最高分
        /// </summary>
        public void UpdateHighScore(int newScore)
        {
            if (newScore > highScore)
            {
                highScore = newScore;
            }
            totalScore += newScore;
        }
        
        /// <summary>
        /// 解锁新关卡
        /// </summary>
        public void UnlockLevel(int level)
        {
            if (level > maxUnlockedLevel)
            {
                maxUnlockedLevel = level;
            }
        }
        
        /// <summary>
        /// 记录关卡完成
        /// </summary>
        public void RecordLevelCompletion(int level, float completionTime, int score)
        {
            if (level > 0 && level <= levelData.Length)
            {
                var data = levelData[level - 1];
                data.isCompleted = true;
                data.playCount++;
                
                if (completionTime < data.bestTime || data.bestTime == 0)
                {
                    data.bestTime = completionTime;
                }
                
                if (score > data.bestScore)
                {
                    data.bestScore = score;
                }
                
                data.lastPlayedTime = DateTime.Now.ToBinary();
            }
            
            totalWins++;
        }
        
        /// <summary>
        /// 记录死亡
        /// </summary>
        public void RecordDeath()
        {
            totalDeaths++;
        }
        
        /// <summary>
        /// 记录电池收集
        /// </summary>
        public void RecordBatteryCollected()
        {
            batteriesCollected++;
        }
        
        /// <summary>
        /// 记录陷阱触发
        /// </summary>
        public void RecordTrapTriggered()
        {
            trapsTriggered++;
        }
        
        /// <summary>
        /// 解锁成就
        /// </summary>
        public void UnlockAchievement(int achievementId)
        {
            if (achievementId >= 0 && achievementId < achievements.Length)
            {
                achievements[achievementId] = true;
            }
        }
        
        /// <summary>
        /// 检查成就是否已解锁
        /// </summary>
        public bool IsAchievementUnlocked(int achievementId)
        {
            if (achievementId >= 0 && achievementId < achievements.Length)
            {
                return achievements[achievementId];
            }
            return false;
        }
        
        /// <summary>
        /// 获取已解锁成就数量
        /// </summary>
        public int GetUnlockedAchievementCount()
        {
            int count = 0;
            for (int i = 0; i < achievements.Length; i++)
            {
                if (achievements[i]) count++;
            }
            return count;
        }
        
        /// <summary>
        /// 获取游戏时长（格式化字符串）
        /// </summary>
        public string GetFormattedPlayTime()
        {
            int hours = totalPlayTime / 3600;
            int minutes = (totalPlayTime % 3600) / 60;
            int seconds = totalPlayTime % 60;
            
            if (hours > 0)
            {
                return $"{hours:D2}:{minutes:D2}:{seconds:D2}";
            }
            else
            {
                return $"{minutes:D2}:{seconds:D2}";
            }
        }
        
        /// <summary>
        /// 获取胜率
        /// </summary>
        public float GetWinRate()
        {
            int totalGames = totalWins + totalDeaths;
            if (totalGames == 0) return 0f;
            return (float)totalWins / totalGames;
        }
        
        /// <summary>
        /// 重置游戏数据
        /// </summary>
        public void ResetGameData()
        {
            InitializeDefaultValues();
        }
        
        /// <summary>
        /// 验证数据完整性
        /// </summary>
        public void ValidateData()
        {
            // 确保数组不为空
            if (achievements == null)
                achievements = new bool[20];
            
            if (levelData == null)
            {
                levelData = new LevelData[10];
                for (int i = 0; i < levelData.Length; i++)
                {
                    levelData[i] = new LevelData();
                }
            }
            
            // 确保数值在合理范围内
            currentLevel = Mathf.Max(1, currentLevel);
            maxUnlockedLevel = Mathf.Max(1, maxUnlockedLevel);
            totalScore = Mathf.Max(0, totalScore);
            highScore = Mathf.Max(0, highScore);
            
            totalPlayTime = Mathf.Max(0, totalPlayTime);
            totalDeaths = Mathf.Max(0, totalDeaths);
            totalWins = Mathf.Max(0, totalWins);
            batteriesCollected = Mathf.Max(0, batteriesCollected);
            trapsTriggered = Mathf.Max(0, trapsTriggered);
            
            masterVolume = Mathf.Clamp01(masterVolume);
            sfxVolume = Mathf.Clamp01(sfxVolume);
            musicVolume = Mathf.Clamp01(musicVolume);
            qualityLevel = Mathf.Clamp(qualityLevel, 0, 2);
            
            joystickSensitivity = Mathf.Clamp(joystickSensitivity, 0.1f, 2f);
            touchSensitivity = Mathf.Clamp(touchSensitivity, 0.5f, 3f);
        }
    }
    
    /// <summary>
    /// 关卡数据
    /// </summary>
    [Serializable]
    public class LevelData
    {
        public bool isCompleted = false;
        public int playCount = 0;
        public float bestTime = 0f;
        public int bestScore = 0;
        public long lastPlayedTime = 0; // DateTime.ToBinary()
        
        /// <summary>
        /// 获取最后游戏时间
        /// </summary>
        public DateTime GetLastPlayedDateTime()
        {
            if (lastPlayedTime == 0)
                return DateTime.MinValue;
            
            return DateTime.FromBinary(lastPlayedTime);
        }
        
        /// <summary>
        /// 获取格式化的最佳时间
        /// </summary>
        public string GetFormattedBestTime()
        {
            if (bestTime <= 0) return "--:--";
            
            int minutes = (int)(bestTime / 60);
            int seconds = (int)(bestTime % 60);
            return $"{minutes:D2}:{seconds:D2}";
        }
    }
}
