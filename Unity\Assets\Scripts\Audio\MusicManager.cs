using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using MazeAdventure.Core;

namespace MazeAdventure.Audio
{
    /// <summary>
    /// 音乐管理器 - 专门管理背景音乐的播放和切换
    /// </summary>
    public class MusicManager : MonoBehaviour
    {
        [Header("音乐轨道")]
        [SerializeField] private MusicTrack[] musicTracks;
        [SerializeField] private AudioSource primaryMusicSource;
        [SerializeField] private AudioSource secondaryMusicSource;
        
        [Header("播放设置")]
        [SerializeField] private bool autoPlayOnStart = true;
        [SerializeField] private float defaultFadeTime = 2f;
        [SerializeField] private float crossfadeTime = 3f;
        [SerializeField] private bool enableDynamicMusic = true;
        
        [Header("动态音乐")]
        [SerializeField] private float tensionCheckInterval = 1f;
        [SerializeField] private float lowTensionThreshold = 0.3f;
        [SerializeField] private float highTensionThreshold = 0.7f;
        
        // 单例模式
        public static MusicManager Instance { get; private set; }
        
        // 当前状态
        public MusicTrack CurrentTrack { get; private set; }
        public bool IsPlaying { get; private set; }
        public float CurrentVolume { get; private set; } = 1f;
        
        // 音乐轨道字典
        private Dictionary<MusicType, MusicTrack> trackDictionary;
        
        // 淡入淡出控制
        private Coroutine fadeCoroutine;
        private Coroutine crossfadeCoroutine;
        private Coroutine dynamicMusicCoroutine;
        
        // 动态音乐状态
        private float currentTension = 0f;
        private MusicType lastDynamicType = MusicType.Ambient;
        
        // 事件系统
        public System.Action<MusicTrack> OnMusicChanged;
        public System.Action<float> OnVolumeChanged;
        public System.Action<bool> OnPlayStateChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeMusicManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupMusicManager();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeMusicManager()
        {
            // 创建音频源（如果不存在）
            CreateAudioSources();
            
            // 构建音乐轨道字典
            BuildTrackDictionary();
            
            // 从AudioManager获取音量设置
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.OnMusicVolumeChanged += OnMusicVolumeChanged;
                CurrentVolume = AudioManager.Instance.musicVolume;
            }
        }
        
        private void CreateAudioSources()
        {
            if (primaryMusicSource == null)
            {
                GameObject primaryObj = new GameObject("PrimaryMusicSource");
                primaryObj.transform.SetParent(transform);
                primaryMusicSource = primaryObj.AddComponent<AudioSource>();
                primaryMusicSource.loop = true;
                primaryMusicSource.playOnAwake = false;
            }
            
            if (secondaryMusicSource == null)
            {
                GameObject secondaryObj = new GameObject("SecondaryMusicSource");
                secondaryObj.transform.SetParent(transform);
                secondaryMusicSource = secondaryObj.AddComponent<AudioSource>();
                secondaryMusicSource.loop = true;
                secondaryMusicSource.playOnAwake = false;
            }
        }
        
        private void BuildTrackDictionary()
        {
            trackDictionary = new Dictionary<MusicType, MusicTrack>();
            
            if (musicTracks != null)
            {
                foreach (MusicTrack track in musicTracks)
                {
                    if (track != null && track.audioClip != null)
                    {
                        trackDictionary[track.musicType] = track;
                    }
                }
            }
            
            Debug.Log($"音乐管理器加载了 {trackDictionary.Count} 个音乐轨道");
        }
        
        private void SetupMusicManager()
        {
            // 订阅游戏事件
            SubscribeToGameEvents();
            
            // 开始动态音乐系统
            if (enableDynamicMusic)
            {
                dynamicMusicCoroutine = StartCoroutine(DynamicMusicCoroutine());
            }
            
            // 自动播放开始音乐
            if (autoPlayOnStart)
            {
                PlayMusic(MusicType.Menu);
            }
            
            Debug.Log("音乐管理器初始化完成");
        }
        
        private void SubscribeToGameEvents()
        {
            // 订阅游戏状态事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged += OnGameStateChanged;
            }
            
            // 订阅玩家数据事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnLowBattery += () => UpdateTension(0.8f);
                PlayerData.Instance.OnDamageTaken += (damage) => UpdateTension(0.6f);
            }
            
            // 订阅道具系统事件
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnItemCollected += (type, pos) => UpdateTension(-0.1f);
            }
        }
        
        #endregion
        
        #region 音乐播放控制
        
        /// <summary>
        /// 播放指定类型的音乐
        /// </summary>
        public void PlayMusic(MusicType musicType, bool fadeIn = true, float fadeTime = -1f)
        {
            if (!trackDictionary.ContainsKey(musicType))
            {
                Debug.LogWarning($"未找到音乐类型: {musicType}");
                return;
            }
            
            MusicTrack track = trackDictionary[musicType];
            PlayMusic(track, fadeIn, fadeTime);
        }
        
        /// <summary>
        /// 播放指定的音乐轨道
        /// </summary>
        public void PlayMusic(MusicTrack track, bool fadeIn = true, float fadeTime = -1f)
        {
            if (track == null || track.audioClip == null)
            {
                Debug.LogWarning("音乐轨道为空");
                return;
            }
            
            if (CurrentTrack == track && IsPlaying)
            {
                return; // 已经在播放相同的音乐
            }
            
            float actualFadeTime = fadeTime > 0 ? fadeTime : defaultFadeTime;
            
            // 停止之前的淡入淡出
            if (fadeCoroutine != null)
            {
                StopCoroutine(fadeCoroutine);
            }
            
            if (crossfadeCoroutine != null)
            {
                StopCoroutine(crossfadeCoroutine);
            }
            
            // 如果当前有音乐在播放，使用交叉淡入淡出
            if (IsPlaying && CurrentTrack != null)
            {
                crossfadeCoroutine = StartCoroutine(CrossfadeToTrack(track, actualFadeTime));
            }
            else
            {
                // 直接播放新音乐
                StartPlayingTrack(track, fadeIn, actualFadeTime);
            }
        }
        
        /// <summary>
        /// 停止音乐播放
        /// </summary>
        public void StopMusic(bool fadeOut = true, float fadeTime = -1f)
        {
            if (!IsPlaying) return;
            
            float actualFadeTime = fadeTime > 0 ? fadeTime : defaultFadeTime;
            
            if (fadeCoroutine != null)
            {
                StopCoroutine(fadeCoroutine);
            }
            
            if (crossfadeCoroutine != null)
            {
                StopCoroutine(crossfadeCoroutine);
            }
            
            if (fadeOut)
            {
                fadeCoroutine = StartCoroutine(FadeOutMusic(actualFadeTime));
            }
            else
            {
                primaryMusicSource.Stop();
                secondaryMusicSource.Stop();
                IsPlaying = false;
                CurrentTrack = null;
                OnPlayStateChanged?.Invoke(false);
            }
        }
        
        /// <summary>
        /// 暂停音乐
        /// </summary>
        public void PauseMusic()
        {
            if (IsPlaying)
            {
                primaryMusicSource.Pause();
                secondaryMusicSource.Pause();
                OnPlayStateChanged?.Invoke(false);
            }
        }
        
        /// <summary>
        /// 恢复音乐
        /// </summary>
        public void ResumeMusic()
        {
            if (CurrentTrack != null)
            {
                primaryMusicSource.UnPause();
                secondaryMusicSource.UnPause();
                IsPlaying = true;
                OnPlayStateChanged?.Invoke(true);
            }
        }
        
        #endregion
        
        #region 音乐切换协程
        
        private void StartPlayingTrack(MusicTrack track, bool fadeIn, float fadeTime)
        {
            primaryMusicSource.clip = track.audioClip;
            primaryMusicSource.volume = fadeIn ? 0f : track.volume * CurrentVolume;
            primaryMusicSource.pitch = track.pitch;
            primaryMusicSource.Play();
            
            CurrentTrack = track;
            IsPlaying = true;
            
            if (fadeIn)
            {
                fadeCoroutine = StartCoroutine(FadeInMusic(track.volume * CurrentVolume, fadeTime));
            }
            
            OnMusicChanged?.Invoke(track);
            OnPlayStateChanged?.Invoke(true);
        }
        
        private IEnumerator CrossfadeToTrack(MusicTrack newTrack, float fadeTime)
        {
            // 设置新音乐到辅助音频源
            secondaryMusicSource.clip = newTrack.audioClip;
            secondaryMusicSource.volume = 0f;
            secondaryMusicSource.pitch = newTrack.pitch;
            secondaryMusicSource.Play();
            
            float startVolume = primaryMusicSource.volume;
            float targetVolume = newTrack.volume * CurrentVolume;
            
            // 交叉淡入淡出
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                float progress = t / fadeTime;
                
                primaryMusicSource.volume = Mathf.Lerp(startVolume, 0f, progress);
                secondaryMusicSource.volume = Mathf.Lerp(0f, targetVolume, progress);
                
                yield return null;
            }
            
            // 完成切换
            primaryMusicSource.Stop();
            primaryMusicSource.volume = targetVolume;
            
            // 交换音频源
            AudioSource temp = primaryMusicSource;
            primaryMusicSource = secondaryMusicSource;
            secondaryMusicSource = temp;
            
            CurrentTrack = newTrack;
            OnMusicChanged?.Invoke(newTrack);
            
            crossfadeCoroutine = null;
        }
        
        private IEnumerator FadeInMusic(float targetVolume, float fadeTime)
        {
            float startVolume = primaryMusicSource.volume;
            
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                primaryMusicSource.volume = Mathf.Lerp(startVolume, targetVolume, t / fadeTime);
                yield return null;
            }
            
            primaryMusicSource.volume = targetVolume;
            fadeCoroutine = null;
        }
        
        private IEnumerator FadeOutMusic(float fadeTime)
        {
            float startVolume = primaryMusicSource.volume;
            
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                primaryMusicSource.volume = Mathf.Lerp(startVolume, 0f, t / fadeTime);
                yield return null;
            }
            
            primaryMusicSource.Stop();
            primaryMusicSource.volume = startVolume;
            IsPlaying = false;
            CurrentTrack = null;
            OnPlayStateChanged?.Invoke(false);
            
            fadeCoroutine = null;
        }
        
        #endregion
        
        #region 动态音乐系统
        
        private IEnumerator DynamicMusicCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(tensionCheckInterval);
                
                if (GameStateManager.Instance != null && GameStateManager.Instance.IsGameActive)
                {
                    UpdateDynamicMusic();
                }
            }
        }
        
        private void UpdateDynamicMusic()
        {
            // 根据游戏状态计算紧张度
            CalculateTension();
            
            // 根据紧张度选择合适的音乐
            MusicType targetType = GetMusicTypeForTension(currentTension);
            
            // 如果需要切换音乐
            if (targetType != lastDynamicType && trackDictionary.ContainsKey(targetType))
            {
                PlayMusic(targetType, true, crossfadeTime);
                lastDynamicType = targetType;
            }
        }
        
        private void CalculateTension()
        {
            float tension = 0f;
            
            if (PlayerData.Instance != null)
            {
                // 根据电量计算紧张度
                float batteryTension = 1f - PlayerData.Instance.BatteryPercentage;
                tension += batteryTension * 0.4f;
                
                // 根据生命值计算紧张度
                float healthTension = 1f - PlayerData.Instance.HealthPercentage;
                tension += healthTension * 0.3f;
            }
            
            if (GameStateManager.Instance != null)
            {
                // 根据剩余时间计算紧张度
                float timeRatio = GameStateManager.Instance.GameTime / 300f; // 假设5分钟游戏时间
                tension += timeRatio * 0.3f;
            }
            
            // 平滑过渡紧张度
            currentTension = Mathf.Lerp(currentTension, Mathf.Clamp01(tension), Time.deltaTime * 0.5f);
        }
        
        private MusicType GetMusicTypeForTension(float tension)
        {
            if (tension < lowTensionThreshold)
            {
                return MusicType.Ambient;
            }
            else if (tension < highTensionThreshold)
            {
                return MusicType.Exploration;
            }
            else
            {
                return MusicType.Tension;
            }
        }
        
        /// <summary>
        /// 手动更新紧张度
        /// </summary>
        public void UpdateTension(float tensionChange)
        {
            currentTension = Mathf.Clamp01(currentTension + tensionChange);
        }
        
        #endregion
        
        #region 事件处理
        
        private void OnGameStateChanged(GameState oldState, GameState newState)
        {
            switch (newState)
            {
                case GameState.Menu:
                    PlayMusic(MusicType.Menu);
                    break;
                    
                case GameState.Playing:
                    if (enableDynamicMusic)
                    {
                        PlayMusic(MusicType.Ambient);
                    }
                    else
                    {
                        PlayMusic(MusicType.Gameplay);
                    }
                    break;
                    
                case GameState.Paused:
                    PauseMusic();
                    break;
                    
                case GameState.Victory:
                    PlayMusic(MusicType.Victory);
                    break;
                    
                case GameState.GameOver:
                    PlayMusic(MusicType.GameOver);
                    break;
            }
            
            // 从暂停状态恢复时
            if (oldState == GameState.Paused && newState == GameState.Playing)
            {
                ResumeMusic();
            }
        }
        
        private void OnMusicVolumeChanged(float volume)
        {
            CurrentVolume = volume;
            
            // 更新当前播放音乐的音量
            if (IsPlaying && CurrentTrack != null)
            {
                primaryMusicSource.volume = CurrentTrack.volume * CurrentVolume;
            }
            
            OnVolumeChanged?.Invoke(CurrentVolume);
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置音乐音量
        /// </summary>
        public void SetVolume(float volume)
        {
            CurrentVolume = Mathf.Clamp01(volume);
            
            if (IsPlaying && CurrentTrack != null)
            {
                primaryMusicSource.volume = CurrentTrack.volume * CurrentVolume;
            }
            
            OnVolumeChanged?.Invoke(CurrentVolume);
        }
        
        /// <summary>
        /// 获取指定类型的音乐轨道
        /// </summary>
        public MusicTrack GetTrack(MusicType musicType)
        {
            return trackDictionary.ContainsKey(musicType) ? trackDictionary[musicType] : null;
        }
        
        /// <summary>
        /// 检查是否有指定类型的音乐
        /// </summary>
        public bool HasTrack(MusicType musicType)
        {
            return trackDictionary.ContainsKey(musicType);
        }
        
        /// <summary>
        /// 获取当前紧张度
        /// </summary>
        public float GetCurrentTension()
        {
            return currentTension;
        }
        
        /// <summary>
        /// 启用/禁用动态音乐
        /// </summary>
        public void SetDynamicMusicEnabled(bool enabled)
        {
            enableDynamicMusic = enabled;
            
            if (enabled && dynamicMusicCoroutine == null)
            {
                dynamicMusicCoroutine = StartCoroutine(DynamicMusicCoroutine());
            }
            else if (!enabled && dynamicMusicCoroutine != null)
            {
                StopCoroutine(dynamicMusicCoroutine);
                dynamicMusicCoroutine = null;
            }
        }
        
        /// <summary>
        /// 获取音乐状态信息
        /// </summary>
        public string GetMusicStatus()
        {
            return $"音乐状态:\n" +
                   $"当前轨道: {(CurrentTrack != null ? CurrentTrack.name : "无")}\n" +
                   $"播放状态: {(IsPlaying ? "播放中" : "已停止")}\n" +
                   $"音量: {CurrentVolume:P0}\n" +
                   $"紧张度: {currentTension:P0}\n" +
                   $"动态音乐: {(enableDynamicMusic ? "启用" : "禁用")}\n" +
                   $"可用轨道: {trackDictionary.Count}";
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 停止所有协程
            if (fadeCoroutine != null)
            {
                StopCoroutine(fadeCoroutine);
            }
            
            if (crossfadeCoroutine != null)
            {
                StopCoroutine(crossfadeCoroutine);
            }
            
            if (dynamicMusicCoroutine != null)
            {
                StopCoroutine(dynamicMusicCoroutine);
            }
            
            // 取消订阅事件
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.OnMusicVolumeChanged -= OnMusicVolumeChanged;
            }
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStateChanged -= OnGameStateChanged;
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 音乐类型枚举
    /// </summary>
    public enum MusicType
    {
        Menu,           // 菜单音乐
        Ambient,        // 环境音乐（低紧张度）
        Exploration,    // 探索音乐（中等紧张度）
        Tension,        // 紧张音乐（高紧张度）
        Gameplay,       // 通用游戏音乐
        Victory,        // 胜利音乐
        GameOver        // 游戏结束音乐
    }
    
    /// <summary>
    /// 音乐轨道数据
    /// </summary>
    [System.Serializable]
    public class MusicTrack
    {
        public string name;
        public MusicType musicType;
        public AudioClip audioClip;
        [Range(0f, 1f)]
        public float volume = 1f;
        [Range(0.5f, 2f)]
        public float pitch = 1f;
        public bool loop = true;
        [TextArea(2, 4)]
        public string description;
    }
}
