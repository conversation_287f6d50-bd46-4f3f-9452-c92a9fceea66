using UnityEngine;
using System.Collections.Generic;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 输入管理器 - 处理手机触屏输入和虚拟摇杆
    /// </summary>
    public class InputManager : MonoBehaviour
    {
        [Header("虚拟摇杆设置")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        [SerializeField] private float joystickRange = 50f;
        [SerializeField] private bool dynamicJoystick = true;
        
        [Header("输入设置")]
        [SerializeField] private float deadZone = 0.1f;
        [SerializeField] private float sensitivity = 1f;
        
        // 单例模式
        public static InputManager Instance { get; private set; }
        
        // 输入状态
        public Vector2 MoveInput { get; private set; }
        public bool IsTouching { get; private set; }
        public Vector2 TouchPosition { get; private set; }
        public Vector2 TouchDelta { get; private set; }
        
        // 事件系统
        public System.Action<Vector2> OnMoveInputChanged;
        public System.Action OnTouchStart;
        public System.Action OnTouchEnd;
        public System.Action OnSpecialAction; // 特殊操作（比如使用道具）
        
        // 私有变量
        private bool isJoystickActive = false;
        private Vector2 joystickStartPosition;
        private Vector2 lastTouchPosition;
        private int joystickTouchId = -1;
        
        // 触摸历史记录（用于手势识别）
        private List<Vector2> touchHistory = new List<Vector2>();
        private float touchStartTime;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        #endregion
        
        #region 初始化
        
        public void Initialize()
        {
            // 从游戏配置加载设置
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                deadZone = config.joystickDeadZone;
                sensitivity = config.joystickSensitivity;
            }
            
            // 初始化虚拟摇杆
            InitializeJoystick();
            
            Debug.Log("输入管理器初始化完成");
        }
        
        private void InitializeJoystick()
        {
            if (joystickBackground != null && joystickHandle != null)
            {
                // 设置摇杆初始状态
                joystickHandle.anchoredPosition = Vector2.zero;
                
                if (dynamicJoystick)
                {
                    joystickBackground.gameObject.SetActive(false);
                }
            }
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInput()
        {
            #if UNITY_EDITOR || UNITY_STANDALONE
                HandleMouseInput();
            #elif UNITY_ANDROID || UNITY_IOS
                HandleTouchInput();
            #endif
        }
        
        #if UNITY_EDITOR || UNITY_STANDALONE
        private void HandleMouseInput()
        {
            // 鼠标输入（用于编辑器测试）
            if (Input.GetMouseButtonDown(0))
            {
                StartJoystick(Input.mousePosition);
            }
            else if (Input.GetMouseButton(0) && isJoystickActive)
            {
                UpdateJoystick(Input.mousePosition);
            }
            else if (Input.GetMouseButtonUp(0))
            {
                EndJoystick();
            }
            
            // 键盘输入（备用）
            Vector2 keyboardInput = Vector2.zero;
            if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
                keyboardInput.y += 1;
            if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
                keyboardInput.y -= 1;
            if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
                keyboardInput.x -= 1;
            if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
                keyboardInput.x += 1;
            
            if (keyboardInput != Vector2.zero)
            {
                SetMoveInput(keyboardInput.normalized);
            }
        }
        #endif
        
        private void HandleTouchInput()
        {
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                
                switch (touch.phase)
                {
                    case TouchPhase.Began:
                        HandleTouchBegan(touch);
                        break;
                        
                    case TouchPhase.Moved:
                        HandleTouchMoved(touch);
                        break;
                        
                    case TouchPhase.Ended:
                    case TouchPhase.Canceled:
                        HandleTouchEnded(touch);
                        break;
                }
            }
        }
        
        private void HandleTouchBegan(Touch touch)
        {
            Vector2 screenPosition = touch.position;
            
            // 检查是否在摇杆区域
            if (IsInJoystickArea(screenPosition))
            {
                if (joystickTouchId == -1) // 确保只有一个触摸控制摇杆
                {
                    joystickTouchId = touch.fingerId;
                    StartJoystick(screenPosition);
                }
            }
            else
            {
                // 处理其他触摸（特殊操作）
                HandleSpecialTouch(screenPosition);
            }
            
            // 记录触摸开始
            IsTouching = true;
            TouchPosition = screenPosition;
            touchStartTime = Time.time;
            touchHistory.Clear();
            touchHistory.Add(screenPosition);
            
            OnTouchStart?.Invoke();
        }
        
        private void HandleTouchMoved(Touch touch)
        {
            if (touch.fingerId == joystickTouchId && isJoystickActive)
            {
                UpdateJoystick(touch.position);
            }
            
            // 更新触摸信息
            TouchDelta = touch.position - lastTouchPosition;
            TouchPosition = touch.position;
            lastTouchPosition = touch.position;
            
            // 记录触摸轨迹
            touchHistory.Add(touch.position);
            if (touchHistory.Count > 10) // 限制历史记录长度
            {
                touchHistory.RemoveAt(0);
            }
        }
        
        private void HandleTouchEnded(Touch touch)
        {
            if (touch.fingerId == joystickTouchId)
            {
                EndJoystick();
                joystickTouchId = -1;
            }
            
            // 检查是否为点击操作
            float touchDuration = Time.time - touchStartTime;
            if (touchDuration < 0.3f && touchHistory.Count < 3)
            {
                // 短时间触摸，可能是点击操作
                HandleTap(touch.position);
            }
            
            IsTouching = false;
            TouchDelta = Vector2.zero;
            OnTouchEnd?.Invoke();
        }
        
        #endregion
        
        #region 虚拟摇杆
        
        private bool IsInJoystickArea(Vector2 screenPosition)
        {
            if (dynamicJoystick)
            {
                // 动态摇杆：屏幕左半部分
                return screenPosition.x < Screen.width * 0.5f;
            }
            else
            {
                // 固定摇杆：检查是否在摇杆范围内
                if (joystickBackground == null) return false;
                
                Vector2 localPoint;
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    joystickBackground, screenPosition, null, out localPoint);
                
                return localPoint.magnitude <= joystickRange;
            }
        }
        
        private void StartJoystick(Vector2 screenPosition)
        {
            isJoystickActive = true;
            
            if (dynamicJoystick && joystickBackground != null)
            {
                // 动态摇杆：在触摸位置显示
                joystickBackground.gameObject.SetActive(true);
                joystickBackground.position = screenPosition;
                joystickStartPosition = screenPosition;
            }
            else if (joystickBackground != null)
            {
                // 固定摇杆：使用固定位置
                joystickStartPosition = joystickBackground.position;
            }
        }
        
        private void UpdateJoystick(Vector2 screenPosition)
        {
            if (!isJoystickActive || joystickHandle == null) return;
            
            Vector2 direction = screenPosition - joystickStartPosition;
            float distance = direction.magnitude;
            
            // 限制摇杆范围
            if (distance > joystickRange)
            {
                direction = direction.normalized * joystickRange;
                distance = joystickRange;
            }
            
            // 更新摇杆手柄位置
            joystickHandle.anchoredPosition = direction;
            
            // 计算输入值
            Vector2 normalizedInput = direction / joystickRange;
            
            // 应用死区
            if (normalizedInput.magnitude < deadZone)
            {
                normalizedInput = Vector2.zero;
            }
            else
            {
                // 重新映射到死区外的范围
                float magnitude = (normalizedInput.magnitude - deadZone) / (1f - deadZone);
                normalizedInput = normalizedInput.normalized * magnitude;
            }
            
            // 应用灵敏度
            normalizedInput *= sensitivity;
            
            SetMoveInput(normalizedInput);
        }
        
        private void EndJoystick()
        {
            isJoystickActive = false;
            
            if (joystickHandle != null)
            {
                joystickHandle.anchoredPosition = Vector2.zero;
            }
            
            if (dynamicJoystick && joystickBackground != null)
            {
                joystickBackground.gameObject.SetActive(false);
            }
            
            SetMoveInput(Vector2.zero);
        }
        
        #endregion
        
        #region 输入设置
        
        private void SetMoveInput(Vector2 input)
        {
            if (MoveInput != input)
            {
                MoveInput = input;
                OnMoveInputChanged?.Invoke(input);
            }
        }
        
        private void HandleSpecialTouch(Vector2 screenPosition)
        {
            // 处理特殊操作区域的触摸
            // 比如屏幕右侧的特殊操作按钮
            if (screenPosition.x > Screen.width * 0.8f)
            {
                OnSpecialAction?.Invoke();
            }
        }
        
        private void HandleTap(Vector2 screenPosition)
        {
            // 处理点击操作
            Debug.Log($"点击位置: {screenPosition}");
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置摇杆灵敏度
        /// </summary>
        public void SetSensitivity(float newSensitivity)
        {
            sensitivity = Mathf.Clamp(newSensitivity, 0.1f, 2f);
        }
        
        /// <summary>
        /// 设置死区大小
        /// </summary>
        public void SetDeadZone(float newDeadZone)
        {
            deadZone = Mathf.Clamp01(newDeadZone);
        }
        
        /// <summary>
        /// 启用/禁用动态摇杆
        /// </summary>
        public void SetDynamicJoystick(bool enabled)
        {
            dynamicJoystick = enabled;
            InitializeJoystick();
        }
        
        /// <summary>
        /// 获取移动输入的方向角度
        /// </summary>
        public float GetMoveAngle()
        {
            if (MoveInput.magnitude < 0.1f) return 0f;
            return Mathf.Atan2(MoveInput.y, MoveInput.x) * Mathf.Rad2Deg;
        }
        
        /// <summary>
        /// 检查是否有移动输入
        /// </summary>
        public bool HasMoveInput()
        {
            return MoveInput.magnitude > 0.1f;
        }
        
        /// <summary>
        /// 震动反馈
        /// </summary>
        public void Vibrate(float duration = 0.1f)
        {
            #if UNITY_ANDROID && !UNITY_EDITOR
                Handheld.Vibrate();
            #endif
        }
        
        #endregion
    }
}
