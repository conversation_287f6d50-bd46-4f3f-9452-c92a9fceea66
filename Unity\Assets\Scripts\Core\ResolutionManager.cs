using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 分辨率管理器 - 处理多分辨率适配和屏幕设置
    /// </summary>
    public class ResolutionManager : MonoBehaviour
    {
        [Header("参考分辨率")]
        [SerializeField] private Vector2 referenceResolution = new Vector2(1920, 1080);
        [SerializeField] private CanvasScaler.ScreenMatchMode screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        [SerializeField] private float matchWidthOrHeight = 0.5f;
        
        [Header("支持的分辨率")]
        [SerializeField] private List<Vector2> supportedResolutions = new List<Vector2>
        {
            new Vector2(1920, 1080), // Full HD
            new Vector2(2560, 1440), // 2K
            new Vector2(2340, 1080), // 19.5:9
            new Vector2(2400, 1080), // 20:9
            new Vector2(1280, 720),  // HD
            new Vector2(1600, 900),  // HD+
            new Vector2(2160, 1080), // 18:9
            new Vector2(2880, 1440)  // 3K
        };
        
        [Header("安全区域设置")]
        [SerializeField] private bool enableSafeArea = true;
        [SerializeField] private RectTransform safeAreaPanel;
        [SerializeField] private float minSafeAreaMargin = 20f;
        
        [Header("UI缩放设置")]
        [SerializeField] private float minUIScale = 0.8f;
        [SerializeField] private float maxUIScale = 1.2f;
        [SerializeField] private bool adaptUIToScreenSize = true;
        
        // 单例模式
        public static ResolutionManager Instance { get; private set; }
        
        // 当前屏幕信息
        public Vector2 CurrentResolution { get; private set; }
        public float AspectRatio { get; private set; }
        public float UIScale { get; private set; }
        public Rect SafeArea { get; private set; }
        public DeviceType CurrentDeviceType { get; private set; }
        
        // 事件系统
        public System.Action<Vector2> OnResolutionChanged;
        public System.Action<Rect> OnSafeAreaChanged;
        public System.Action<float> OnUIScaleChanged;
        
        // 组件引用
        private CanvasScaler[] canvasScalers;
        private Camera mainCamera;
        
        // 屏幕信息缓存
        private Vector2 lastScreenSize;
        private Rect lastSafeArea;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeResolutionManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            ApplyResolutionSettings();
        }
        
        private void Update()
        {
            CheckForResolutionChanges();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeResolutionManager()
        {
            // 强制横屏
            SetScreenOrientation();
            
            // 获取组件引用
            canvasScalers = FindObjectsOfType<CanvasScaler>();
            mainCamera = Camera.main;
            
            // 初始化屏幕信息
            UpdateScreenInfo();
            
            // 检测设备类型
            DetectDeviceType();
            
            Debug.Log($"分辨率管理器初始化完成 - 分辨率: {CurrentResolution}, 宽高比: {AspectRatio:F2}");
        }
        
        private void SetScreenOrientation()
        {
            // 设置为横屏模式
            Screen.orientation = ScreenOrientation.LandscapeLeft;
            Screen.autorotateToLandscapeLeft = true;
            Screen.autorotateToLandscapeRight = true;
            Screen.autorotateToPortrait = false;
            Screen.autorotateToPortraitUpsideDown = false;
        }
        
        private void UpdateScreenInfo()
        {
            CurrentResolution = new Vector2(Screen.width, Screen.height);
            AspectRatio = CurrentResolution.x / CurrentResolution.y;
            SafeArea = Screen.safeArea;
            
            lastScreenSize = CurrentResolution;
            lastSafeArea = SafeArea;
        }
        
        private void DetectDeviceType()
        {
            float screenDiagonal = GetScreenDiagonal();
            
            if (screenDiagonal < 5.0f)
            {
                CurrentDeviceType = DeviceType.SmallPhone;
            }
            else if (screenDiagonal < 6.5f)
            {
                CurrentDeviceType = DeviceType.Phone;
            }
            else if (screenDiagonal < 8.0f)
            {
                CurrentDeviceType = DeviceType.LargePhone;
            }
            else
            {
                CurrentDeviceType = DeviceType.Tablet;
            }
            
            Debug.Log($"检测到设备类型: {CurrentDeviceType}, 屏幕对角线: {screenDiagonal:F1}英寸");
        }
        
        private float GetScreenDiagonal()
        {
            float dpi = Screen.dpi > 0 ? Screen.dpi : 160f; // 默认DPI
            float widthInches = Screen.width / dpi;
            float heightInches = Screen.height / dpi;
            return Mathf.Sqrt(widthInches * widthInches + heightInches * heightInches);
        }
        
        #endregion
        
        #region 分辨率适配
        
        private void ApplyResolutionSettings()
        {
            // 计算UI缩放
            CalculateUIScale();
            
            // 应用Canvas缩放设置
            ApplyCanvasScalerSettings();
            
            // 适配安全区域
            if (enableSafeArea)
            {
                AdaptToSafeArea();
            }
            
            // 调整相机设置
            AdjustCameraSettings();
            
            // 触发事件
            OnResolutionChanged?.Invoke(CurrentResolution);
            OnUIScaleChanged?.Invoke(UIScale);
            OnSafeAreaChanged?.Invoke(SafeArea);
        }
        
        private void CalculateUIScale()
        {
            if (!adaptUIToScreenSize)
            {
                UIScale = 1f;
                return;
            }
            
            // 基于屏幕尺寸和设备类型计算UI缩放
            float baseScale = 1f;
            
            switch (CurrentDeviceType)
            {
                case DeviceType.SmallPhone:
                    baseScale = 0.9f;
                    break;
                case DeviceType.Phone:
                    baseScale = 1f;
                    break;
                case DeviceType.LargePhone:
                    baseScale = 1.1f;
                    break;
                case DeviceType.Tablet:
                    baseScale = 1.2f;
                    break;
            }
            
            // 基于分辨率微调
            float resolutionFactor = CurrentResolution.magnitude / referenceResolution.magnitude;
            UIScale = baseScale * Mathf.Sqrt(resolutionFactor);
            
            // 限制在合理范围内
            UIScale = Mathf.Clamp(UIScale, minUIScale, maxUIScale);
        }
        
        private void ApplyCanvasScalerSettings()
        {
            foreach (CanvasScaler scaler in canvasScalers)
            {
                if (scaler == null) continue;
                
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = referenceResolution;
                scaler.screenMatchMode = screenMatchMode;
                scaler.matchWidthOrHeight = matchWidthOrHeight;
                scaler.referencePixelsPerUnit = 100f;
                
                // 应用UI缩放
                if (adaptUIToScreenSize)
                {
                    scaler.scaleFactor = UIScale;
                }
            }
        }
        
        private void AdaptToSafeArea()
        {
            if (safeAreaPanel == null) return;
            
            Rect safeArea = Screen.safeArea;
            Vector2 anchorMin = safeArea.position;
            Vector2 anchorMax = safeArea.position + safeArea.size;
            
            // 转换为相对坐标
            anchorMin.x /= Screen.width;
            anchorMin.y /= Screen.height;
            anchorMax.x /= Screen.width;
            anchorMax.y /= Screen.height;
            
            // 应用最小边距
            float minMarginX = minSafeAreaMargin / Screen.width;
            float minMarginY = minSafeAreaMargin / Screen.height;
            
            anchorMin.x = Mathf.Max(anchorMin.x, minMarginX);
            anchorMin.y = Mathf.Max(anchorMin.y, minMarginY);
            anchorMax.x = Mathf.Min(anchorMax.x, 1f - minMarginX);
            anchorMax.y = Mathf.Min(anchorMax.y, 1f - minMarginY);
            
            // 应用到面板
            safeAreaPanel.anchorMin = anchorMin;
            safeAreaPanel.anchorMax = anchorMax;
            
            Debug.Log($"安全区域适配: {safeArea} -> 锚点: {anchorMin} - {anchorMax}");
        }
        
        private void AdjustCameraSettings()
        {
            if (mainCamera == null) return;
            
            // 根据宽高比调整相机设置
            if (AspectRatio > 2.1f) // 超宽屏
            {
                // 可能需要调整FOV或正交大小
                if (mainCamera.orthographic)
                {
                    mainCamera.orthographicSize *= 1.1f;
                }
            }
            else if (AspectRatio < 1.6f) // 接近正方形
            {
                if (mainCamera.orthographic)
                {
                    mainCamera.orthographicSize *= 0.9f;
                }
            }
        }
        
        #endregion
        
        #region 分辨率检测
        
        private void CheckForResolutionChanges()
        {
            Vector2 currentScreenSize = new Vector2(Screen.width, Screen.height);
            Rect currentSafeArea = Screen.safeArea;
            
            // 检查分辨率变化
            if (currentScreenSize != lastScreenSize)
            {
                Debug.Log($"检测到分辨率变化: {lastScreenSize} -> {currentScreenSize}");
                UpdateScreenInfo();
                ApplyResolutionSettings();
            }
            
            // 检查安全区域变化
            if (currentSafeArea != lastSafeArea)
            {
                Debug.Log($"检测到安全区域变化: {lastSafeArea} -> {currentSafeArea}");
                SafeArea = currentSafeArea;
                lastSafeArea = currentSafeArea;
                
                if (enableSafeArea)
                {
                    AdaptToSafeArea();
                }
                
                OnSafeAreaChanged?.Invoke(SafeArea);
            }
        }
        
        #endregion
        
        #region 分辨率查询
        
        /// <summary>
        /// 获取最佳支持的分辨率
        /// </summary>
        public Vector2 GetBestSupportedResolution()
        {
            Vector2 currentRes = new Vector2(Screen.width, Screen.height);
            Vector2 bestRes = referenceResolution;
            float bestScore = float.MaxValue;
            
            foreach (Vector2 supportedRes in supportedResolutions)
            {
                float score = Vector2.Distance(currentRes, supportedRes);
                if (score < bestScore)
                {
                    bestScore = score;
                    bestRes = supportedRes;
                }
            }
            
            return bestRes;
        }
        
        /// <summary>
        /// 检查是否为超宽屏
        /// </summary>
        public bool IsUltraWideScreen()
        {
            return AspectRatio > 2.0f;
        }
        
        /// <summary>
        /// 检查是否为刘海屏
        /// </summary>
        public bool HasNotch()
        {
            return SafeArea.width < Screen.width || SafeArea.height < Screen.height;
        }
        
        /// <summary>
        /// 获取屏幕密度等级
        /// </summary>
        public ScreenDensity GetScreenDensity()
        {
            float dpi = Screen.dpi > 0 ? Screen.dpi : 160f;
            
            if (dpi < 120) return ScreenDensity.Low;
            else if (dpi < 160) return ScreenDensity.Medium;
            else if (dpi < 240) return ScreenDensity.High;
            else if (dpi < 320) return ScreenDensity.ExtraHigh;
            else if (dpi < 480) return ScreenDensity.XXHigh;
            else return ScreenDensity.XXXHigh;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置参考分辨率
        /// </summary>
        public void SetReferenceResolution(Vector2 resolution)
        {
            referenceResolution = resolution;
            ApplyResolutionSettings();
        }
        
        /// <summary>
        /// 设置屏幕匹配模式
        /// </summary>
        public void SetScreenMatchMode(CanvasScaler.ScreenMatchMode mode, float matchValue)
        {
            screenMatchMode = mode;
            matchWidthOrHeight = matchValue;
            ApplyCanvasScalerSettings();
        }
        
        /// <summary>
        /// 启用/禁用安全区域适配
        /// </summary>
        public void SetSafeAreaEnabled(bool enabled)
        {
            enableSafeArea = enabled;
            if (enabled)
            {
                AdaptToSafeArea();
            }
        }
        
        /// <summary>
        /// 设置UI缩放范围
        /// </summary>
        public void SetUIScaleRange(float min, float max)
        {
            minUIScale = min;
            maxUIScale = max;
            CalculateUIScale();
            ApplyCanvasScalerSettings();
        }
        
        /// <summary>
        /// 强制刷新分辨率设置
        /// </summary>
        public void RefreshResolutionSettings()
        {
            UpdateScreenInfo();
            ApplyResolutionSettings();
        }
        
        /// <summary>
        /// 获取屏幕信息字符串
        /// </summary>
        public string GetScreenInfoString()
        {
            return $"分辨率: {CurrentResolution.x}x{CurrentResolution.y}\n" +
                   $"宽高比: {AspectRatio:F2}\n" +
                   $"DPI: {Screen.dpi:F0}\n" +
                   $"设备类型: {CurrentDeviceType}\n" +
                   $"屏幕密度: {GetScreenDensity()}\n" +
                   $"安全区域: {SafeArea}\n" +
                   $"UI缩放: {UIScale:F2}";
        }
        
        #endregion
    }
    
    /// <summary>
    /// 设备类型枚举
    /// </summary>
    public enum DeviceType
    {
        SmallPhone,  // 小屏手机 (<5英寸)
        Phone,       // 普通手机 (5-6.5英寸)
        LargePhone,  // 大屏手机 (6.5-8英寸)
        Tablet       // 平板 (>8英寸)
    }
    
    /// <summary>
    /// 屏幕密度枚举
    /// </summary>
    public enum ScreenDensity
    {
        Low,        // ~120dpi
        Medium,     // ~160dpi
        High,       // ~240dpi
        ExtraHigh,  // ~320dpi
        XXHigh,     // ~480dpi
        XXXHigh     // ~640dpi
    }
}
