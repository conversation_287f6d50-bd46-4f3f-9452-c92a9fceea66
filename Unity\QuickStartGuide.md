# 迷宫探险 - Unity项目快速启动指南

## 🚀 快速开始（5分钟设置）

### 第1步：创建Unity项目
1. 打开Unity Hub
2. 点击"新建项目"
3. 选择"2D (URP)"模板
4. 项目名称：`MazeAdventure`
5. 点击"创建项目"

### 第2步：导入项目文件
1. 将`Unity/Assets/`文件夹中的所有内容复制到你的Unity项目的`Assets/`文件夹中
2. 等待Unity导入完成（可能有一些编译错误，这是正常的）

### 第3步：运行自动设置
1. 在Unity菜单栏选择：`MazeAdventure > Auto Project Setup`
2. 在弹出的窗口中点击"执行自动设置"
3. 等待设置完成

### 第4步：验证设置
1. 在Hierarchy中应该看到：
   - MainCanvas
   - EventSystem  
   - Managers (包含GameManager、InputManager等)
2. 在Project窗口中应该看到完整的文件夹结构
3. Console中应该显示"项目设置验证通过"

## 📁 项目结构说明

```
Assets/
├── Art/                    # 美术资源（稍后添加）
├── Audio/                  # 音频资源（稍后添加）
├── Scripts/                # 代码文件
│   ├── Core/              # 核心系统（已完成）
│   ├── Gameplay/          # 游戏逻辑（待开发）
│   ├── UI/                # 界面系统（已完成）
│   └── Utils/             # 工具类（已完成）
├── Prefabs/               # 预制体（待创建）
├── Scenes/                # 场景文件
├── Materials/             # 材质文件（待创建）
├── Animations/            # 动画文件（待创建）
└── Settings/              # 配置文件（已创建）
```

## 🎯 下一步开发计划

### 立即可以做的事情：
1. **测试基础功能**：运行游戏，检查管理器是否正常工作
2. **开始制作像素艺术**：使用Aseprite制作32x32角色精灵
3. **学习项目架构**：查看各个管理器脚本的功能

### 接下来的开发顺序：
1. **制作基础像素艺术资源** (1-2天)
   - 玩家角色精灵和动画
   - 基础环境瓦片
   - 道具图标

2. **实现核心游戏逻辑** (2-3天)
   - 玩家移动控制
   - 迷宫生成系统
   - 碰撞检测

3. **集成光照系统** (1-2天)
   - 手电筒效果
   - 动态光照

4. **添加音效和音乐** (1天)
   - 背景音乐
   - 交互音效

5. **性能优化和测试** (1-2天)
   - Android设备测试
   - 性能优化

## 🛠️ 开发工具推荐

### 必需工具：
- **Unity 2022.3 LTS** - 游戏引擎
- **Visual Studio Code** 或 **Visual Studio** - 代码编辑器
- **Aseprite** - 像素艺术制作（推荐）

### 可选工具：
- **Audacity** - 音频编辑
- **GIMP** - 免费图像编辑器
- **Git** - 版本控制

## 📱 Android测试设置

### 准备工作：
1. 安装Android SDK
2. 启用开发者选项和USB调试
3. 连接Android设备

### 构建测试：
1. File > Build Settings
2. 选择Android平台
3. 点击"Build And Run"
4. 选择输出位置
5. 等待构建和安装

## 🎨 像素艺术制作指南

### 角色制作步骤：
1. 打开Aseprite，创建32x32画布
2. 使用项目调色板（16色）
3. 绘制玩家角色基础形状
4. 添加头灯细节
5. 制作4方向移动动画
6. 导出为PNG序列

### 导入Unity设置：
- Texture Type: Sprite (2D and UI)
- Sprite Mode: Multiple（如果是动画序列）
- Pixels Per Unit: 32
- Filter Mode: Point (no filter)
- Compression: None

## 🔧 常见问题解决

### Q: 编译错误怎么办？
A: 
1. 检查是否所有脚本都已导入
2. 确保命名空间正确
3. 暂时注释掉有问题的代码行
4. 重新运行自动设置

### Q: Android构建失败？
A:
1. 检查Android SDK路径设置
2. 确保安装了正确的Build Tools
3. 检查包名格式是否正确

### Q: UI显示不正常？
A:
1. 检查Canvas Scaler设置
2. 确保Reference Resolution为1920x1080
3. 验证安全区域适配是否正确

### Q: 触摸输入无响应？
A:
1. 确保EventSystem存在
2. 检查InputManager是否正确初始化
3. 在Android设备上测试（编辑器中触摸模拟有限）

## 📞 获取帮助

### 学习资源：
- Unity官方文档
- Unity Learn平台
- 像素艺术教程网站

### 调试工具：
- Unity Console（查看日志）
- Unity Profiler（性能分析）
- Android Logcat（Android调试）

## ✅ 检查清单

在开始下一阶段开发前，确保：

- [ ] Unity项目创建成功
- [ ] 所有脚本文件已导入
- [ ] 自动设置已完成
- [ ] 项目验证通过
- [ ] 可以成功构建到Android
- [ ] 基础管理器系统正常工作
- [ ] 文件夹结构完整
- [ ] GameConfig资源已创建

完成这些检查后，你就可以开始制作像素艺术资源了！

## 🎉 恭喜！

你现在有了一个完整的Unity手机游戏项目框架！这个框架包含：

- ✅ 专业的项目架构
- ✅ 完整的管理器系统  
- ✅ 手机触屏输入支持
- ✅ 多分辨率适配
- ✅ Android平台优化
- ✅ 数据持久化系统
- ✅ 响应式UI系统

接下来就是发挥创意，制作精美的像素艺术和有趣的游戏玩法了！
