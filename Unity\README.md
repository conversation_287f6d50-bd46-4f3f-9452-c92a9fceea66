# 迷宫探险 - Unity手机版

一款2D像素风格的生存冒险游戏，专为Android手机横屏设计。

## 项目概述

玩家扮演一名头戴头灯的探险者，在黑暗的迷宫中探索，需要通过收集电池维持头灯电量，避开各种机关陷阱，寻找出口。

## 技术栈

- **引擎**: Unity 2022.3 LTS
- **平台**: Android (API Level 21+)
- **美术风格**: 32x32像素艺术
- **分辨率**: 1920x1080基准，支持多分辨率适配
- **操作方式**: 虚拟摇杆 + 触屏交互

## 项目结构

```
MazeAdventure/
├── Assets/
│   ├── Art/                    # 美术资源
│   │   ├── Characters/         # 角色精灵
│   │   ├── Environment/        # 环境贴图
│   │   ├── Items/             # 道具图标
│   │   ├── UI/                # UI元素
│   │   └── Effects/           # 特效资源
│   ├── Audio/                 # 音频资源
│   │   ├── Music/             # 背景音乐
│   │   ├── SFX/               # 音效
│   │   └── Ambient/           # 环境音
│   ├── Scripts/               # 代码文件
│   │   ├── Core/              # 核心系统
│   │   ├── Gameplay/          # 游戏逻辑
│   │   ├── UI/                # 界面系统
│   │   ├── Audio/             # 音频管理
│   │   └── Utils/             # 工具类
│   ├── Prefabs/               # 预制体
│   │   ├── Characters/        # 角色预制体
│   │   ├── Environment/       # 环境预制体
│   │   ├── Items/             # 道具预制体
│   │   └── UI/                # UI预制体
│   ├── Scenes/                # 场景文件
│   │   ├── MainMenu.unity     # 主菜单
│   │   ├── GamePlay.unity     # 游戏场景
│   │   └── Settings.unity     # 设置界面
│   ├── Materials/             # 材质文件
│   ├── Animations/            # 动画文件
│   └── Resources/             # 运行时加载资源
└── ProjectSettings/           # 项目设置
```

## 开发规范

### 代码规范
- 使用C#编程语言
- 遵循Unity官方命名规范
- 类名使用PascalCase
- 方法名使用PascalCase
- 变量名使用camelCase
- 常量使用UPPER_CASE

### 文件命名规范
- 脚本文件：PascalCase.cs
- 预制体：PascalCase.prefab
- 场景文件：PascalCase.unity
- 美术资源：snake_case

## 核心系统架构

### 1. 游戏管理器 (GameManager)
- 游戏状态管理
- 场景切换
- 数据持久化

### 2. 玩家控制系统 (PlayerController)
- 移动控制
- 输入处理
- 状态管理

### 3. 迷宫系统 (MazeSystem)
- 迷宫生成
- 碰撞检测
- 路径查找

### 4. 光照系统 (LightingSystem)
- 手电筒效果
- 动态光照
- 视野管理

### 5. UI系统 (UISystem)
- HUD显示
- 菜单管理
- 触屏交互

### 6. 音频系统 (AudioSystem)
- 背景音乐
- 音效播放
- 音量控制

## 开发计划

1. **项目初始化** - 创建Unity项目，配置基础设置
2. **美术资源** - 制作32x32像素艺术资源
3. **核心系统** - 实现基础游戏逻辑
4. **UI界面** - 设计手机适配的用户界面
5. **优化测试** - 性能优化和设备兼容性测试
6. **打包发布** - Android平台打包和发布准备

## 技术要求

### 最低系统要求
- Android 5.0 (API Level 21)
- RAM: 2GB
- 存储空间: 100MB
- OpenGL ES 3.0

### 推荐系统要求
- Android 8.0 (API Level 26)
- RAM: 4GB
- 存储空间: 200MB
- OpenGL ES 3.2

## 性能目标

- 帧率: 60 FPS (稳定)
- 启动时间: < 3秒
- 内存占用: < 200MB
- 电池续航: 优化功耗

## 版本控制

使用Git进行版本控制，建议的分支策略：
- `main`: 主分支，稳定版本
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支
