using UnityEngine;
using UnityEngine.Rendering.Universal;
using System.Collections.Generic;
using System.Collections;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 光照优化器 - 专门优化2D光照系统在手机设备上的性能
    /// </summary>
    public class LightingOptimizer : MonoBehaviour
    {
        [Header("性能设置")]
        [SerializeField] private int maxActiveLights = 4;
        [SerializeField] private float lightCullingDistance = 12f;
        [SerializeField] private float shadowCullingDistance = 8f;
        [SerializeField] private int maxShadowCasters = 20;
        
        [Header("质量设置")]
        [SerializeField] private LightingQuality targetQuality = LightingQuality.Medium;
        [SerializeField] private bool adaptiveQuality = true;
        [SerializeField] private float performanceCheckInterval = 2f;
        [SerializeField] private float targetFrameRate = 30f;
        
        [Header("LOD设置")]
        [SerializeField] private float nearLODDistance = 5f;
        [SerializeField] private float farLODDistance = 10f;
        [SerializeField] private bool enableLightLOD = true;
        [SerializeField] private bool enableShadowLOD = true;
        
        [Header("批处理优化")]
        [SerializeField] private bool enableLightBatching = true;
        [SerializeField] private int batchSize = 8;
        [SerializeField] private bool enableInstancing = true;
        
        [Header("调试选项")]
        [SerializeField] private bool showPerformanceStats = false;
        [SerializeField] private bool enableProfiling = false;
        
        // 单例模式
        public static LightingOptimizer Instance { get; private set; }
        
        // 性能监控
        public float CurrentFPS { get; private set; }
        public int ActiveLightCount { get; private set; }
        public int ActiveShadowCasterCount { get; private set; }
        public LightingQuality CurrentQuality { get; private set; }
        
        // 光照管理
        private List<OptimizedLight> optimizedLights;
        private List<OptimizedShadowCaster> optimizedShadowCasters;
        private Queue<Light2D> lightPool;
        private Queue<ShadowCaster2D> shadowCasterPool;
        
        // 性能监控
        private float frameTimeAccumulator;
        private int frameCount;
        private float lastPerformanceCheck;
        private Coroutine performanceCoroutine;
        
        // 相机引用
        private Camera mainCamera;
        private Transform cameraTransform;
        
        // 事件系统
        public System.Action<LightingQuality> OnQualityChanged;
        public System.Action<float> OnPerformanceUpdated;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeLightingOptimizer();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupLightingOptimizer();
        }
        
        private void Update()
        {
            UpdatePerformanceMetrics();
            UpdateLightCulling();
            UpdateShadowCulling();
            
            if (enableLightLOD)
            {
                UpdateLightLOD();
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeLightingOptimizer()
        {
            optimizedLights = new List<OptimizedLight>();
            optimizedShadowCasters = new List<OptimizedShadowCaster>();
            lightPool = new Queue<Light2D>();
            shadowCasterPool = new Queue<ShadowCaster2D>();
            
            mainCamera = Camera.main;
            if (mainCamera == null)
            {
                mainCamera = FindObjectOfType<Camera>();
            }
            
            if (mainCamera != null)
            {
                cameraTransform = mainCamera.transform;
            }
            
            CurrentQuality = targetQuality;
        }
        
        private void SetupLightingOptimizer()
        {
            // 应用初始质量设置
            ApplyQualitySettings(targetQuality);
            
            // 查找并优化现有光源
            OptimizeExistingLights();
            OptimizeExistingShadowCasters();
            
            // 开始性能监控
            if (adaptiveQuality)
            {
                performanceCoroutine = StartCoroutine(PerformanceMonitorCoroutine());
            }
            
            Debug.Log($"光照优化器初始化完成，目标质量: {targetQuality}");
        }
        
        private void OptimizeExistingLights()
        {
            Light2D[] allLights = FindObjectsOfType<Light2D>();
            
            foreach (Light2D light in allLights)
            {
                if (light != null)
                {
                    RegisterLight(light);
                }
            }
            
            Debug.Log($"优化了 {optimizedLights.Count} 个光源");
        }
        
        private void OptimizeExistingShadowCasters()
        {
            ShadowCaster2D[] allCasters = FindObjectsOfType<ShadowCaster2D>();
            
            foreach (ShadowCaster2D caster in allCasters)
            {
                if (caster != null)
                {
                    RegisterShadowCaster(caster);
                }
            }
            
            Debug.Log($"优化了 {optimizedShadowCasters.Count} 个阴影投射器");
        }
        
        #endregion
        
        #region 质量设置
        
        /// <summary>
        /// 应用光照质量设置
        /// </summary>
        public void ApplyQualitySettings(LightingQuality quality)
        {
            CurrentQuality = quality;
            
            switch (quality)
            {
                case LightingQuality.Low:
                    ApplyLowQualitySettings();
                    break;
                case LightingQuality.Medium:
                    ApplyMediumQualitySettings();
                    break;
                case LightingQuality.High:
                    ApplyHighQualitySettings();
                    break;
            }
            
            OnQualityChanged?.Invoke(quality);
            Debug.Log($"应用光照质量设置: {quality}");
        }
        
        private void ApplyLowQualitySettings()
        {
            maxActiveLights = 2;
            lightCullingDistance = 8f;
            shadowCullingDistance = 5f;
            maxShadowCasters = 10;
            enableShadowLOD = true;
            
            // 降低光照分辨率
            SetLightResolution(0.5f);
            
            // 禁用一些视觉效果
            DisableExpensiveEffects();
        }
        
        private void ApplyMediumQualitySettings()
        {
            maxActiveLights = 4;
            lightCullingDistance = 12f;
            shadowCullingDistance = 8f;
            maxShadowCasters = 20;
            enableShadowLOD = true;
            
            // 标准光照分辨率
            SetLightResolution(0.75f);
        }
        
        private void ApplyHighQualitySettings()
        {
            maxActiveLights = 8;
            lightCullingDistance = 15f;
            shadowCullingDistance = 12f;
            maxShadowCasters = 40;
            enableShadowLOD = false;
            
            // 高光照分辨率
            SetLightResolution(1f);
        }
        
        private void SetLightResolution(float scale)
        {
            // 这里可以调整URP的光照分辨率设置
            // 需要通过URP Asset或代码设置
        }
        
        private void DisableExpensiveEffects()
        {
            // 禁用一些昂贵的光照效果
            foreach (OptimizedLight optLight in optimizedLights)
            {
                if (optLight.light != null)
                {
                    // 简化光照设置
                    optLight.light.lightOrder = 0; // 降低渲染优先级
                }
            }
        }
        
        #endregion
        
        #region 光源管理
        
        /// <summary>
        /// 注册光源进行优化
        /// </summary>
        public void RegisterLight(Light2D light)
        {
            if (light == null) return;
            
            OptimizedLight optLight = new OptimizedLight
            {
                light = light,
                originalIntensity = light.intensity,
                originalRadius = light.pointLightOuterRadius,
                isActive = light.enabled,
                lastDistance = float.MaxValue
            };
            
            optimizedLights.Add(optLight);
        }
        
        /// <summary>
        /// 注销光源
        /// </summary>
        public void UnregisterLight(Light2D light)
        {
            optimizedLights.RemoveAll(opt => opt.light == light);
        }
        
        private void UpdateLightCulling()
        {
            if (cameraTransform == null) return;
            
            Vector3 cameraPos = cameraTransform.position;
            int activeLights = 0;
            
            // 按距离排序光源
            optimizedLights.Sort((a, b) => 
            {
                float distA = Vector3.Distance(cameraPos, a.light.transform.position);
                float distB = Vector3.Distance(cameraPos, b.light.transform.position);
                return distA.CompareTo(distB);
            });
            
            foreach (OptimizedLight optLight in optimizedLights)
            {
                if (optLight.light == null) continue;
                
                float distance = Vector3.Distance(cameraPos, optLight.light.transform.position);
                optLight.lastDistance = distance;
                
                bool shouldBeActive = distance <= lightCullingDistance && activeLights < maxActiveLights;
                
                if (optLight.light.enabled != shouldBeActive)
                {
                    optLight.light.enabled = shouldBeActive;
                    optLight.isActive = shouldBeActive;
                }
                
                if (shouldBeActive)
                {
                    activeLights++;
                }
            }
            
            ActiveLightCount = activeLights;
        }
        
        private void UpdateLightLOD()
        {
            if (cameraTransform == null) return;
            
            Vector3 cameraPos = cameraTransform.position;
            
            foreach (OptimizedLight optLight in optimizedLights)
            {
                if (optLight.light == null || !optLight.isActive) continue;
                
                float distance = optLight.lastDistance;
                float lodFactor = CalculateLODFactor(distance);
                
                // 根据距离调整光照强度和范围
                optLight.light.intensity = optLight.originalIntensity * lodFactor;
                optLight.light.pointLightOuterRadius = optLight.originalRadius * lodFactor;
            }
        }
        
        private float CalculateLODFactor(float distance)
        {
            if (distance <= nearLODDistance)
            {
                return 1f; // 全质量
            }
            else if (distance >= farLODDistance)
            {
                return 0.3f; // 最低质量
            }
            else
            {
                // 线性插值
                float t = (distance - nearLODDistance) / (farLODDistance - nearLODDistance);
                return Mathf.Lerp(1f, 0.3f, t);
            }
        }
        
        #endregion
        
        #region 阴影优化
        
        /// <summary>
        /// 注册阴影投射器进行优化
        /// </summary>
        public void RegisterShadowCaster(ShadowCaster2D shadowCaster)
        {
            if (shadowCaster == null) return;
            
            OptimizedShadowCaster optCaster = new OptimizedShadowCaster
            {
                shadowCaster = shadowCaster,
                isActive = shadowCaster.enabled,
                lastDistance = float.MaxValue
            };
            
            optimizedShadowCasters.Add(optCaster);
        }
        
        /// <summary>
        /// 注销阴影投射器
        /// </summary>
        public void UnregisterShadowCaster(ShadowCaster2D shadowCaster)
        {
            optimizedShadowCasters.RemoveAll(opt => opt.shadowCaster == shadowCaster);
        }
        
        private void UpdateShadowCulling()
        {
            if (cameraTransform == null) return;
            
            Vector3 cameraPos = cameraTransform.position;
            int activeShadowCasters = 0;
            
            // 按距离排序阴影投射器
            optimizedShadowCasters.Sort((a, b) => 
            {
                float distA = Vector3.Distance(cameraPos, a.shadowCaster.transform.position);
                float distB = Vector3.Distance(cameraPos, b.shadowCaster.transform.position);
                return distA.CompareTo(distB);
            });
            
            foreach (OptimizedShadowCaster optCaster in optimizedShadowCasters)
            {
                if (optCaster.shadowCaster == null) continue;
                
                float distance = Vector3.Distance(cameraPos, optCaster.shadowCaster.transform.position);
                optCaster.lastDistance = distance;
                
                bool shouldBeActive = distance <= shadowCullingDistance && 
                                    activeShadowCasters < maxShadowCasters;
                
                if (optCaster.shadowCaster.enabled != shouldBeActive)
                {
                    optCaster.shadowCaster.enabled = shouldBeActive;
                    optCaster.isActive = shouldBeActive;
                }
                
                if (shouldBeActive)
                {
                    activeShadowCasters++;
                }
            }
            
            ActiveShadowCasterCount = activeShadowCasters;
        }
        
        #endregion
        
        #region 性能监控
        
        private void UpdatePerformanceMetrics()
        {
            frameTimeAccumulator += Time.unscaledDeltaTime;
            frameCount++;
            
            if (Time.time - lastPerformanceCheck >= 1f)
            {
                CurrentFPS = frameCount / (Time.time - lastPerformanceCheck);
                frameTimeAccumulator = 0f;
                frameCount = 0;
                lastPerformanceCheck = Time.time;
                
                OnPerformanceUpdated?.Invoke(CurrentFPS);
            }
        }
        
        private IEnumerator PerformanceMonitorCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(performanceCheckInterval);
                
                if (adaptiveQuality)
                {
                    CheckAndAdjustQuality();
                }
            }
        }
        
        private void CheckAndAdjustQuality()
        {
            if (CurrentFPS < targetFrameRate * 0.8f)
            {
                // 性能不足，降低质量
                if (CurrentQuality == LightingQuality.High)
                {
                    ApplyQualitySettings(LightingQuality.Medium);
                }
                else if (CurrentQuality == LightingQuality.Medium)
                {
                    ApplyQualitySettings(LightingQuality.Low);
                }
            }
            else if (CurrentFPS > targetFrameRate * 1.2f)
            {
                // 性能充足，可以提高质量
                if (CurrentQuality == LightingQuality.Low)
                {
                    ApplyQualitySettings(LightingQuality.Medium);
                }
                else if (CurrentQuality == LightingQuality.Medium && targetQuality == LightingQuality.High)
                {
                    ApplyQualitySettings(LightingQuality.High);
                }
            }
        }
        
        #endregion
        
        #region 批处理优化
        
        /// <summary>
        /// 启用光照批处理
        /// </summary>
        public void EnableLightBatching(bool enable)
        {
            enableLightBatching = enable;
            
            if (enable)
            {
                OptimizeLightBatching();
            }
        }
        
        private void OptimizeLightBatching()
        {
            // 将相似的光源分组进行批处理
            var lightGroups = new Dictionary<string, List<OptimizedLight>>();
            
            foreach (OptimizedLight optLight in optimizedLights)
            {
                if (optLight.light == null) continue;
                
                string groupKey = GetLightGroupKey(optLight.light);
                
                if (!lightGroups.ContainsKey(groupKey))
                {
                    lightGroups[groupKey] = new List<OptimizedLight>();
                }
                
                lightGroups[groupKey].Add(optLight);
            }
            
            // 对每组光源进行批处理优化
            foreach (var group in lightGroups)
            {
                if (group.Value.Count >= batchSize)
                {
                    OptimizeLightGroup(group.Value);
                }
            }
        }
        
        private string GetLightGroupKey(Light2D light)
        {
            // 根据光源属性生成分组键
            return $"{light.lightType}_{light.color}_{light.intensity:F1}";
        }
        
        private void OptimizeLightGroup(List<OptimizedLight> lightGroup)
        {
            // 对光源组进行批处理优化
            // 这里可以实现具体的批处理逻辑
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 强制更新所有光照优化
        /// </summary>
        public void ForceUpdateOptimization()
        {
            UpdateLightCulling();
            UpdateShadowCulling();
            
            if (enableLightLOD)
            {
                UpdateLightLOD();
            }
        }
        
        /// <summary>
        /// 设置目标帧率
        /// </summary>
        public void SetTargetFrameRate(float frameRate)
        {
            targetFrameRate = frameRate;
        }
        
        /// <summary>
        /// 启用/禁用自适应质量
        /// </summary>
        public void SetAdaptiveQuality(bool enabled)
        {
            adaptiveQuality = enabled;
            
            if (enabled && performanceCoroutine == null)
            {
                performanceCoroutine = StartCoroutine(PerformanceMonitorCoroutine());
            }
            else if (!enabled && performanceCoroutine != null)
            {
                StopCoroutine(performanceCoroutine);
                performanceCoroutine = null;
            }
        }
        
        /// <summary>
        /// 获取优化统计信息
        /// </summary>
        public string GetOptimizationStats()
        {
            return $"光照优化统计:\n" +
                   $"当前FPS: {CurrentFPS:F1}\n" +
                   $"活跃光源: {ActiveLightCount}/{optimizedLights.Count}\n" +
                   $"活跃阴影: {ActiveShadowCasterCount}/{optimizedShadowCasters.Count}\n" +
                   $"当前质量: {CurrentQuality}\n" +
                   $"自适应质量: {(adaptiveQuality ? "启用" : "禁用")}\n" +
                   $"光照LOD: {(enableLightLOD ? "启用" : "禁用")}\n" +
                   $"批处理: {(enableLightBatching ? "启用" : "禁用")}";
        }
        
        /// <summary>
        /// 清理无效的优化对象
        /// </summary>
        public void CleanupOptimizedObjects()
        {
            optimizedLights.RemoveAll(opt => opt.light == null);
            optimizedShadowCasters.RemoveAll(opt => opt.shadowCaster == null);
        }
        
        #endregion
        
        #region 调试
        
        private void OnGUI()
        {
            if (showPerformanceStats && Application.isPlaying)
            {
                GUILayout.BeginArea(new Rect(Screen.width - 250, Screen.height - 200, 240, 190));
                GUILayout.Box("光照性能统计");
                
                GUILayout.Label($"FPS: {CurrentFPS:F1}");
                GUILayout.Label($"质量: {CurrentQuality}");
                GUILayout.Label($"活跃光源: {ActiveLightCount}");
                GUILayout.Label($"活跃阴影: {ActiveShadowCasterCount}");
                
                if (GUILayout.Button("降低质量"))
                {
                    LightingQuality newQuality = CurrentQuality == LightingQuality.High ? 
                        LightingQuality.Medium : LightingQuality.Low;
                    ApplyQualitySettings(newQuality);
                }
                
                if (GUILayout.Button("提高质量"))
                {
                    LightingQuality newQuality = CurrentQuality == LightingQuality.Low ? 
                        LightingQuality.Medium : LightingQuality.High;
                    ApplyQualitySettings(newQuality);
                }
                
                GUILayout.EndArea();
            }
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            if (performanceCoroutine != null)
            {
                StopCoroutine(performanceCoroutine);
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 光照质量枚举
    /// </summary>
    public enum LightingQuality
    {
        Low,    // 低质量
        Medium, // 中等质量
        High    // 高质量
    }
    
    /// <summary>
    /// 优化的光源数据
    /// </summary>
    [System.Serializable]
    public class OptimizedLight
    {
        public Light2D light;
        public float originalIntensity;
        public float originalRadius;
        public bool isActive;
        public float lastDistance;
    }
    
    /// <summary>
    /// 优化的阴影投射器数据
    /// </summary>
    [System.Serializable]
    public class OptimizedShadowCaster
    {
        public ShadowCaster2D shadowCaster;
        public bool isActive;
        public float lastDistance;
    }
}
