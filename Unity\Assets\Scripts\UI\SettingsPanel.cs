using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;
using MazeAdventure.Core;
using MazeAdventure.Gameplay;
using MazeAdventure.Audio;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 设置面板 - 管理游戏的各种设置选项
    /// </summary>
    public class SettingsPanel : MonoBehaviour
    {
        [Header("音频设置")]
        [SerializeField] private Slider masterVolumeSlider;
        [SerializeField] private Text masterVolumeText;
        [SerializeField] private Slider musicVolumeSlider;
        [SerializeField] private Text musicVolumeText;
        [SerializeField] private Slider sfxVolumeSlider;
        [SerializeField] private Text sfxVolumeText;
        [SerializeField] private Toggle muteToggle;
        [SerializeField] private Button audioTestButton;
        
        [Header("画质设置")]
        [SerializeField] private Dropdown qualityDropdown;
        [SerializeField] private Toggle vsyncToggle;
        [SerializeField] private Dropdown targetFrameRateDropdown;
        [SerializeField] private Toggle lightingToggle;
        [SerializeField] private Toggle shadowsToggle;
        [SerializeField] private Slider lightingQualitySlider;
        [SerializeField] private Text lightingQualityText;
        
        [Header("控制设置")]
        [SerializeField] private Slider joystickSensitivitySlider;
        [SerializeField] private Text joystickSensitivityText;
        [SerializeField] private Toggle joystickVisibilityToggle;
        [SerializeField] private Dropdown joystickPositionDropdown;
        [SerializeField] private Toggle vibrationToggle;
        [SerializeField] private Slider vibrationIntensitySlider;
        [SerializeField] private Text vibrationIntensityText;
        
        [Header("游戏设置")]
        [SerializeField] private Dropdown difficultyDropdown;
        [SerializeField] private Toggle autoSaveToggle;
        [SerializeField] private Slider autoSaveIntervalSlider;
        [SerializeField] private Text autoSaveIntervalText;
        [SerializeField] private Toggle showFPSToggle;
        [SerializeField] private Toggle enableTutorialToggle;
        
        [Header("界面控制")]
        [SerializeField] private Button resetToDefaultButton;
        [SerializeField] private Button applyButton;
        [SerializeField] private Button cancelButton;
        [SerializeField] private Button backButton;
        
        [Header("预览区域")]
        [SerializeField] private GameObject previewArea;
        [SerializeField] private Text previewText;
        [SerializeField] private Image previewImage;
        
        // 设置数据
        private SettingsData currentSettings;
        private SettingsData originalSettings;
        
        // 预览更新
        private bool enableRealTimePreview = true;
        private float previewUpdateDelay = 0.1f;
        private float lastPreviewUpdate;
        
        // 事件系统
        public System.Action OnSettingsChanged;
        public System.Action OnSettingsApplied;
        public System.Action OnSettingsCancelled;
        
        #region Unity生命周期
        
        private void Start()
        {
            InitializeSettingsPanel();
            LoadCurrentSettings();
            SetupEventListeners();
        }
        
        private void Update()
        {
            if (enableRealTimePreview && Time.time - lastPreviewUpdate > previewUpdateDelay)
            {
                UpdatePreview();
                lastPreviewUpdate = Time.time;
            }
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeSettingsPanel()
        {
            // 初始化下拉菜单选项
            SetupDropdownOptions();
            
            // 设置按钮事件
            SetupButtonEvents();
            
            // 创建设置数据对象
            currentSettings = new SettingsData();
            originalSettings = new SettingsData();
            
            Debug.Log("设置面板初始化完成");
        }
        
        private void SetupDropdownOptions()
        {
            // 画质选项
            if (qualityDropdown != null)
            {
                qualityDropdown.options.Clear();
                qualityDropdown.options.Add(new Dropdown.OptionData("低"));
                qualityDropdown.options.Add(new Dropdown.OptionData("中"));
                qualityDropdown.options.Add(new Dropdown.OptionData("高"));
                qualityDropdown.RefreshShownValue();
            }
            
            // 帧率选项
            if (targetFrameRateDropdown != null)
            {
                targetFrameRateDropdown.options.Clear();
                targetFrameRateDropdown.options.Add(new Dropdown.OptionData("30 FPS"));
                targetFrameRateDropdown.options.Add(new Dropdown.OptionData("60 FPS"));
                targetFrameRateDropdown.options.Add(new Dropdown.OptionData("120 FPS"));
                targetFrameRateDropdown.RefreshShownValue();
            }
            
            // 摇杆位置选项
            if (joystickPositionDropdown != null)
            {
                joystickPositionDropdown.options.Clear();
                joystickPositionDropdown.options.Add(new Dropdown.OptionData("左下"));
                joystickPositionDropdown.options.Add(new Dropdown.OptionData("左上"));
                joystickPositionDropdown.options.Add(new Dropdown.OptionData("右下"));
                joystickPositionDropdown.options.Add(new Dropdown.OptionData("右上"));
                joystickPositionDropdown.RefreshShownValue();
            }
            
            // 难度选项
            if (difficultyDropdown != null)
            {
                difficultyDropdown.options.Clear();
                difficultyDropdown.options.Add(new Dropdown.OptionData("简单"));
                difficultyDropdown.options.Add(new Dropdown.OptionData("普通"));
                difficultyDropdown.options.Add(new Dropdown.OptionData("困难"));
                difficultyDropdown.options.Add(new Dropdown.OptionData("专家"));
                difficultyDropdown.RefreshShownValue();
            }
        }
        
        private void SetupButtonEvents()
        {
            if (resetToDefaultButton != null)
                resetToDefaultButton.onClick.AddListener(OnResetToDefaultClicked);
            if (applyButton != null)
                applyButton.onClick.AddListener(OnApplyClicked);
            if (cancelButton != null)
                cancelButton.onClick.AddListener(OnCancelClicked);
            if (backButton != null)
                backButton.onClick.AddListener(OnBackClicked);
            if (audioTestButton != null)
                audioTestButton.onClick.AddListener(OnAudioTestClicked);
        }
        
        private void SetupEventListeners()
        {
            // 音频设置事件
            if (masterVolumeSlider != null)
                masterVolumeSlider.onValueChanged.AddListener(OnMasterVolumeChanged);
            if (musicVolumeSlider != null)
                musicVolumeSlider.onValueChanged.AddListener(OnMusicVolumeChanged);
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.onValueChanged.AddListener(OnSFXVolumeChanged);
            if (muteToggle != null)
                muteToggle.onValueChanged.AddListener(OnMuteToggleChanged);
            
            // 画质设置事件
            if (qualityDropdown != null)
                qualityDropdown.onValueChanged.AddListener(OnQualityChanged);
            if (vsyncToggle != null)
                vsyncToggle.onValueChanged.AddListener(OnVSyncToggleChanged);
            if (targetFrameRateDropdown != null)
                targetFrameRateDropdown.onValueChanged.AddListener(OnTargetFrameRateChanged);
            if (lightingToggle != null)
                lightingToggle.onValueChanged.AddListener(OnLightingToggleChanged);
            if (shadowsToggle != null)
                shadowsToggle.onValueChanged.AddListener(OnShadowsToggleChanged);
            if (lightingQualitySlider != null)
                lightingQualitySlider.onValueChanged.AddListener(OnLightingQualityChanged);
            
            // 控制设置事件
            if (joystickSensitivitySlider != null)
                joystickSensitivitySlider.onValueChanged.AddListener(OnJoystickSensitivityChanged);
            if (joystickVisibilityToggle != null)
                joystickVisibilityToggle.onValueChanged.AddListener(OnJoystickVisibilityChanged);
            if (joystickPositionDropdown != null)
                joystickPositionDropdown.onValueChanged.AddListener(OnJoystickPositionChanged);
            if (vibrationToggle != null)
                vibrationToggle.onValueChanged.AddListener(OnVibrationToggleChanged);
            if (vibrationIntensitySlider != null)
                vibrationIntensitySlider.onValueChanged.AddListener(OnVibrationIntensityChanged);
            
            // 游戏设置事件
            if (difficultyDropdown != null)
                difficultyDropdown.onValueChanged.AddListener(OnDifficultyChanged);
            if (autoSaveToggle != null)
                autoSaveToggle.onValueChanged.AddListener(OnAutoSaveToggleChanged);
            if (autoSaveIntervalSlider != null)
                autoSaveIntervalSlider.onValueChanged.AddListener(OnAutoSaveIntervalChanged);
            if (showFPSToggle != null)
                showFPSToggle.onValueChanged.AddListener(OnShowFPSToggleChanged);
            if (enableTutorialToggle != null)
                enableTutorialToggle.onValueChanged.AddListener(OnTutorialToggleChanged);
        }
        
        #endregion
        
        #region 设置加载和保存
        
        /// <summary>
        /// 加载当前设置
        /// </summary>
        public void LoadCurrentSettings()
        {
            // 从各个系统获取当前设置
            LoadAudioSettings();
            LoadGraphicsSettings();
            LoadControlSettings();
            LoadGameSettings();
            
            // 备份原始设置
            originalSettings.CopyFrom(currentSettings);
            
            // 更新UI显示
            UpdateUIFromSettings();
        }
        
        private void LoadAudioSettings()
        {
            if (AudioManager.Instance != null)
            {
                currentSettings.masterVolume = AudioManager.Instance.masterVolume;
                currentSettings.musicVolume = AudioManager.Instance.musicVolume;
                currentSettings.sfxVolume = AudioManager.Instance.sfxVolume;
                currentSettings.isMuted = AudioManager.Instance.isMuted;
            }
        }
        
        private void LoadGraphicsSettings()
        {
            currentSettings.qualityLevel = QualitySettings.GetQualityLevel();
            currentSettings.vsyncEnabled = QualitySettings.vSyncCount > 0;
            currentSettings.targetFrameRate = Application.targetFrameRate;
            
            if (LightingOptimizer.Instance != null)
            {
                currentSettings.lightingEnabled = true; // 根据实际情况设置
                currentSettings.shadowsEnabled = true;
                currentSettings.lightingQuality = (int)LightingOptimizer.Instance.CurrentQuality;
            }
        }
        
        private void LoadControlSettings()
        {
            if (VirtualJoystick.Instance != null)
            {
                currentSettings.joystickSensitivity = VirtualJoystick.Instance.sensitivity;
                currentSettings.joystickVisible = VirtualJoystick.Instance.alwaysVisible;
            }
            
            currentSettings.vibrationEnabled = true; // 根据实际情况设置
            currentSettings.vibrationIntensity = 1f;
        }
        
        private void LoadGameSettings()
        {
            if (GameManager.Instance != null)
            {
                GameConfig config = GameManager.Instance.GetGameConfig();
                if (config != null)
                {
                    currentSettings.difficulty = (int)config.difficulty;
                    currentSettings.autoSaveEnabled = GameManager.Instance.autoSave;
                    currentSettings.autoSaveInterval = GameManager.Instance.saveInterval;
                }
            }
            
            currentSettings.showFPS = false; // 根据实际情况设置
            currentSettings.tutorialEnabled = true;
        }
        
        /// <summary>
        /// 应用设置
        /// </summary>
        public void ApplySettings()
        {
            ApplyAudioSettings();
            ApplyGraphicsSettings();
            ApplyControlSettings();
            ApplyGameSettings();
            
            // 保存设置到本地
            SaveSettingsToPlayerPrefs();
            
            OnSettingsApplied?.Invoke();
            
            Debug.Log("设置已应用");
        }
        
        private void ApplyAudioSettings()
        {
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.SetMasterVolume(currentSettings.masterVolume);
                AudioManager.Instance.SetMusicVolume(currentSettings.musicVolume);
                AudioManager.Instance.SetSFXVolume(currentSettings.sfxVolume);
                AudioManager.Instance.SetMute(currentSettings.isMuted);
            }
        }
        
        private void ApplyGraphicsSettings()
        {
            QualitySettings.SetQualityLevel(currentSettings.qualityLevel);
            QualitySettings.vSyncCount = currentSettings.vsyncEnabled ? 1 : 0;
            Application.targetFrameRate = currentSettings.targetFrameRate;
            
            if (LightingOptimizer.Instance != null)
            {
                LightingOptimizer.Instance.ApplyQualitySettings((LightingQuality)currentSettings.lightingQuality);
            }
            
            if (LightingRenderer.Instance != null)
            {
                LightingRenderer.Instance.SetShadowsEnabled(currentSettings.shadowsEnabled);
            }
        }
        
        private void ApplyControlSettings()
        {
            if (VirtualJoystick.Instance != null)
            {
                VirtualJoystick.Instance.SetSensitivity(currentSettings.joystickSensitivity);
                VirtualJoystick.Instance.SetAlwaysVisible(currentSettings.joystickVisible);
            }
            
            // 应用震动设置
            if (TouchInteractionManager.Instance != null)
            {
                TouchInteractionManager.Instance.SetVibrationEnabled(currentSettings.vibrationEnabled);
                TouchInteractionManager.Instance.SetVibrationIntensity(currentSettings.vibrationIntensity);
            }
        }
        
        private void ApplyGameSettings()
        {
            if (GameManager.Instance != null)
            {
                GameManager.Instance.autoSave = currentSettings.autoSaveEnabled;
                GameManager.Instance.saveInterval = currentSettings.autoSaveInterval;
                
                // 更新游戏配置
                GameConfig config = GameManager.Instance.GetGameConfig();
                if (config != null)
                {
                    config.difficulty = (DifficultyLevel)currentSettings.difficulty;
                }
            }
        }
        
        private void SaveSettingsToPlayerPrefs()
        {
            // 音频设置
            PlayerPrefs.SetFloat("MasterVolume", currentSettings.masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", currentSettings.musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", currentSettings.sfxVolume);
            PlayerPrefs.SetInt("AudioMuted", currentSettings.isMuted ? 1 : 0);
            
            // 画质设置
            PlayerPrefs.SetInt("QualityLevel", currentSettings.qualityLevel);
            PlayerPrefs.SetInt("VSyncEnabled", currentSettings.vsyncEnabled ? 1 : 0);
            PlayerPrefs.SetInt("TargetFrameRate", currentSettings.targetFrameRate);
            PlayerPrefs.SetInt("LightingEnabled", currentSettings.lightingEnabled ? 1 : 0);
            PlayerPrefs.SetInt("ShadowsEnabled", currentSettings.shadowsEnabled ? 1 : 0);
            PlayerPrefs.SetInt("LightingQuality", currentSettings.lightingQuality);
            
            // 控制设置
            PlayerPrefs.SetFloat("JoystickSensitivity", currentSettings.joystickSensitivity);
            PlayerPrefs.SetInt("JoystickVisible", currentSettings.joystickVisible ? 1 : 0);
            PlayerPrefs.SetInt("JoystickPosition", currentSettings.joystickPosition);
            PlayerPrefs.SetInt("VibrationEnabled", currentSettings.vibrationEnabled ? 1 : 0);
            PlayerPrefs.SetFloat("VibrationIntensity", currentSettings.vibrationIntensity);
            
            // 游戏设置
            PlayerPrefs.SetInt("Difficulty", currentSettings.difficulty);
            PlayerPrefs.SetInt("AutoSaveEnabled", currentSettings.autoSaveEnabled ? 1 : 0);
            PlayerPrefs.SetFloat("AutoSaveInterval", currentSettings.autoSaveInterval);
            PlayerPrefs.SetInt("ShowFPS", currentSettings.showFPS ? 1 : 0);
            PlayerPrefs.SetInt("TutorialEnabled", currentSettings.tutorialEnabled ? 1 : 0);
            
            PlayerPrefs.Save();
        }
        
        #endregion
        
        #region UI更新
        
        private void UpdateUIFromSettings()
        {
            // 更新音频UI
            if (masterVolumeSlider != null)
                masterVolumeSlider.value = currentSettings.masterVolume;
            if (musicVolumeSlider != null)
                musicVolumeSlider.value = currentSettings.musicVolume;
            if (sfxVolumeSlider != null)
                sfxVolumeSlider.value = currentSettings.sfxVolume;
            if (muteToggle != null)
                muteToggle.isOn = currentSettings.isMuted;
            
            // 更新画质UI
            if (qualityDropdown != null)
                qualityDropdown.value = currentSettings.qualityLevel;
            if (vsyncToggle != null)
                vsyncToggle.isOn = currentSettings.vsyncEnabled;
            if (targetFrameRateDropdown != null)
                targetFrameRateDropdown.value = GetFrameRateDropdownIndex(currentSettings.targetFrameRate);
            if (lightingToggle != null)
                lightingToggle.isOn = currentSettings.lightingEnabled;
            if (shadowsToggle != null)
                shadowsToggle.isOn = currentSettings.shadowsEnabled;
            if (lightingQualitySlider != null)
                lightingQualitySlider.value = currentSettings.lightingQuality;
            
            // 更新控制UI
            if (joystickSensitivitySlider != null)
                joystickSensitivitySlider.value = currentSettings.joystickSensitivity;
            if (joystickVisibilityToggle != null)
                joystickVisibilityToggle.isOn = currentSettings.joystickVisible;
            if (joystickPositionDropdown != null)
                joystickPositionDropdown.value = currentSettings.joystickPosition;
            if (vibrationToggle != null)
                vibrationToggle.isOn = currentSettings.vibrationEnabled;
            if (vibrationIntensitySlider != null)
                vibrationIntensitySlider.value = currentSettings.vibrationIntensity;
            
            // 更新游戏UI
            if (difficultyDropdown != null)
                difficultyDropdown.value = currentSettings.difficulty;
            if (autoSaveToggle != null)
                autoSaveToggle.isOn = currentSettings.autoSaveEnabled;
            if (autoSaveIntervalSlider != null)
                autoSaveIntervalSlider.value = currentSettings.autoSaveInterval;
            if (showFPSToggle != null)
                showFPSToggle.isOn = currentSettings.showFPS;
            if (enableTutorialToggle != null)
                enableTutorialToggle.isOn = currentSettings.tutorialEnabled;
            
            // 更新文本显示
            UpdateVolumeTexts();
            UpdateOtherTexts();
        }
        
        private void UpdateVolumeTexts()
        {
            if (masterVolumeText != null)
                masterVolumeText.text = $"{Mathf.RoundToInt(currentSettings.masterVolume * 100)}%";
            if (musicVolumeText != null)
                musicVolumeText.text = $"{Mathf.RoundToInt(currentSettings.musicVolume * 100)}%";
            if (sfxVolumeText != null)
                sfxVolumeText.text = $"{Mathf.RoundToInt(currentSettings.sfxVolume * 100)}%";
        }
        
        private void UpdateOtherTexts()
        {
            if (lightingQualityText != null)
            {
                string[] qualityNames = { "低", "中", "高" };
                int index = Mathf.Clamp(currentSettings.lightingQuality, 0, qualityNames.Length - 1);
                lightingQualityText.text = qualityNames[index];
            }
            
            if (joystickSensitivityText != null)
                joystickSensitivityText.text = currentSettings.joystickSensitivity.ToString("F1");
            
            if (vibrationIntensityText != null)
                vibrationIntensityText.text = $"{Mathf.RoundToInt(currentSettings.vibrationIntensity * 100)}%";
            
            if (autoSaveIntervalText != null)
                autoSaveIntervalText.text = $"{Mathf.RoundToInt(currentSettings.autoSaveInterval)}秒";
        }
        
        private int GetFrameRateDropdownIndex(int frameRate)
        {
            switch (frameRate)
            {
                case 30: return 0;
                case 60: return 1;
                case 120: return 2;
                default: return 1; // 默认60FPS
            }
        }
        
        private int GetFrameRateFromDropdownIndex(int index)
        {
            switch (index)
            {
                case 0: return 30;
                case 1: return 60;
                case 2: return 120;
                default: return 60;
            }
        }
        
        #endregion
        
        #region 事件处理
        
        // 音频设置事件
        private void OnMasterVolumeChanged(float value)
        {
            currentSettings.masterVolume = value;
            if (masterVolumeText != null)
                masterVolumeText.text = $"{Mathf.RoundToInt(value * 100)}%";
            OnSettingsChanged?.Invoke();
        }
        
        private void OnMusicVolumeChanged(float value)
        {
            currentSettings.musicVolume = value;
            if (musicVolumeText != null)
                musicVolumeText.text = $"{Mathf.RoundToInt(value * 100)}%";
            OnSettingsChanged?.Invoke();
        }
        
        private void OnSFXVolumeChanged(float value)
        {
            currentSettings.sfxVolume = value;
            if (sfxVolumeText != null)
                sfxVolumeText.text = $"{Mathf.RoundToInt(value * 100)}%";
            OnSettingsChanged?.Invoke();
        }
        
        private void OnMuteToggleChanged(bool value)
        {
            currentSettings.isMuted = value;
            OnSettingsChanged?.Invoke();
        }
        
        // 画质设置事件
        private void OnQualityChanged(int value)
        {
            currentSettings.qualityLevel = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnVSyncToggleChanged(bool value)
        {
            currentSettings.vsyncEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnTargetFrameRateChanged(int value)
        {
            currentSettings.targetFrameRate = GetFrameRateFromDropdownIndex(value);
            OnSettingsChanged?.Invoke();
        }
        
        private void OnLightingToggleChanged(bool value)
        {
            currentSettings.lightingEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnShadowsToggleChanged(bool value)
        {
            currentSettings.shadowsEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnLightingQualityChanged(float value)
        {
            currentSettings.lightingQuality = Mathf.RoundToInt(value);
            if (lightingQualityText != null)
            {
                string[] qualityNames = { "低", "中", "高" };
                int index = Mathf.Clamp(currentSettings.lightingQuality, 0, qualityNames.Length - 1);
                lightingQualityText.text = qualityNames[index];
            }
            OnSettingsChanged?.Invoke();
        }
        
        // 控制设置事件
        private void OnJoystickSensitivityChanged(float value)
        {
            currentSettings.joystickSensitivity = value;
            if (joystickSensitivityText != null)
                joystickSensitivityText.text = value.ToString("F1");
            OnSettingsChanged?.Invoke();
        }
        
        private void OnJoystickVisibilityChanged(bool value)
        {
            currentSettings.joystickVisible = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnJoystickPositionChanged(int value)
        {
            currentSettings.joystickPosition = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnVibrationToggleChanged(bool value)
        {
            currentSettings.vibrationEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnVibrationIntensityChanged(float value)
        {
            currentSettings.vibrationIntensity = value;
            if (vibrationIntensityText != null)
                vibrationIntensityText.text = $"{Mathf.RoundToInt(value * 100)}%";
            OnSettingsChanged?.Invoke();
        }
        
        // 游戏设置事件
        private void OnDifficultyChanged(int value)
        {
            currentSettings.difficulty = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnAutoSaveToggleChanged(bool value)
        {
            currentSettings.autoSaveEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnAutoSaveIntervalChanged(float value)
        {
            currentSettings.autoSaveInterval = value;
            if (autoSaveIntervalText != null)
                autoSaveIntervalText.text = $"{Mathf.RoundToInt(value)}秒";
            OnSettingsChanged?.Invoke();
        }
        
        private void OnShowFPSToggleChanged(bool value)
        {
            currentSettings.showFPS = value;
            OnSettingsChanged?.Invoke();
        }
        
        private void OnTutorialToggleChanged(bool value)
        {
            currentSettings.tutorialEnabled = value;
            OnSettingsChanged?.Invoke();
        }
        
        // 按钮事件
        private void OnResetToDefaultClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 重置为默认设置
            currentSettings.ResetToDefault();
            UpdateUIFromSettings();
            OnSettingsChanged?.Invoke();
        }
        
        private void OnApplyClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            ApplySettings();
            originalSettings.CopyFrom(currentSettings);
        }
        
        private void OnCancelClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 恢复原始设置
            currentSettings.CopyFrom(originalSettings);
            UpdateUIFromSettings();
            OnSettingsCancelled?.Invoke();
        }
        
        private void OnBackClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 检查是否有未保存的更改
            if (!currentSettings.Equals(originalSettings))
            {
                // 显示确认对话框
                if (MenuSystem.Instance != null)
                {
                    MenuSystem.Instance.ShowConfirmation(
                        "有未保存的更改，是否要保存？",
                        () => {
                            ApplySettings();
                            ClosePanel();
                        },
                        () => {
                            OnCancelClicked();
                            ClosePanel();
                        }
                    );
                }
            }
            else
            {
                ClosePanel();
            }
        }
        
        private void OnAudioTestClicked()
        {
            AudioManager.Instance?.PlayButtonClick();
            
            // 播放测试音效
            if (AudioManager.Instance != null)
            {
                AudioManager.Instance.PlaySFX(null, currentSettings.sfxVolume); // 需要提供测试音效
            }
        }
        
        private void ClosePanel()
        {
            if (LandscapeUIManager.Instance != null)
            {
                LandscapeUIManager.Instance.ShowPanel(UIPanel.MainMenu);
            }
        }
        
        #endregion
        
        #region 预览更新
        
        private void UpdatePreview()
        {
            if (previewArea == null || !previewArea.activeInHierarchy) return;
            
            // 更新预览文本
            if (previewText != null)
            {
                previewText.text = GetPreviewText();
            }
            
            // 更新预览图像
            if (previewImage != null)
            {
                UpdatePreviewImage();
            }
        }
        
        private string GetPreviewText()
        {
            return $"画质: {GetQualityName(currentSettings.qualityLevel)}\n" +
                   $"帧率: {currentSettings.targetFrameRate} FPS\n" +
                   $"光照: {(currentSettings.lightingEnabled ? "开启" : "关闭")}\n" +
                   $"阴影: {(currentSettings.shadowsEnabled ? "开启" : "关闭")}";
        }
        
        private string GetQualityName(int qualityLevel)
        {
            string[] names = { "低", "中", "高" };
            return qualityLevel < names.Length ? names[qualityLevel] : "未知";
        }
        
        private void UpdatePreviewImage()
        {
            // 这里可以根据设置更新预览图像
            // 例如调整亮度、对比度等
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 显示设置面板
        /// </summary>
        public void ShowPanel()
        {
            gameObject.SetActive(true);
            LoadCurrentSettings();
        }
        
        /// <summary>
        /// 隐藏设置面板
        /// </summary>
        public void HidePanel()
        {
            gameObject.SetActive(false);
        }
        
        /// <summary>
        /// 获取设置状态信息
        /// </summary>
        public string GetSettingsStatus()
        {
            return $"设置状态:\n" +
                   $"主音量: {currentSettings.masterVolume:P0}\n" +
                   $"画质等级: {currentSettings.qualityLevel}\n" +
                   $"目标帧率: {currentSettings.targetFrameRate}\n" +
                   $"摇杆灵敏度: {currentSettings.joystickSensitivity:F1}\n" +
                   $"难度: {currentSettings.difficulty}";
        }
        
        #endregion
    }
    
    /// <summary>
    /// 设置数据类
    /// </summary>
    [System.Serializable]
    public class SettingsData
    {
        // 音频设置
        public float masterVolume = 1f;
        public float musicVolume = 0.7f;
        public float sfxVolume = 0.8f;
        public bool isMuted = false;
        
        // 画质设置
        public int qualityLevel = 1;
        public bool vsyncEnabled = true;
        public int targetFrameRate = 60;
        public bool lightingEnabled = true;
        public bool shadowsEnabled = true;
        public int lightingQuality = 1;
        
        // 控制设置
        public float joystickSensitivity = 1f;
        public bool joystickVisible = true;
        public int joystickPosition = 0;
        public bool vibrationEnabled = true;
        public float vibrationIntensity = 1f;
        
        // 游戏设置
        public int difficulty = 1;
        public bool autoSaveEnabled = true;
        public float autoSaveInterval = 30f;
        public bool showFPS = false;
        public bool tutorialEnabled = true;
        
        public void CopyFrom(SettingsData other)
        {
            masterVolume = other.masterVolume;
            musicVolume = other.musicVolume;
            sfxVolume = other.sfxVolume;
            isMuted = other.isMuted;
            
            qualityLevel = other.qualityLevel;
            vsyncEnabled = other.vsyncEnabled;
            targetFrameRate = other.targetFrameRate;
            lightingEnabled = other.lightingEnabled;
            shadowsEnabled = other.shadowsEnabled;
            lightingQuality = other.lightingQuality;
            
            joystickSensitivity = other.joystickSensitivity;
            joystickVisible = other.joystickVisible;
            joystickPosition = other.joystickPosition;
            vibrationEnabled = other.vibrationEnabled;
            vibrationIntensity = other.vibrationIntensity;
            
            difficulty = other.difficulty;
            autoSaveEnabled = other.autoSaveEnabled;
            autoSaveInterval = other.autoSaveInterval;
            showFPS = other.showFPS;
            tutorialEnabled = other.tutorialEnabled;
        }
        
        public void ResetToDefault()
        {
            masterVolume = 1f;
            musicVolume = 0.7f;
            sfxVolume = 0.8f;
            isMuted = false;
            
            qualityLevel = 1;
            vsyncEnabled = true;
            targetFrameRate = 60;
            lightingEnabled = true;
            shadowsEnabled = true;
            lightingQuality = 1;
            
            joystickSensitivity = 1f;
            joystickVisible = true;
            joystickPosition = 0;
            vibrationEnabled = true;
            vibrationIntensity = 1f;
            
            difficulty = 1;
            autoSaveEnabled = true;
            autoSaveInterval = 30f;
            showFPS = false;
            tutorialEnabled = true;
        }
        
        public bool Equals(SettingsData other)
        {
            if (other == null) return false;
            
            return masterVolume == other.masterVolume &&
                   musicVolume == other.musicVolume &&
                   sfxVolume == other.sfxVolume &&
                   isMuted == other.isMuted &&
                   qualityLevel == other.qualityLevel &&
                   vsyncEnabled == other.vsyncEnabled &&
                   targetFrameRate == other.targetFrameRate &&
                   lightingEnabled == other.lightingEnabled &&
                   shadowsEnabled == other.shadowsEnabled &&
                   lightingQuality == other.lightingQuality &&
                   joystickSensitivity == other.joystickSensitivity &&
                   joystickVisible == other.joystickVisible &&
                   joystickPosition == other.joystickPosition &&
                   vibrationEnabled == other.vibrationEnabled &&
                   vibrationIntensity == other.vibrationIntensity &&
                   difficulty == other.difficulty &&
                   autoSaveEnabled == other.autoSaveEnabled &&
                   autoSaveInterval == other.autoSaveInterval &&
                   showFPS == other.showFPS &&
                   tutorialEnabled == other.tutorialEnabled;
        }
    }
}
