using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 触屏交互管理器 - 处理各种触屏手势和交互
    /// </summary>
    public class TouchInteractionManager : MonoBehaviour
    {
        [Header("交互区域")]
        [SerializeField] private RectTransform leftInteractionZone;  // 左侧摇杆区域
        [SerializeField] private RectTransform rightInteractionZone; // 右侧操作区域
        [SerializeField] private RectTransform topInteractionZone;   // 顶部UI区域
        
        [Header("手势设置")]
        [SerializeField] private float tapMaxDuration = 0.3f;
        [SerializeField] private float tapMaxDistance = 50f;
        [SerializeField] private float doubleTapMaxInterval = 0.5f;
        [SerializeField] private float longPressMinDuration = 0.8f;
        [SerializeField] private float swipeMinDistance = 100f;
        
        [Header("特殊操作按钮")]
        [SerializeField] private Button actionButton;
        [SerializeField] private Button pauseButton;
        [SerializeField] private Button flashlightButton;
        
        [Header("视觉反馈")]
        [SerializeField] private GameObject touchEffectPrefab;
        [SerializeField] private float touchEffectDuration = 0.3f;
        
        // 事件系统
        public System.Action<Vector2> OnTap;
        public System.Action<Vector2> OnDoubleTap;
        public System.Action<Vector2> OnLongPress;
        public System.Action<Vector2, Vector2> OnSwipe;
        public System.Action OnSpecialAction;
        public System.Action OnPauseAction;
        public System.Action OnFlashlightToggle;
        
        // 触摸状态跟踪
        private Dictionary<int, TouchInfo> activeTouches = new Dictionary<int, TouchInfo>();
        private float lastTapTime = 0f;
        private Vector2 lastTapPosition = Vector2.zero;
        
        // 组件引用
        private Canvas parentCanvas;
        private GraphicRaycaster graphicRaycaster;
        
        #region Unity生命周期
        
        private void Awake()
        {
            InitializeComponents();
        }
        
        private void Start()
        {
            SetupButtons();
        }
        
        private void Update()
        {
            HandleTouchInput();
            UpdateTouchStates();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeComponents()
        {
            parentCanvas = GetComponentInParent<Canvas>();
            graphicRaycaster = GetComponentInParent<GraphicRaycaster>();
            
            // 创建默认交互区域
            if (leftInteractionZone == null)
                CreateInteractionZone("LeftZone", new Vector2(0, 0.5f), new Vector2(0.5f, 1f));
            
            if (rightInteractionZone == null)
                CreateInteractionZone("RightZone", new Vector2(0.5f, 0.5f), new Vector2(1f, 1f));
            
            if (topInteractionZone == null)
                CreateInteractionZone("TopZone", new Vector2(0, 0.8f), new Vector2(1f, 1f));
        }
        
        private void CreateInteractionZone(string name, Vector2 anchorMin, Vector2 anchorMax)
        {
            GameObject zoneObj = new GameObject(name);
            zoneObj.transform.SetParent(transform);
            
            RectTransform rectTransform = zoneObj.AddComponent<RectTransform>();
            rectTransform.anchorMin = anchorMin;
            rectTransform.anchorMax = anchorMax;
            rectTransform.offsetMin = Vector2.zero;
            rectTransform.offsetMax = Vector2.zero;
            
            // 添加透明图像以接收射线检测
            Image image = zoneObj.AddComponent<Image>();
            image.color = new Color(0, 0, 0, 0);
            image.raycastTarget = true;
        }
        
        private void SetupButtons()
        {
            // 设置按钮事件
            if (actionButton != null)
            {
                actionButton.onClick.AddListener(() => OnSpecialAction?.Invoke());
            }
            
            if (pauseButton != null)
            {
                pauseButton.onClick.AddListener(() => OnPauseAction?.Invoke());
            }
            
            if (flashlightButton != null)
            {
                flashlightButton.onClick.AddListener(() => OnFlashlightToggle?.Invoke());
            }
        }
        
        #endregion
        
        #region 触摸输入处理
        
        private void HandleTouchInput()
        {
            // 处理所有触摸点
            for (int i = 0; i < Input.touchCount; i++)
            {
                Touch touch = Input.GetTouch(i);
                ProcessTouch(touch);
            }
            
            // 清理结束的触摸
            CleanupEndedTouches();
        }
        
        private void ProcessTouch(Touch touch)
        {
            switch (touch.phase)
            {
                case TouchPhase.Began:
                    HandleTouchBegan(touch);
                    break;
                    
                case TouchPhase.Moved:
                    HandleTouchMoved(touch);
                    break;
                    
                case TouchPhase.Ended:
                    HandleTouchEnded(touch);
                    break;
                    
                case TouchPhase.Canceled:
                    HandleTouchCanceled(touch);
                    break;
            }
        }
        
        private void HandleTouchBegan(Touch touch)
        {
            // 检查是否触摸到UI元素
            if (IsTouchingUI(touch.position))
                return;
            
            // 创建触摸信息
            TouchInfo touchInfo = new TouchInfo
            {
                fingerId = touch.fingerId,
                startPosition = touch.position,
                currentPosition = touch.position,
                startTime = Time.time,
                interactionZone = GetInteractionZone(touch.position)
            };
            
            activeTouches[touch.fingerId] = touchInfo;
            
            // 显示触摸效果
            ShowTouchEffect(touch.position);
            
            // 震动反馈
            if (InputManager.Instance != null)
            {
                InputManager.Instance.Vibrate(0.03f);
            }
        }
        
        private void HandleTouchMoved(Touch touch)
        {
            if (activeTouches.ContainsKey(touch.fingerId))
            {
                TouchInfo touchInfo = activeTouches[touch.fingerId];
                touchInfo.currentPosition = touch.position;
                touchInfo.totalDistance += Vector2.Distance(touchInfo.lastPosition, touch.position);
                touchInfo.lastPosition = touch.position;
            }
        }
        
        private void HandleTouchEnded(Touch touch)
        {
            if (!activeTouches.ContainsKey(touch.fingerId))
                return;
            
            TouchInfo touchInfo = activeTouches[touch.fingerId];
            float touchDuration = Time.time - touchInfo.startTime;
            float touchDistance = Vector2.Distance(touchInfo.startPosition, touch.position);
            
            // 判断手势类型
            if (touchDuration < tapMaxDuration && touchDistance < tapMaxDistance)
            {
                HandleTap(touch.position);
            }
            else if (touchDuration >= longPressMinDuration && touchDistance < tapMaxDistance)
            {
                HandleLongPress(touch.position);
            }
            else if (touchDistance >= swipeMinDistance)
            {
                HandleSwipe(touchInfo.startPosition, touch.position);
            }
            
            // 清理触摸信息
            activeTouches.Remove(touch.fingerId);
        }
        
        private void HandleTouchCanceled(Touch touch)
        {
            activeTouches.Remove(touch.fingerId);
        }
        
        #endregion
        
        #region 手势识别
        
        private void HandleTap(Vector2 position)
        {
            float currentTime = Time.time;
            
            // 检查双击
            if (currentTime - lastTapTime < doubleTapMaxInterval &&
                Vector2.Distance(position, lastTapPosition) < tapMaxDistance)
            {
                OnDoubleTap?.Invoke(position);
                lastTapTime = 0f; // 重置以避免三击
            }
            else
            {
                OnTap?.Invoke(position);
                lastTapTime = currentTime;
                lastTapPosition = position;
            }
        }
        
        private void HandleLongPress(Vector2 position)
        {
            OnLongPress?.Invoke(position);
            
            // 长按震动反馈
            if (InputManager.Instance != null)
            {
                InputManager.Instance.Vibrate(0.1f);
            }
        }
        
        private void HandleSwipe(Vector2 startPosition, Vector2 endPosition)
        {
            Vector2 swipeDirection = (endPosition - startPosition).normalized;
            OnSwipe?.Invoke(startPosition, swipeDirection);
        }
        
        #endregion
        
        #region 交互区域管理
        
        private InteractionZone GetInteractionZone(Vector2 screenPosition)
        {
            Vector2 localPoint;
            
            // 检查左侧区域
            if (leftInteractionZone != null &&
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    leftInteractionZone, screenPosition, parentCanvas.worldCamera, out localPoint) &&
                leftInteractionZone.rect.Contains(localPoint))
            {
                return InteractionZone.Left;
            }
            
            // 检查右侧区域
            if (rightInteractionZone != null &&
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    rightInteractionZone, screenPosition, parentCanvas.worldCamera, out localPoint) &&
                rightInteractionZone.rect.Contains(localPoint))
            {
                return InteractionZone.Right;
            }
            
            // 检查顶部区域
            if (topInteractionZone != null &&
                RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    topInteractionZone, screenPosition, parentCanvas.worldCamera, out localPoint) &&
                topInteractionZone.rect.Contains(localPoint))
            {
                return InteractionZone.Top;
            }
            
            return InteractionZone.Center;
        }
        
        private bool IsTouchingUI(Vector2 screenPosition)
        {
            if (graphicRaycaster == null) return false;
            
            var eventData = new UnityEngine.EventSystems.PointerEventData(
                UnityEngine.EventSystems.EventSystem.current)
            {
                position = screenPosition
            };
            
            var results = new List<UnityEngine.EventSystems.RaycastResult>();
            graphicRaycaster.Raycast(eventData, results);
            
            // 检查是否点击到可交互的UI元素
            foreach (var result in results)
            {
                if (result.gameObject.GetComponent<Button>() != null ||
                    result.gameObject.GetComponent<Slider>() != null ||
                    result.gameObject.GetComponent<Toggle>() != null)
                {
                    return true;
                }
            }
            
            return false;
        }
        
        #endregion
        
        #region 视觉反馈
        
        private void ShowTouchEffect(Vector2 screenPosition)
        {
            if (touchEffectPrefab == null) return;
            
            Vector2 worldPosition;
            RectTransformUtility.ScreenPointToWorldPointInRectangle(
                parentCanvas.transform as RectTransform,
                screenPosition,
                parentCanvas.worldCamera,
                out worldPosition
            );
            
            GameObject effect = Instantiate(touchEffectPrefab, worldPosition, Quaternion.identity, transform);
            Destroy(effect, touchEffectDuration);
        }
        
        #endregion
        
        #region 状态更新
        
        private void UpdateTouchStates()
        {
            // 更新长按检测
            foreach (var kvp in activeTouches)
            {
                TouchInfo touchInfo = kvp.Value;
                float touchDuration = Time.time - touchInfo.startTime;
                
                if (!touchInfo.longPressTriggered && 
                    touchDuration >= longPressMinDuration &&
                    touchInfo.totalDistance < tapMaxDistance)
                {
                    touchInfo.longPressTriggered = true;
                    HandleLongPress(touchInfo.currentPosition);
                }
            }
        }
        
        private void CleanupEndedTouches()
        {
            var keysToRemove = new List<int>();
            
            foreach (var kvp in activeTouches)
            {
                bool touchExists = false;
                for (int i = 0; i < Input.touchCount; i++)
                {
                    if (Input.GetTouch(i).fingerId == kvp.Key)
                    {
                        touchExists = true;
                        break;
                    }
                }
                
                if (!touchExists)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }
            
            foreach (int key in keysToRemove)
            {
                activeTouches.Remove(key);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置手势参数
        /// </summary>
        public void SetGestureSettings(float tapDuration, float tapDistance, float swipeDistance)
        {
            tapMaxDuration = tapDuration;
            tapMaxDistance = tapDistance;
            swipeMinDistance = swipeDistance;
        }
        
        /// <summary>
        /// 启用/禁用触摸交互
        /// </summary>
        public void SetInteractionEnabled(bool enabled)
        {
            this.enabled = enabled;
            if (!enabled)
            {
                activeTouches.Clear();
            }
        }
        
        /// <summary>
        /// 获取当前活跃触摸数量
        /// </summary>
        public int GetActiveTouchCount()
        {
            return activeTouches.Count;
        }
        
        #endregion
    }
    
    /// <summary>
    /// 触摸信息类
    /// </summary>
    public class TouchInfo
    {
        public int fingerId;
        public Vector2 startPosition;
        public Vector2 currentPosition;
        public Vector2 lastPosition;
        public float startTime;
        public float totalDistance;
        public bool longPressTriggered;
        public InteractionZone interactionZone;
    }
    
    /// <summary>
    /// 交互区域枚举
    /// </summary>
    public enum InteractionZone
    {
        Left,    // 左侧区域（摇杆）
        Right,   // 右侧区域（操作按钮）
        Top,     // 顶部区域（UI）
        Center   // 中央区域
    }
}
