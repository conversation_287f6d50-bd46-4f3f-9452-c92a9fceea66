using UnityEngine;
using UnityEngine.Tilemaps;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 迷宫渲染器 - 将生成的迷宫数据渲染到场景中
    /// </summary>
    public class MazeRenderer : MonoBehaviour
    {
        [Header("渲染设置")]
        [SerializeField] private bool useSprites = false;
        [SerializeField] private bool useTilemap = true;
        [SerializeField] private bool useProceduralMesh = false;
        
        [Header("精灵渲染")]
        [SerializeField] private GameObject wallPrefab;
        [SerializeField] private GameObject pathPrefab;
        [SerializeField] private GameObject roomPrefab;
        [SerializeField] private GameObject startPrefab;
        [SerializeField] private GameObject endPrefab;
        
        [Header("瓦片地图渲染")]
        [SerializeField] private Tilemap tilemap;
        [SerializeField] private TileBase wallTile;
        [SerializeField] private TileBase pathTile;
        [SerializeField] private TileBase roomTile;
        [SerializeField] private TileBase startTile;
        [SerializeField] private TileBase endTile;
        
        [Header("临时渲染（无资源时）")]
        [SerializeField] private bool useTemporaryRendering = true;
        [SerializeField] private Material wallMaterial;
        [SerializeField] private Material pathMaterial;
        
        [Header("性能设置")]
        [SerializeField] private bool enableBatching = true;
        [SerializeField] private int batchSize = 100;
        [SerializeField] private bool enableCulling = true;
        
        // 单例模式
        public static MazeRenderer Instance { get; private set; }
        
        // 渲染对象容器
        private Transform mazeContainer;
        private GameObject[,] renderedObjects;
        
        // 临时渲染
        private MeshRenderer meshRenderer;
        private MeshFilter meshFilter;
        private Mesh mazeMesh;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeRenderer();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupRenderer();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeRenderer()
        {
            // 创建迷宫容器
            GameObject containerObj = new GameObject("MazeContainer");
            containerObj.transform.SetParent(transform);
            mazeContainer = containerObj.transform;
            
            // 设置临时渲染组件
            if (useTemporaryRendering)
            {
                meshRenderer = gameObject.AddComponent<MeshRenderer>();
                meshFilter = gameObject.AddComponent<MeshFilter>();
                
                // 创建默认材质
                if (wallMaterial == null)
                {
                    wallMaterial = CreateDefaultMaterial(Color.black);
                }
                if (pathMaterial == null)
                {
                    pathMaterial = CreateDefaultMaterial(Color.white);
                }
            }
            
            // 获取瓦片地图组件
            if (useTilemap && tilemap == null)
            {
                tilemap = GetComponentInChildren<Tilemap>();
            }
        }
        
        private void SetupRenderer()
        {
            // 订阅迷宫生成事件
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated += RenderMaze;
            }
        }
        
        private Material CreateDefaultMaterial(Color color)
        {
            Material mat = new Material(Shader.Find("Sprites/Default"));
            mat.color = color;
            return mat;
        }
        
        #endregion
        
        #region 迷宫渲染
        
        /// <summary>
        /// 渲染迷宫
        /// </summary>
        public void RenderMaze(MazeCell[,] mazeGrid)
        {
            if (mazeGrid == null) return;
            
            // 清除之前的渲染
            ClearRendering();
            
            int width = mazeGrid.GetLength(0);
            int height = mazeGrid.GetLength(1);
            
            // 选择渲染方法
            if (useTilemap && tilemap != null && HasTiles())
            {
                RenderWithTilemap(mazeGrid, width, height);
            }
            else if (useSprites && HasPrefabs())
            {
                RenderWithSprites(mazeGrid, width, height);
            }
            else if (useTemporaryRendering)
            {
                RenderTemporary(mazeGrid, width, height);
            }
            else
            {
                Debug.LogWarning("没有可用的渲染方法，请设置瓦片或预制体");
            }
        }
        
        #endregion
        
        #region 瓦片地图渲染
        
        private void RenderWithTilemap(MazeCell[,] mazeGrid, int width, int height)
        {
            if (tilemap == null) return;
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    Vector3Int position = new Vector3Int(x, y, 0);
                    TileBase tile = GetTileForCellType(mazeGrid[x, y].cellType);
                    
                    if (tile != null)
                    {
                        tilemap.SetTile(position, tile);
                    }
                }
            }
            
            Debug.Log("使用瓦片地图渲染迷宫完成");
        }
        
        private TileBase GetTileForCellType(CellType cellType)
        {
            switch (cellType)
            {
                case CellType.Wall: return wallTile;
                case CellType.Path: return pathTile;
                case CellType.Room: return roomTile ?? pathTile;
                case CellType.Start: return startTile ?? pathTile;
                case CellType.End: return endTile ?? pathTile;
                default: return null;
            }
        }
        
        private bool HasTiles()
        {
            return wallTile != null && pathTile != null;
        }
        
        #endregion
        
        #region 精灵渲染
        
        private void RenderWithSprites(MazeCell[,] mazeGrid, int width, int height)
        {
            renderedObjects = new GameObject[width, height];
            float cellSize = MazeGenerator.Instance?.cellSize ?? 1f;
            
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    GameObject prefab = GetPrefabForCellType(mazeGrid[x, y].cellType);
                    
                    if (prefab != null)
                    {
                        Vector3 position = new Vector3(x * cellSize, y * cellSize, 0);
                        GameObject instance = Instantiate(prefab, position, Quaternion.identity, mazeContainer);
                        instance.name = $"Cell_{x}_{y}_{mazeGrid[x, y].cellType}";
                        renderedObjects[x, y] = instance;
                    }
                }
            }
            
            Debug.Log("使用精灵渲染迷宫完成");
        }
        
        private GameObject GetPrefabForCellType(CellType cellType)
        {
            switch (cellType)
            {
                case CellType.Wall: return wallPrefab;
                case CellType.Path: return pathPrefab;
                case CellType.Room: return roomPrefab ?? pathPrefab;
                case CellType.Start: return startPrefab ?? pathPrefab;
                case CellType.End: return endPrefab ?? pathPrefab;
                default: return null;
            }
        }
        
        private bool HasPrefabs()
        {
            return wallPrefab != null && pathPrefab != null;
        }
        
        #endregion
        
        #region 临时渲染
        
        private void RenderTemporary(MazeCell[,] mazeGrid, int width, int height)
        {
            // 使用简单的方块渲染
            for (int x = 0; x < width; x++)
            {
                for (int y = 0; y < height; y++)
                {
                    CreateTemporaryCell(mazeGrid[x, y], x, y);
                }
            }
            
            Debug.Log("使用临时渲染迷宫完成");
        }
        
        private void CreateTemporaryCell(MazeCell cell, int x, int y)
        {
            float cellSize = MazeGenerator.Instance?.cellSize ?? 1f;
            Vector3 position = new Vector3(x * cellSize, y * cellSize, 0);
            
            GameObject cellObj = GameObject.CreatePrimitive(PrimitiveType.Quad);
            cellObj.transform.position = position;
            cellObj.transform.localScale = Vector3.one * cellSize * 0.9f;
            cellObj.transform.SetParent(mazeContainer);
            cellObj.name = $"TempCell_{x}_{y}_{cell.cellType}";
            
            // 移除碰撞体（我们不需要）
            Collider collider = cellObj.GetComponent<Collider>();
            if (collider != null)
            {
                DestroyImmediate(collider);
            }
            
            // 设置材质和颜色
            MeshRenderer renderer = cellObj.GetComponent<MeshRenderer>();
            if (renderer != null)
            {
                renderer.material = GetMaterialForCellType(cell.cellType);
            }
        }
        
        private Material GetMaterialForCellType(CellType cellType)
        {
            Color color = GetColorForCellType(cellType);
            Material mat = new Material(Shader.Find("Sprites/Default"));
            mat.color = color;
            return mat;
        }
        
        private Color GetColorForCellType(CellType cellType)
        {
            switch (cellType)
            {
                case CellType.Wall: return Color.black;
                case CellType.Path: return Color.white;
                case CellType.Room: return Color.cyan;
                case CellType.Start: return Color.green;
                case CellType.End: return Color.red;
                default: return Color.gray;
            }
        }
        
        #endregion
        
        #region 清理和更新
        
        /// <summary>
        /// 清除当前渲染
        /// </summary>
        public void ClearRendering()
        {
            // 清除瓦片地图
            if (tilemap != null)
            {
                tilemap.SetTilesBlock(tilemap.cellBounds, new TileBase[tilemap.cellBounds.size.x * tilemap.cellBounds.size.y * tilemap.cellBounds.size.z]);
            }
            
            // 清除精灵对象
            if (mazeContainer != null)
            {
                for (int i = mazeContainer.childCount - 1; i >= 0; i--)
                {
                    if (Application.isPlaying)
                    {
                        Destroy(mazeContainer.GetChild(i).gameObject);
                    }
                    else
                    {
                        DestroyImmediate(mazeContainer.GetChild(i).gameObject);
                    }
                }
            }
            
            renderedObjects = null;
        }
        
        /// <summary>
        /// 更新特定位置的渲染
        /// </summary>
        public void UpdateCell(Vector2Int position, CellType newType)
        {
            if (useTilemap && tilemap != null)
            {
                Vector3Int tilePos = new Vector3Int(position.x, position.y, 0);
                TileBase tile = GetTileForCellType(newType);
                tilemap.SetTile(tilePos, tile);
            }
            else if (renderedObjects != null && 
                     position.x >= 0 && position.x < renderedObjects.GetLength(0) &&
                     position.y >= 0 && position.y < renderedObjects.GetLength(1))
            {
                // 更新精灵渲染
                GameObject oldObj = renderedObjects[position.x, position.y];
                if (oldObj != null)
                {
                    Destroy(oldObj);
                }
                
                GameObject prefab = GetPrefabForCellType(newType);
                if (prefab != null)
                {
                    float cellSize = MazeGenerator.Instance?.cellSize ?? 1f;
                    Vector3 worldPos = new Vector3(position.x * cellSize, position.y * cellSize, 0);
                    GameObject newObj = Instantiate(prefab, worldPos, Quaternion.identity, mazeContainer);
                    renderedObjects[position.x, position.y] = newObj;
                }
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置渲染模式
        /// </summary>
        public void SetRenderingMode(bool sprites, bool tilemap, bool temporary)
        {
            useSprites = sprites;
            useTilemap = tilemap;
            useTemporaryRendering = temporary;
        }
        
        /// <summary>
        /// 获取迷宫渲染边界
        /// </summary>
        public Bounds GetRenderBounds()
        {
            if (MazeGenerator.Instance != null)
            {
                return MazeGenerator.Instance.GetMazeBounds();
            }
            
            return new Bounds(Vector3.zero, Vector3.one * 10f);
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (MazeGenerator.Instance != null)
            {
                MazeGenerator.Instance.OnMazeGenerated -= RenderMaze;
            }
        }
        
        #endregion
    }
}
