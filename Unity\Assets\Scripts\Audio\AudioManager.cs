using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using MazeAdventure.Core;

namespace MazeAdventure.Audio
{
    /// <summary>
    /// 音频管理器 - 管理游戏中的所有音效和音乐
    /// </summary>
    public class AudioManager : MonoBehaviour
    {
        [Header("音频源设置")]
        [SerializeField] private int maxAudioSources = 10;
        [SerializeField] private AudioSource musicSource;
        [SerializeField] private AudioSource ambientSource;
        [SerializeField] private AudioSource[] sfxSources;
        
        [Header("音量设置")]
        [Range(0f, 1f)]
        [SerializeField] private float masterVolume = 1f;
        [Range(0f, 1f)]
        [SerializeField] private float musicVolume = 0.7f;
        [Range(0f, 1f)]
        [SerializeField] private float sfxVolume = 0.8f;
        [Range(0f, 1f)]
        [SerializeField] private float ambientVolume = 0.5f;
        
        [Header("音频剪辑")]
        [SerializeField] private AudioClip backgroundMusic;
        [SerializeField] private AudioClip menuMusic;
        [SerializeField] private AudioClip victoryMusic;
        [SerializeField] private AudioClip gameOverMusic;
        
        [Header("环境音效")]
        [SerializeField] private AudioClip ambientCaveSound;
        [SerializeField] private AudioClip ambientWindSound;
        
        [Header("玩家音效")]
        [SerializeField] private AudioClip[] footstepSounds;
        [SerializeField] private AudioClip playerHurtSound;
        [SerializeField] private AudioClip playerDeathSound;
        [SerializeField] private AudioClip breathingSound;
        
        [Header("道具音效")]
        [SerializeField] private AudioClip batteryPickupSound;
        [SerializeField] private AudioClip healthPickupSound;
        [SerializeField] private AudioClip itemPickupSound;
        [SerializeField] private AudioClip powerUpSound;
        
        [Header("UI音效")]
        [SerializeField] private AudioClip buttonClickSound;
        [SerializeField] private AudioClip buttonHoverSound;
        [SerializeField] private AudioClip menuOpenSound;
        [SerializeField] private AudioClip menuCloseSound;
        [SerializeField] private AudioClip warningSound;
        
        [Header("游戏音效")]
        [SerializeField] private AudioClip headlampToggleSound;
        [SerializeField] private AudioClip lowBatterySound;
        [SerializeField] private AudioClip gameStartSound;
        [SerializeField] private AudioClip levelCompleteSound;
        
        [Header("音频设置")]
        [SerializeField] private bool enableSpatialAudio = false;
        [SerializeField] private bool enableReverb = true;
        [SerializeField] private bool enableLowPassFilter = false;
        [SerializeField] private bool muteOnFocusLoss = true;
        
        // 单例模式
        public static AudioManager Instance { get; private set; }
        
        // 音频源池
        private Queue<AudioSource> availableSfxSources;
        private List<AudioSource> activeSfxSources;
        
        // 当前播放状态
        private AudioClip currentMusic;
        private bool isMusicPlaying;
        private bool isMuted;
        
        // 淡入淡出
        private Coroutine musicFadeCoroutine;
        private Coroutine ambientFadeCoroutine;
        
        // 脚步声控制
        private float lastFootstepTime;
        private float footstepInterval = 0.5f;
        private int currentFootstepIndex;
        
        // 事件系统
        public System.Action<float> OnMasterVolumeChanged;
        public System.Action<float> OnMusicVolumeChanged;
        public System.Action<float> OnSfxVolumeChanged;
        public System.Action<bool> OnMuteStateChanged;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeAudioManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupAudioManager();
        }
        
        private void Update()
        {
            UpdateFootsteps();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeAudioManager()
        {
            // 创建音频源
            CreateAudioSources();
            
            // 初始化音频源池
            availableSfxSources = new Queue<AudioSource>();
            activeSfxSources = new List<AudioSource>();
            
            // 将所有SFX音频源加入池中
            foreach (AudioSource source in sfxSources)
            {
                availableSfxSources.Enqueue(source);
            }
            
            // 加载保存的音量设置
            LoadVolumeSettings();
        }
        
        private void CreateAudioSources()
        {
            // 创建音乐音频源
            if (musicSource == null)
            {
                GameObject musicObj = new GameObject("MusicSource");
                musicObj.transform.SetParent(transform);
                musicSource = musicObj.AddComponent<AudioSource>();
                musicSource.loop = true;
                musicSource.playOnAwake = false;
            }
            
            // 创建环境音频源
            if (ambientSource == null)
            {
                GameObject ambientObj = new GameObject("AmbientSource");
                ambientObj.transform.SetParent(transform);
                ambientSource = ambientObj.AddComponent<AudioSource>();
                ambientSource.loop = true;
                ambientSource.playOnAwake = false;
            }
            
            // 创建音效音频源数组
            if (sfxSources == null || sfxSources.Length == 0)
            {
                sfxSources = new AudioSource[maxAudioSources];
                for (int i = 0; i < maxAudioSources; i++)
                {
                    GameObject sfxObj = new GameObject($"SFXSource_{i}");
                    sfxObj.transform.SetParent(transform);
                    sfxSources[i] = sfxObj.AddComponent<AudioSource>();
                    sfxSources[i].playOnAwake = false;
                }
            }
        }
        
        private void SetupAudioManager()
        {
            // 订阅游戏事件
            SubscribeToGameEvents();
            
            // 应用音量设置
            ApplyVolumeSettings();
            
            // 开始播放环境音
            if (ambientCaveSound != null)
            {
                PlayAmbient(ambientCaveSound);
            }
            
            Debug.Log("音频管理器初始化完成");
        }
        
        private void SubscribeToGameEvents()
        {
            // 订阅玩家事件
            if (PlayerController.Instance != null)
            {
                // 这里可以订阅玩家移动事件来播放脚步声
            }
            
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnDamageTaken += PlayPlayerHurtSound;
                PlayerData.Instance.OnPlayerDied += PlayPlayerDeathSound;
                PlayerData.Instance.OnLowBattery += PlayLowBatteryWarning;
            }
            
            // 订阅道具系统事件
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnItemCollected += PlayItemPickupSound;
            }
            
            // 订阅游戏状态事件
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStarted += PlayGameStartSound;
                GameStateManager.Instance.OnGameWon += PlayVictoryMusic;
                GameStateManager.Instance.OnGameLost += PlayGameOverMusic;
            }
            
            // 订阅头灯事件
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.OnHeadlampToggled += PlayHeadlampToggleSound;
            }
        }
        
        #endregion
        
        #region 音乐播放
        
        /// <summary>
        /// 播放背景音乐
        /// </summary>
        public void PlayMusic(AudioClip clip, bool fadeIn = true, float fadeTime = 1f)
        {
            if (clip == null || clip == currentMusic) return;
            
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            if (fadeIn && isMusicPlaying)
            {
                musicFadeCoroutine = StartCoroutine(CrossfadeMusic(clip, fadeTime));
            }
            else
            {
                musicSource.clip = clip;
                musicSource.volume = musicVolume * masterVolume;
                musicSource.Play();
                currentMusic = clip;
                isMusicPlaying = true;
            }
        }
        
        /// <summary>
        /// 停止音乐播放
        /// </summary>
        public void StopMusic(bool fadeOut = true, float fadeTime = 1f)
        {
            if (!isMusicPlaying) return;
            
            if (musicFadeCoroutine != null)
            {
                StopCoroutine(musicFadeCoroutine);
            }
            
            if (fadeOut)
            {
                musicFadeCoroutine = StartCoroutine(FadeOutMusic(fadeTime));
            }
            else
            {
                musicSource.Stop();
                isMusicPlaying = false;
                currentMusic = null;
            }
        }
        
        private IEnumerator CrossfadeMusic(AudioClip newClip, float fadeTime)
        {
            float startVolume = musicSource.volume;
            
            // 淡出当前音乐
            for (float t = 0; t < fadeTime * 0.5f; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(startVolume, 0f, t / (fadeTime * 0.5f));
                yield return null;
            }
            
            // 切换音乐
            musicSource.clip = newClip;
            musicSource.Play();
            currentMusic = newClip;
            
            // 淡入新音乐
            for (float t = 0; t < fadeTime * 0.5f; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(0f, musicVolume * masterVolume, t / (fadeTime * 0.5f));
                yield return null;
            }
            
            musicSource.volume = musicVolume * masterVolume;
            musicFadeCoroutine = null;
        }
        
        private IEnumerator FadeOutMusic(float fadeTime)
        {
            float startVolume = musicSource.volume;
            
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                musicSource.volume = Mathf.Lerp(startVolume, 0f, t / fadeTime);
                yield return null;
            }
            
            musicSource.Stop();
            musicSource.volume = musicVolume * masterVolume;
            isMusicPlaying = false;
            currentMusic = null;
            musicFadeCoroutine = null;
        }
        
        #endregion
        
        #region 环境音播放
        
        /// <summary>
        /// 播放环境音
        /// </summary>
        public void PlayAmbient(AudioClip clip, bool fadeIn = true, float fadeTime = 2f)
        {
            if (clip == null) return;
            
            if (ambientFadeCoroutine != null)
            {
                StopCoroutine(ambientFadeCoroutine);
            }
            
            if (fadeIn && ambientSource.isPlaying)
            {
                ambientFadeCoroutine = StartCoroutine(CrossfadeAmbient(clip, fadeTime));
            }
            else
            {
                ambientSource.clip = clip;
                ambientSource.volume = ambientVolume * masterVolume;
                ambientSource.Play();
            }
        }
        
        /// <summary>
        /// 停止环境音
        /// </summary>
        public void StopAmbient(bool fadeOut = true, float fadeTime = 2f)
        {
            if (!ambientSource.isPlaying) return;
            
            if (ambientFadeCoroutine != null)
            {
                StopCoroutine(ambientFadeCoroutine);
            }
            
            if (fadeOut)
            {
                ambientFadeCoroutine = StartCoroutine(FadeOutAmbient(fadeTime));
            }
            else
            {
                ambientSource.Stop();
            }
        }
        
        private IEnumerator CrossfadeAmbient(AudioClip newClip, float fadeTime)
        {
            float startVolume = ambientSource.volume;
            
            // 淡出当前环境音
            for (float t = 0; t < fadeTime * 0.5f; t += Time.deltaTime)
            {
                ambientSource.volume = Mathf.Lerp(startVolume, 0f, t / (fadeTime * 0.5f));
                yield return null;
            }
            
            // 切换环境音
            ambientSource.clip = newClip;
            ambientSource.Play();
            
            // 淡入新环境音
            for (float t = 0; t < fadeTime * 0.5f; t += Time.deltaTime)
            {
                ambientSource.volume = Mathf.Lerp(0f, ambientVolume * masterVolume, t / (fadeTime * 0.5f));
                yield return null;
            }
            
            ambientSource.volume = ambientVolume * masterVolume;
            ambientFadeCoroutine = null;
        }
        
        private IEnumerator FadeOutAmbient(float fadeTime)
        {
            float startVolume = ambientSource.volume;
            
            for (float t = 0; t < fadeTime; t += Time.deltaTime)
            {
                ambientSource.volume = Mathf.Lerp(startVolume, 0f, t / fadeTime);
                yield return null;
            }
            
            ambientSource.Stop();
            ambientSource.volume = ambientVolume * masterVolume;
            ambientFadeCoroutine = null;
        }
        
        #endregion
        
        #region 音效播放
        
        /// <summary>
        /// 播放音效
        /// </summary>
        public void PlaySFX(AudioClip clip, float volume = 1f, float pitch = 1f, bool randomizePitch = false)
        {
            if (clip == null) return;
            
            AudioSource source = GetAvailableSFXSource();
            if (source == null) return;
            
            source.clip = clip;
            source.volume = volume * sfxVolume * masterVolume;
            source.pitch = randomizePitch ? Random.Range(0.8f, 1.2f) : pitch;
            source.Play();
            
            // 将音频源标记为活跃
            activeSfxSources.Add(source);
            
            // 播放完成后回收音频源
            StartCoroutine(RecycleSFXSource(source, clip.length / source.pitch));
        }
        
        /// <summary>
        /// 在指定位置播放3D音效
        /// </summary>
        public void PlaySFXAtPosition(AudioClip clip, Vector3 position, float volume = 1f, float maxDistance = 10f)
        {
            if (clip == null || !enableSpatialAudio) 
            {
                PlaySFX(clip, volume);
                return;
            }
            
            AudioSource source = GetAvailableSFXSource();
            if (source == null) return;
            
            source.transform.position = position;
            source.clip = clip;
            source.volume = volume * sfxVolume * masterVolume;
            source.spatialBlend = 1f; // 3D音效
            source.maxDistance = maxDistance;
            source.rolloffMode = AudioRolloffMode.Linear;
            source.Play();
            
            activeSfxSources.Add(source);
            StartCoroutine(RecycleSFXSource(source, clip.length));
        }
        
        private AudioSource GetAvailableSFXSource()
        {
            // 清理已完成播放的音频源
            for (int i = activeSfxSources.Count - 1; i >= 0; i--)
            {
                if (!activeSfxSources[i].isPlaying)
                {
                    availableSfxSources.Enqueue(activeSfxSources[i]);
                    activeSfxSources.RemoveAt(i);
                }
            }
            
            if (availableSfxSources.Count > 0)
            {
                return availableSfxSources.Dequeue();
            }
            
            // 如果没有可用的音频源，强制回收最老的
            if (activeSfxSources.Count > 0)
            {
                AudioSource oldestSource = activeSfxSources[0];
                oldestSource.Stop();
                activeSfxSources.RemoveAt(0);
                return oldestSource;
            }
            
            return null;
        }
        
        private IEnumerator RecycleSFXSource(AudioSource source, float delay)
        {
            yield return new WaitForSeconds(delay);
            
            if (activeSfxSources.Contains(source))
            {
                activeSfxSources.Remove(source);
                availableSfxSources.Enqueue(source);
                
                // 重置音频源设置
                source.spatialBlend = 0f;
                source.pitch = 1f;
                source.transform.position = transform.position;
            }
        }
        
        #endregion
        
        #region 特定音效播放
        
        private void UpdateFootsteps()
        {
            if (PlayerController.Instance == null || footstepSounds == null || footstepSounds.Length == 0) 
                return;
            
            // 检查玩家是否在移动
            if (PlayerController.Instance.IsMoving && Time.time - lastFootstepTime > footstepInterval)
            {
                PlayFootstepSound();
                lastFootstepTime = Time.time;
            }
        }
        
        private void PlayFootstepSound()
        {
            if (footstepSounds == null || footstepSounds.Length == 0) return;
            
            AudioClip footstepClip = footstepSounds[currentFootstepIndex];
            currentFootstepIndex = (currentFootstepIndex + 1) % footstepSounds.Length;
            
            PlaySFX(footstepClip, 0.6f, 1f, true);
        }
        
        private void PlayPlayerHurtSound(float damage)
        {
            if (playerHurtSound != null)
            {
                PlaySFX(playerHurtSound, 0.8f);
            }
        }
        
        private void PlayPlayerDeathSound()
        {
            if (playerDeathSound != null)
            {
                PlaySFX(playerDeathSound, 1f);
            }
        }
        
        private void PlayLowBatteryWarning()
        {
            if (lowBatterySound != null)
            {
                PlaySFX(lowBatterySound, 0.7f);
            }
        }
        
        private void PlayItemPickupSound(ItemType itemType, Vector2 position)
        {
            AudioClip clipToPlay = null;
            
            switch (itemType)
            {
                case ItemType.Battery:
                    clipToPlay = batteryPickupSound;
                    break;
                case ItemType.HealthPack:
                    clipToPlay = healthPickupSound;
                    break;
                default:
                    clipToPlay = itemPickupSound;
                    break;
            }
            
            if (clipToPlay != null)
            {
                if (enableSpatialAudio)
                {
                    PlaySFXAtPosition(clipToPlay, position, 0.8f);
                }
                else
                {
                    PlaySFX(clipToPlay, 0.8f);
                }
            }
        }
        
        private void PlayGameStartSound()
        {
            if (gameStartSound != null)
            {
                PlaySFX(gameStartSound, 0.9f);
            }
            
            // 开始播放背景音乐
            if (backgroundMusic != null)
            {
                PlayMusic(backgroundMusic);
            }
        }
        
        private void PlayVictoryMusic()
        {
            if (victoryMusic != null)
            {
                PlayMusic(victoryMusic, true, 2f);
            }
        }
        
        private void PlayGameOverMusic()
        {
            if (gameOverMusic != null)
            {
                PlayMusic(gameOverMusic, true, 2f);
            }
        }
        
        private void PlayHeadlampToggleSound(bool isOn)
        {
            if (headlampToggleSound != null)
            {
                float pitch = isOn ? 1.2f : 0.8f;
                PlaySFX(headlampToggleSound, 0.6f, pitch);
            }
        }
        
        #endregion
        
        #region UI音效
        
        /// <summary>
        /// 播放按钮点击音效
        /// </summary>
        public void PlayButtonClick()
        {
            if (buttonClickSound != null)
            {
                PlaySFX(buttonClickSound, 0.7f);
            }
        }
        
        /// <summary>
        /// 播放按钮悬停音效
        /// </summary>
        public void PlayButtonHover()
        {
            if (buttonHoverSound != null)
            {
                PlaySFX(buttonHoverSound, 0.5f);
            }
        }
        
        /// <summary>
        /// 播放菜单打开音效
        /// </summary>
        public void PlayMenuOpen()
        {
            if (menuOpenSound != null)
            {
                PlaySFX(menuOpenSound, 0.6f);
            }
        }
        
        /// <summary>
        /// 播放菜单关闭音效
        /// </summary>
        public void PlayMenuClose()
        {
            if (menuCloseSound != null)
            {
                PlaySFX(menuCloseSound, 0.6f);
            }
        }
        
        /// <summary>
        /// 播放警告音效
        /// </summary>
        public void PlayWarning()
        {
            if (warningSound != null)
            {
                PlaySFX(warningSound, 0.8f);
            }
        }
        
        #endregion
        
        #region 音量控制
        
        /// <summary>
        /// 设置主音量
        /// </summary>
        public void SetMasterVolume(float volume)
        {
            masterVolume = Mathf.Clamp01(volume);
            ApplyVolumeSettings();
            SaveVolumeSettings();
            OnMasterVolumeChanged?.Invoke(masterVolume);
        }
        
        /// <summary>
        /// 设置音乐音量
        /// </summary>
        public void SetMusicVolume(float volume)
        {
            musicVolume = Mathf.Clamp01(volume);
            if (musicSource != null)
            {
                musicSource.volume = musicVolume * masterVolume;
            }
            SaveVolumeSettings();
            OnMusicVolumeChanged?.Invoke(musicVolume);
        }
        
        /// <summary>
        /// 设置音效音量
        /// </summary>
        public void SetSFXVolume(float volume)
        {
            sfxVolume = Mathf.Clamp01(volume);
            SaveVolumeSettings();
            OnSfxVolumeChanged?.Invoke(sfxVolume);
        }
        
        /// <summary>
        /// 设置环境音音量
        /// </summary>
        public void SetAmbientVolume(float volume)
        {
            ambientVolume = Mathf.Clamp01(volume);
            if (ambientSource != null)
            {
                ambientSource.volume = ambientVolume * masterVolume;
            }
            SaveVolumeSettings();
        }
        
        /// <summary>
        /// 静音/取消静音
        /// </summary>
        public void SetMute(bool mute)
        {
            isMuted = mute;
            ApplyVolumeSettings();
            OnMuteStateChanged?.Invoke(isMuted);
        }
        
        private void ApplyVolumeSettings()
        {
            float volumeMultiplier = isMuted ? 0f : masterVolume;
            
            if (musicSource != null)
            {
                musicSource.volume = musicVolume * volumeMultiplier;
            }
            
            if (ambientSource != null)
            {
                ambientSource.volume = ambientVolume * volumeMultiplier;
            }
            
            // SFX音量会在播放时应用
        }
        
        private void LoadVolumeSettings()
        {
            masterVolume = PlayerPrefs.GetFloat("MasterVolume", 1f);
            musicVolume = PlayerPrefs.GetFloat("MusicVolume", 0.7f);
            sfxVolume = PlayerPrefs.GetFloat("SFXVolume", 0.8f);
            ambientVolume = PlayerPrefs.GetFloat("AmbientVolume", 0.5f);
            isMuted = PlayerPrefs.GetInt("AudioMuted", 0) == 1;
        }
        
        private void SaveVolumeSettings()
        {
            PlayerPrefs.SetFloat("MasterVolume", masterVolume);
            PlayerPrefs.SetFloat("MusicVolume", musicVolume);
            PlayerPrefs.SetFloat("SFXVolume", sfxVolume);
            PlayerPrefs.SetFloat("AmbientVolume", ambientVolume);
            PlayerPrefs.SetInt("AudioMuted", isMuted ? 1 : 0);
            PlayerPrefs.Save();
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取音频统计信息
        /// </summary>
        public string GetAudioStats()
        {
            return $"音频统计:\n" +
                   $"主音量: {masterVolume:P0}\n" +
                   $"音乐音量: {musicVolume:P0}\n" +
                   $"音效音量: {sfxVolume:P0}\n" +
                   $"环境音音量: {ambientVolume:P0}\n" +
                   $"静音状态: {(isMuted ? "是" : "否")}\n" +
                   $"活跃音效源: {activeSfxSources.Count}/{maxAudioSources}\n" +
                   $"当前音乐: {(currentMusic != null ? currentMusic.name : "无")}\n" +
                   $"空间音频: {(enableSpatialAudio ? "启用" : "禁用")}";
        }
        
        /// <summary>
        /// 暂停所有音频
        /// </summary>
        public void PauseAll()
        {
            if (musicSource != null && musicSource.isPlaying)
            {
                musicSource.Pause();
            }
            
            if (ambientSource != null && ambientSource.isPlaying)
            {
                ambientSource.Pause();
            }
            
            foreach (AudioSource source in activeSfxSources)
            {
                if (source.isPlaying)
                {
                    source.Pause();
                }
            }
        }
        
        /// <summary>
        /// 恢复所有音频
        /// </summary>
        public void ResumeAll()
        {
            if (musicSource != null)
            {
                musicSource.UnPause();
            }
            
            if (ambientSource != null)
            {
                ambientSource.UnPause();
            }
            
            foreach (AudioSource source in activeSfxSources)
            {
                source.UnPause();
            }
        }
        
        #endregion
        
        #region 应用生命周期
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (muteOnFocusLoss)
            {
                if (hasFocus)
                {
                    ResumeAll();
                }
                else
                {
                    PauseAll();
                }
            }
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (muteOnFocusLoss)
            {
                if (pauseStatus)
                {
                    PauseAll();
                }
                else
                {
                    ResumeAll();
                }
            }
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            // 取消订阅事件
            if (PlayerData.Instance != null)
            {
                PlayerData.Instance.OnDamageTaken -= PlayPlayerHurtSound;
                PlayerData.Instance.OnPlayerDied -= PlayPlayerDeathSound;
                PlayerData.Instance.OnLowBattery -= PlayLowBatteryWarning;
            }
            
            if (ItemSystem.Instance != null)
            {
                ItemSystem.Instance.OnItemCollected -= PlayItemPickupSound;
            }
            
            if (GameStateManager.Instance != null)
            {
                GameStateManager.Instance.OnGameStarted -= PlayGameStartSound;
                GameStateManager.Instance.OnGameWon -= PlayVictoryMusic;
                GameStateManager.Instance.OnGameLost -= PlayGameOverMusic;
            }
            
            if (HeadlampSystem.Instance != null)
            {
                HeadlampSystem.Instance.OnHeadlampToggled -= PlayHeadlampToggleSound;
            }
        }
        
        #endregion
    }
}
