#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.UI;
using System.IO;

namespace MazeAdventure.Utils
{
    /// <summary>
    /// 自动项目设置工具 - 一键配置项目基础设置
    /// </summary>
    public class AutoProjectSetup : EditorWindow
    {
        private bool createFolders = true;
        private bool setupManagers = true;
        private bool configureCanvas = true;
        private bool setupAndroidSettings = true;
        
        private Vector2 scrollPosition;
        
        [MenuItem("MazeAdventure/Auto Project Setup")]
        public static void ShowWindow()
        {
            AutoProjectSetup window = GetWindow<AutoProjectSetup>("项目自动设置");
            window.minSize = new Vector2(400, 500);
            window.Show();
        }
        
        private void OnGUI()
        {
            scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
            
            EditorGUILayout.LabelField("迷宫探险 - 项目自动设置", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("选择要执行的设置步骤:", EditorStyles.label);
            
            createFolders = EditorGUILayout.Toggle("创建文件夹结构", createFolders);
            setupManagers = EditorGUILayout.Toggle("设置管理器对象", setupManagers);
            configureCanvas = EditorGUILayout.Toggle("配置Canvas和UI", configureCanvas);
            setupAndroidSettings = EditorGUILayout.Toggle("配置Android设置", setupAndroidSettings);
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("执行自动设置", GUILayout.Height(30)))
            {
                ExecuteAutoSetup();
            }
            
            EditorGUILayout.Space();
            
            EditorGUILayout.LabelField("手动操作:", EditorStyles.boldLabel);
            
            if (GUILayout.Button("仅创建文件夹结构"))
            {
                CreateFolderStructure();
            }
            
            if (GUILayout.Button("仅设置管理器对象"))
            {
                SetupManagerObjects();
            }
            
            if (GUILayout.Button("仅配置Canvas"))
            {
                ConfigureCanvas();
            }
            
            if (GUILayout.Button("验证项目设置"))
            {
                ValidateProject();
            }
            
            EditorGUILayout.Space();
            
            EditorGUILayout.HelpBox(
                "自动设置将会:\n" +
                "• 创建完整的文件夹结构\n" +
                "• 在场景中创建管理器对象\n" +
                "• 配置Canvas和UI设置\n" +
                "• 设置Android平台参数\n" +
                "• 创建基础的GameConfig资源",
                MessageType.Info);
            
            EditorGUILayout.EndScrollView();
        }
        
        private void ExecuteAutoSetup()
        {
            Debug.Log("=== 开始自动项目设置 ===");
            
            try
            {
                if (createFolders)
                {
                    CreateFolderStructure();
                }
                
                if (setupManagers)
                {
                    SetupManagerObjects();
                }
                
                if (configureCanvas)
                {
                    ConfigureCanvas();
                }
                
                if (setupAndroidSettings)
                {
                    SetupAndroidSettings();
                }
                
                CreateGameConfig();
                
                Debug.Log("=== 自动项目设置完成 ===");
                EditorUtility.DisplayDialog("设置完成", "项目自动设置已完成！", "确定");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"自动设置过程中出现错误: {e.Message}");
                EditorUtility.DisplayDialog("设置失败", $"设置过程中出现错误:\n{e.Message}", "确定");
            }
        }
        
        private void CreateFolderStructure()
        {
            Debug.Log("创建文件夹结构...");
            
            string[] folders = {
                "Assets/Art",
                "Assets/Art/Characters",
                "Assets/Art/Environment",
                "Assets/Art/Items",
                "Assets/Art/UI",
                "Assets/Art/Effects",
                "Assets/Audio",
                "Assets/Audio/Music",
                "Assets/Audio/SFX",
                "Assets/Audio/Ambient",
                "Assets/Scripts/Core",
                "Assets/Scripts/Gameplay",
                "Assets/Scripts/UI",
                "Assets/Scripts/Audio",
                "Assets/Scripts/Utils",
                "Assets/Prefabs",
                "Assets/Prefabs/Characters",
                "Assets/Prefabs/Environment",
                "Assets/Prefabs/Items",
                "Assets/Prefabs/UI",
                "Assets/Scenes",
                "Assets/Materials",
                "Assets/Animations",
                "Assets/Resources",
                "Assets/Settings"
            };
            
            int createdCount = 0;
            foreach (string folder in folders)
            {
                if (!Directory.Exists(folder))
                {
                    Directory.CreateDirectory(folder);
                    createdCount++;
                }
            }
            
            AssetDatabase.Refresh();
            Debug.Log($"文件夹结构创建完成，新建了 {createdCount} 个文件夹");
        }
        
        private void SetupManagerObjects()
        {
            Debug.Log("设置管理器对象...");
            
            // 创建管理器容器
            GameObject managersContainer = GameObject.Find("Managers");
            if (managersContainer == null)
            {
                managersContainer = new GameObject("Managers");
                managersContainer.tag = "EditorOnly";
            }
            
            // 创建GameManager
            if (FindObjectOfType<Core.GameManager>() == null)
            {
                GameObject gameManagerObj = new GameObject("GameManager");
                gameManagerObj.transform.SetParent(managersContainer.transform);
                gameManagerObj.AddComponent<Core.GameManager>();
                Debug.Log("创建了GameManager");
            }
            
            // 创建InputManager
            if (FindObjectOfType<Core.InputManager>() == null)
            {
                GameObject inputManagerObj = new GameObject("InputManager");
                inputManagerObj.transform.SetParent(managersContainer.transform);
                inputManagerObj.AddComponent<Core.InputManager>();
                Debug.Log("创建了InputManager");
            }
            
            // 创建ResolutionManager
            if (FindObjectOfType<Core.ResolutionManager>() == null)
            {
                GameObject resolutionManagerObj = new GameObject("ResolutionManager");
                resolutionManagerObj.transform.SetParent(managersContainer.transform);
                resolutionManagerObj.AddComponent<Core.ResolutionManager>();
                Debug.Log("创建了ResolutionManager");
            }
            
            // 创建ProjectSetupValidator
            GameObject validatorObj = new GameObject("ProjectSetupValidator");
            validatorObj.transform.SetParent(managersContainer.transform);
            validatorObj.AddComponent<ProjectSetupValidator>();
            Debug.Log("创建了ProjectSetupValidator");
            
            Debug.Log("管理器对象设置完成");
        }
        
        private void ConfigureCanvas()
        {
            Debug.Log("配置Canvas和UI...");
            
            // 查找或创建主Canvas
            Canvas mainCanvas = FindObjectOfType<Canvas>();
            if (mainCanvas == null)
            {
                GameObject canvasObj = new GameObject("MainCanvas");
                mainCanvas = canvasObj.AddComponent<Canvas>();
                canvasObj.AddComponent<CanvasScaler>();
                canvasObj.AddComponent<GraphicRaycaster>();
                Debug.Log("创建了主Canvas");
            }
            
            // 配置Canvas
            mainCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            mainCanvas.pixelPerfect = true;
            mainCanvas.sortingOrder = 0;
            
            // 配置CanvasScaler
            CanvasScaler scaler = mainCanvas.GetComponent<CanvasScaler>();
            if (scaler != null)
            {
                scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
                scaler.referenceResolution = new Vector2(1920, 1080);
                scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
                scaler.matchWidthOrHeight = 0.5f;
                scaler.referencePixelsPerUnit = 100f;
            }
            
            // 创建EventSystem
            if (FindObjectOfType<UnityEngine.EventSystems.EventSystem>() == null)
            {
                GameObject eventSystemObj = new GameObject("EventSystem");
                eventSystemObj.AddComponent<UnityEngine.EventSystems.EventSystem>();
                eventSystemObj.AddComponent<UnityEngine.EventSystems.StandaloneInputModule>();
                Debug.Log("创建了EventSystem");
            }
            
            // 创建安全区域面板
            Transform safeAreaPanel = mainCanvas.transform.Find("SafeAreaPanel");
            if (safeAreaPanel == null)
            {
                GameObject safeAreaObj = new GameObject("SafeAreaPanel");
                safeAreaObj.transform.SetParent(mainCanvas.transform, false);
                
                RectTransform rectTransform = safeAreaObj.AddComponent<RectTransform>();
                rectTransform.anchorMin = Vector2.zero;
                rectTransform.anchorMax = Vector2.one;
                rectTransform.offsetMin = Vector2.zero;
                rectTransform.offsetMax = Vector2.zero;
                
                safeAreaObj.AddComponent<UI.SafeAreaAdapter>();
                Debug.Log("创建了SafeAreaPanel");
            }
            
            Debug.Log("Canvas配置完成");
        }
        
        private void SetupAndroidSettings()
        {
            Debug.Log("配置Android设置...");
            
            // 基础设置
            PlayerSettings.companyName = "MazeAdventure Studio";
            PlayerSettings.productName = "迷宫探险";
            
            // Android特定设置
            PlayerSettings.SetApplicationIdentifier(BuildTargetGroup.Android, "com.mazeadventure.game");
            PlayerSettings.Android.minSdkVersion = AndroidSdkVersions.AndroidApiLevel21;
            PlayerSettings.Android.targetSdkVersion = AndroidSdkVersions.AndroidApiLevelAuto;
            
            // 屏幕方向
            PlayerSettings.defaultInterfaceOrientation = UIOrientation.LandscapeLeft;
            PlayerSettings.allowedAutorotateToLandscapeLeft = true;
            PlayerSettings.allowedAutorotateToLandscapeRight = true;
            PlayerSettings.allowedAutorotateToPortrait = false;
            PlayerSettings.allowedAutorotateToPortraitUpsideDown = false;
            
            // 脚本设置
            PlayerSettings.SetScriptingBackend(BuildTargetGroup.Android, ScriptingImplementation.IL2CPP);
            PlayerSettings.SetApiCompatibilityLevel(BuildTargetGroup.Android, ApiCompatibilityLevel.NET_Standard_2_0);
            
            // 架构设置
            PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARM64 | AndroidArchitecture.ARMv7;
            
            Debug.Log("Android设置配置完成");
        }
        
        private void CreateGameConfig()
        {
            Debug.Log("创建GameConfig资源...");
            
            string configPath = "Assets/Settings/GameConfig.asset";
            
            if (!File.Exists(configPath))
            {
                // 确保Settings文件夹存在
                if (!Directory.Exists("Assets/Settings"))
                {
                    Directory.CreateDirectory("Assets/Settings");
                }
                
                // 创建GameConfig实例
                Core.GameConfig config = ScriptableObject.CreateInstance<Core.GameConfig>();
                
                // 设置默认值
                config.name = "GameConfig";
                
                // 保存资源
                AssetDatabase.CreateAsset(config, configPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                Debug.Log($"创建了GameConfig资源: {configPath}");
            }
            else
            {
                Debug.Log("GameConfig资源已存在");
            }
        }
        
        private void ValidateProject()
        {
            ProjectSetupValidator validator = FindObjectOfType<ProjectSetupValidator>();
            if (validator != null)
            {
                validator.ValidateProjectSetup();
            }
            else
            {
                Debug.LogWarning("未找到ProjectSetupValidator组件，请先运行自动设置");
            }
        }
    }
}
