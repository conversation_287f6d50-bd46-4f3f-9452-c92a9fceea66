using UnityEngine;

namespace MazeAdventure.Core
{
    /// <summary>
    /// 简化版输入管理器 - 用于项目初始设置，避免编译错误
    /// </summary>
    public class InputManagerSimple : MonoBehaviour
    {
        [Header("输入设置")]
        [SerializeField] private float sensitivity = 1f;
        [SerializeField] private float deadZone = 0.1f;
        
        // 单例模式
        public static InputManagerSimple Instance { get; private set; }
        
        // 输入状态
        public Vector2 MoveInput { get; private set; }
        public bool IsTouching { get; private set; }
        
        // 事件系统
        public System.Action<Vector2> OnMoveInputChanged;
        public System.Action OnTouchStart;
        public System.Action OnTouchEnd;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Update()
        {
            HandleInput();
        }
        
        #endregion
        
        #region 输入处理
        
        private void HandleInput()
        {
            Vector2 input = Vector2.zero;
            
            // 键盘输入（用于编辑器测试）
            #if UNITY_EDITOR || UNITY_STANDALONE
            if (Input.GetKey(KeyCode.W) || Input.GetKey(KeyCode.UpArrow))
                input.y += 1;
            if (Input.GetKey(KeyCode.S) || Input.GetKey(KeyCode.DownArrow))
                input.y -= 1;
            if (Input.GetKey(KeyCode.A) || Input.GetKey(KeyCode.LeftArrow))
                input.x -= 1;
            if (Input.GetKey(KeyCode.D) || Input.GetKey(KeyCode.RightArrow))
                input.x += 1;
            #endif
            
            // 触摸输入（基础版本）
            #if UNITY_ANDROID || UNITY_IOS
            if (Input.touchCount > 0)
            {
                Touch touch = Input.GetTouch(0);
                
                if (touch.phase == TouchPhase.Began)
                {
                    IsTouching = true;
                    OnTouchStart?.Invoke();
                }
                else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
                {
                    IsTouching = false;
                    OnTouchEnd?.Invoke();
                }
                
                // 简单的触摸移动检测
                if (touch.phase == TouchPhase.Moved)
                {
                    Vector2 deltaPosition = touch.deltaPosition;
                    input = deltaPosition.normalized * sensitivity;
                }
            }
            #endif
            
            // 应用死区
            if (input.magnitude < deadZone)
            {
                input = Vector2.zero;
            }
            
            // 更新输入
            SetMoveInput(input);
        }
        
        private void SetMoveInput(Vector2 input)
        {
            if (MoveInput != input)
            {
                MoveInput = input;
                OnMoveInputChanged?.Invoke(input);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 初始化输入管理器
        /// </summary>
        public void Initialize()
        {
            Debug.Log("简化版输入管理器初始化完成");
        }
        
        /// <summary>
        /// 设置灵敏度
        /// </summary>
        public void SetSensitivity(float newSensitivity)
        {
            sensitivity = Mathf.Clamp(newSensitivity, 0.1f, 3f);
        }
        
        /// <summary>
        /// 设置死区
        /// </summary>
        public void SetDeadZone(float newDeadZone)
        {
            deadZone = Mathf.Clamp01(newDeadZone);
        }
        
        /// <summary>
        /// 震动反馈（简化版）
        /// </summary>
        public void Vibrate(float duration = 0.1f)
        {
            #if UNITY_ANDROID && !UNITY_EDITOR
            Handheld.Vibrate();
            #endif
        }
        
        /// <summary>
        /// 检查是否有移动输入
        /// </summary>
        public bool HasMoveInput()
        {
            return MoveInput.magnitude > 0.1f;
        }
        
        /// <summary>
        /// 获取移动输入角度
        /// </summary>
        public float GetMoveAngle()
        {
            if (MoveInput.magnitude < 0.1f) return 0f;
            return Mathf.Atan2(MoveInput.y, MoveInput.x) * Mathf.Rad2Deg;
        }
        
        #endregion
    }
}
