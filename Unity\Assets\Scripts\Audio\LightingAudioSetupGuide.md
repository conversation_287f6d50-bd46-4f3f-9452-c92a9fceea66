# 光照和音效系统设置指南

## 🌟 头灯光照系统设置

### 第1步：URP 2D光照配置
1. **确保使用URP渲染管线**
   ```
   Window > Package Manager
   搜索 "Universal RP" 并安装
   ```

2. **创建2D URP Asset**
   ```
   Assets > Create > Rendering > URP Asset (with 2D Renderer)
   将创建的Asset设置到 Project Settings > Graphics > Scriptable Render Pipeline Settings
   ```

3. **配置2D Renderer**
   ```
   在URP Asset中：
   - Renderer List > 选择 2D Renderer
   - 启用 "Use 2D Lighting"
   ```

### 第2步：设置头灯系统
1. **创建头灯对象**
   ```
   在Player对象下创建子对象：
   Player
   └── HeadlampSystem (添加HeadlampSystem脚本)
       └── HeadlampLight (Light2D组件)
   ```

2. **配置Light2D组件**
   ```
   Light Type: Spot
   Intensity: 1.5
   Outer Radius: 8
   Spot Angle: 60
   Color: White
   Light Order: 1
   ```

3. **HeadlampSystem脚本设置**
   ```csharp
   Max Intensity: 1.5
   Min Intensity: 0.3
   Max Range: 8
   Min Range: 3
   Cone Angle: 60
   Battery Drain Rate: 2 (每秒消耗)
   Rotation Speed: 5
   Enable Battery Drain: true
   ```

### 第3步：设置2D光照渲染
1. **创建LightingRenderer对象**
   ```
   Managers
   └── LightingRenderer (添加LightingRenderer脚本)
   ```

2. **配置环境光照**
   ```csharp
   Ambient Color: (0.1, 0.1, 0.2, 1)
   Ambient Intensity: 0.3
   Enable Dynamic Ambient: true
   ```

3. **配置阴影设置**
   ```csharp
   Enable Shadows: true
   Shadow Caster Layers: Walls (Layer 8)
   Shadow Length: 10
   Shadow Color: (0, 0, 0, 0.8)
   ```

### 第4步：为迷宫墙壁添加阴影
1. **自动添加阴影投射器**
   ```csharp
   // LightingRenderer会自动为墙壁添加ShadowCaster2D
   // 或手动为墙壁预制体添加ShadowCaster2D组件
   ```

2. **配置ShadowCaster2D**
   ```
   Self Shadows: true
   Casts Shadows: true
   Shape Path: 设置为方形路径
   ```

## 🎵 音效系统设置

### 第1步：创建音频管理器
1. **创建AudioManager对象**
   ```
   Managers
   └── AudioManager (添加AudioManager脚本)
       ├── MusicSource (AudioSource)
       ├── AmbientSource (AudioSource)
       ├── SFXSource_0 (AudioSource)
       ├── SFXSource_1 (AudioSource)
       └── ... (更多SFX音频源)
   ```

2. **配置AudioManager设置**
   ```csharp
   Max Audio Sources: 10
   Master Volume: 1.0
   Music Volume: 0.7
   SFX Volume: 0.8
   Ambient Volume: 0.5
   Enable Spatial Audio: false (2D游戏)
   Mute On Focus Loss: true
   ```

### 第2步：创建音乐管理器
1. **创建MusicManager对象**
   ```
   Managers
   └── MusicManager (添加MusicManager脚本)
       ├── PrimaryMusicSource (AudioSource)
       └── SecondaryMusicSource (AudioSource)
   ```

2. **配置音乐轨道**
   ```csharp
   Music Tracks数组:
   - Menu Music (MusicType.Menu)
   - Ambient Music (MusicType.Ambient)
   - Exploration Music (MusicType.Exploration)
   - Tension Music (MusicType.Tension)
   - Victory Music (MusicType.Victory)
   - Game Over Music (MusicType.GameOver)
   ```

### 第3步：音频资源准备
1. **音乐文件格式建议**
   ```
   格式: OGG Vorbis (最佳压缩)
   采样率: 44.1kHz
   比特率: 128-192 kbps
   循环: 启用无缝循环
   ```

2. **音效文件格式建议**
   ```
   格式: WAV (短音效) 或 OGG (长音效)
   采样率: 22.05kHz (节省内存)
   比特率: 16-bit
   长度: 尽量短小精悍
   ```

3. **音频导入设置**
   ```
   Load Type: Compressed In Memory (音乐)
   Load Type: Decompress On Load (短音效)
   Compression Format: Vorbis (音乐) / PCM (音效)
   Quality: 70-100 (根据需要调整)
   ```

## ⚡ 性能优化设置

### 第1步：创建光照优化器
1. **创建LightingOptimizer对象**
   ```
   Managers
   └── LightingOptimizer (添加LightingOptimizer脚本)
   ```

2. **配置性能设置**
   ```csharp
   Max Active Lights: 4 (中等设备)
   Light Culling Distance: 12
   Shadow Culling Distance: 8
   Max Shadow Casters: 20
   Target Quality: Medium
   Adaptive Quality: true
   Target Frame Rate: 30
   ```

### 第2步：移动设备优化
1. **URP Asset优化**
   ```
   Rendering:
   - Main Light: Enabled
   - Additional Lights: Disabled (节省性能)
   - Additional Light Shadows: Disabled
   
   2D Renderer:
   - Light Blend Styles: 最多2个
   - Use Normal Map: 根据需要
   ```

2. **质量设置**
   ```
   Edit > Project Settings > Quality
   创建三个质量级别:
   - Low: 2个光源，简化阴影
   - Medium: 4个光源，标准阴影
   - High: 8个光源，完整效果
   ```

## 🎮 集成到游戏循环

### 第1步：更新GameLoopManager
```csharp
// 在InitializeGameplaySystems中添加
headlampSystem = HeadlampSystem.Instance;
lightingRenderer = LightingRenderer.Instance;
lightingOptimizer = LightingOptimizer.Instance;
audioManager = AudioManager.Instance;
musicManager = MusicManager.Instance;
```

### 第2步：事件连接
```csharp
// 在SetupSystemConnections中添加
if (headlampSystem != null && playerController != null)
{
    // 头灯跟随玩家方向
    playerController.OnDirectionChanged += headlampSystem.UpdateDirection;
}

if (audioManager != null && gameStateManager != null)
{
    // 音效响应游戏状态
    gameStateManager.OnGameStateChanged += audioManager.OnGameStateChanged;
}
```

## 🔧 测试和调试

### 第1步：光照测试
1. **头灯功能测试**
   - 按L键切换头灯开关
   - 移动玩家观察光照方向跟随
   - 检查电量消耗和光照强度变化
   - 测试低电量时的闪烁效果

2. **性能测试**
   - 开启性能统计显示
   - 观察FPS和活跃光源数量
   - 测试自适应质量调整

### 第2步：音效测试
1. **音乐系统测试**
   - 测试不同游戏状态的音乐切换
   - 检查音乐淡入淡出效果
   - 验证动态音乐系统

2. **音效测试**
   - 测试脚步声播放
   - 检查道具拾取音效
   - 验证UI交互音效

### 第3步：移动设备测试
1. **构建到Android设备**
2. **测试性能表现**
3. **调整质量设置**

## 📱 手机优化建议

### 性能优化
1. **光照优化**
   - 限制同时活跃的光源数量
   - 使用距离剔除
   - 启用LOD系统

2. **音频优化**
   - 压缩音频文件
   - 限制同时播放的音效数量
   - 使用音频池管理

### 电量优化
1. **降低更新频率**
   - 光照更新间隔
   - 性能检查间隔

2. **智能休眠**
   - 应用失去焦点时暂停
   - 减少后台处理

## 🎯 完成检查清单

### 光照系统
- [ ] URP 2D光照正确配置
- [ ] 头灯系统正常工作
- [ ] 光照方向跟随玩家
- [ ] 电量消耗机制正常
- [ ] 阴影投射正确显示
- [ ] 性能优化生效

### 音效系统
- [ ] 音频管理器正常工作
- [ ] 背景音乐播放正常
- [ ] 音效响应游戏事件
- [ ] 音量控制功能正常
- [ ] 动态音乐系统工作
- [ ] 手机音频性能良好

### 性能表现
- [ ] 目标帧率稳定
- [ ] 自适应质量工作
- [ ] 内存使用合理
- [ ] 电量消耗可接受

完成这些设置后，你的游戏将拥有专业级的光照和音效系统！

## 🚀 扩展功能建议

### 高级光照效果
- 动态阴影
- 光照粒子效果
- 环境光遮蔽

### 高级音效功能
- 3D空间音效
- 音效混响
- 动态音量调节

### 性能监控
- 实时性能面板
- 自动质量调节
- 热点分析工具
