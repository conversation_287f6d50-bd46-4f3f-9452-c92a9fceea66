# 32x32像素艺术制作指南

## 设计规范

### 基础规格
- **精灵尺寸**: 32x32像素
- **色彩深度**: 16-32色调色板
- **像素完美**: 确保1:1像素映射
- **动画帧率**: 8-12 FPS
- **导出格式**: PNG (无压缩)

### 色彩方案
```
主色调色板：
- 背景色: #1a1a1a (深灰黑)
- 墙壁色: #404040 (中灰)
- 地面色: #2a2a2a (暗灰)
- 玩家色: #00bcd4 (青色)
- 电池色: #4caf50 (绿色)
- 陷阱色: #f44336 (红色)
- 光照色: #fff8dc (暖白)
```

### 像素艺术原则
1. **像素完美对齐**: 所有线条和边缘对齐到像素网格
2. **一致的光源**: 统一的光照方向（左上角）
3. **有限色彩**: 每个精灵最多使用8-12种颜色
4. **清晰轮廓**: 使用深色轮廓线增强可读性
5. **抖动技术**: 使用抖动创建渐变效果

## 角色设计

### 玩家角色 (32x32)
**设计要求：**
- 头戴头灯的探险者形象
- 简洁但可识别的轮廓
- 4方向移动动画（上下左右）
- 每个方向3-4帧动画

**动画状态：**
1. **待机动画** (2帧)
   - 轻微的呼吸动作
   - 头灯微弱闪烁

2. **移动动画** (4帧 x 4方向)
   - 走路循环动画
   - 头灯随移动方向调整

3. **受伤动画** (3帧)
   - 红色闪烁效果
   - 轻微后退动作

**文件命名：**
```
player_idle_01.png
player_idle_02.png
player_walk_up_01.png
player_walk_up_02.png
player_walk_up_03.png
player_walk_up_04.png
player_walk_down_01.png
...
player_hurt_01.png
player_hurt_02.png
player_hurt_03.png
```

### NPC/敌人 (32x32)
**巡逻机关** (可选扩展内容)
- 简单的机械外观
- 红色警示灯
- 左右移动动画

## 环境资源

### 迷宫元素 (32x32)
1. **墙壁瓦片**
   - wall_solid.png (实心墙)
   - wall_corner_tl.png (左上角)
   - wall_corner_tr.png (右上角)
   - wall_corner_bl.png (左下角)
   - wall_corner_br.png (右下角)
   - wall_horizontal.png (水平墙)
   - wall_vertical.png (垂直墙)

2. **地面瓦片**
   - floor_basic.png (基础地面)
   - floor_cracked.png (破损地面)
   - floor_wet.png (潮湿地面)

3. **装饰元素**
   - debris_small.png (小碎石)
   - debris_large.png (大碎石)
   - puddle.png (水坑)
   - moss.png (苔藓)

### 特殊区域 (32x32)
1. **入口/出口**
   - entrance.png (入口标记)
   - exit.png (出口标记)
   - exit_glow.png (出口发光效果)

2. **房间元素**
   - pillar.png (柱子)
   - altar.png (祭坛)
   - chest.png (宝箱)

## 道具设计

### 电池 (16x16, 放置在32x32画布中心)
**设计要求：**
- 明显的电池外形
- 绿色主色调
- 发光动画效果

**动画：**
1. **闪烁动画** (4帧)
   - 亮度变化
   - 轻微的颜色变化

**文件：**
```
battery_01.png
battery_02.png
battery_03.png
battery_04.png
```

### 医疗包 (16x16)
**设计要求：**
- 红十字标记
- 白色/红色配色
- 静态图标

**文件：**
```
health_pack.png
```

### 特殊道具 (16x16)
1. **速度提升**
   - speed_boost.png (蓝色靴子图标)

2. **护盾**
   - shield.png (金色盾牌图标)

3. **额外电池**
   - battery_large.png (大电池)

## 机关陷阱

### 尖刺陷阱 (32x32)
**设计要求：**
- 地面嵌入式尖刺
- 灰色金属质感
- 触发动画

**动画状态：**
1. **隐藏状态** (1帧)
   - 几乎与地面齐平

2. **触发动画** (6帧)
   - 尖刺快速弹出
   - 红色警告效果

3. **激活状态** (2帧)
   - 尖刺完全伸出
   - 轻微摇摆

**文件：**
```
spike_hidden.png
spike_trigger_01.png
spike_trigger_02.png
spike_trigger_03.png
spike_trigger_04.png
spike_trigger_05.png
spike_trigger_06.png
spike_active_01.png
spike_active_02.png
```

### 移动机关 (32x32)
**设计要求：**
- 机械外观
- 红色危险标识
- 移动轨迹指示

**动画：**
1. **移动动画** (4帧)
   - 左右移动循环
   - 机械部件转动

## UI元素

### HUD元素
1. **电量条背景** (200x20)
   - hud_battery_bg.png

2. **电量条填充** (196x16)
   - hud_battery_fill.png (绿色渐变)

3. **生命条背景** (200x20)
   - hud_health_bg.png

4. **生命条填充** (196x16)
   - hud_health_fill.png (红色渐变)

### 虚拟摇杆 (128x128)
1. **摇杆背景**
   - joystick_bg.png (半透明圆形)

2. **摇杆手柄** (64x64)
   - joystick_handle.png (实心圆形)

### 按钮元素 (64x32)
1. **普通按钮**
   - button_normal.png
   - button_pressed.png
   - button_disabled.png

2. **特殊按钮**
   - button_pause.png
   - button_settings.png
   - button_restart.png

## 特效资源

### 光照效果
1. **手电筒光束** (256x256)
   - flashlight_beam.png (径向渐变)
   - flashlight_weak.png (弱光效果)

2. **道具光效** (64x64)
   - item_glow.png (柔和光晕)
   - pickup_effect.png (拾取特效)

### 粒子效果
1. **火花效果** (8x8)
   - spark_01.png
   - spark_02.png
   - spark_03.png

2. **烟雾效果** (16x16)
   - smoke_01.png
   - smoke_02.png
   - smoke_03.png

## 制作工具推荐

### 像素艺术软件
1. **Aseprite** (推荐)
   - 专业像素艺术工具
   - 强大的动画功能
   - 洋葱皮功能

2. **Photoshop**
   - 关闭抗锯齿
   - 使用铅笔工具
   - 设置像素网格

3. **GIMP** (免费)
   - 像素艺术插件
   - 索引颜色模式

### 调色板工具
1. **Lospec Palette List**
2. **Adobe Color**
3. **Coolors.co**

## 导出设置

### Unity导入设置
```
Texture Type: Sprite (2D and UI)
Sprite Mode: Single
Pixels Per Unit: 32
Filter Mode: Point (no filter)
Compression: None
Max Size: 32 (for 32x32 sprites)
```

### 批量处理
- 使用相同的导入设置
- 创建预设模板
- 自动化导入流程

## 质量检查清单

### 技术检查
- [ ] 像素完美对齐
- [ ] 正确的尺寸 (32x32)
- [ ] 透明背景
- [ ] 无抗锯齿
- [ ] 一致的色彩深度

### 艺术检查
- [ ] 统一的艺术风格
- [ ] 清晰的轮廓
- [ ] 适当的对比度
- [ ] 一致的光照方向
- [ ] 符合游戏主题

### 动画检查
- [ ] 流畅的动画循环
- [ ] 合适的帧率
- [ ] 无跳跃或闪烁
- [ ] 正确的时序
- [ ] 文件命名规范
