# 角色设计详细指南

## 玩家角色设计

### 设计概念
**探险者形象**
- 头戴矿工头灯的冒险家
- 简洁但富有特色的轮廓
- 在32x32像素内清晰可辨
- 适合4方向移动动画

### 像素艺术制作步骤

#### 第一步：基础轮廓 (8x8像素网格规划)
```
在32x32画布中，角色占用中心24x28区域
留出4像素边距用于动画偏移

基础比例：
- 头部: 8x8像素
- 身体: 8x12像素  
- 腿部: 8x8像素
- 总高度: 28像素
```

#### 第二步：色彩方案
```
主要颜色 (6色)：
#1a1a1a - 轮廓线/阴影
#404040 - 深色部分
#606060 - 中间色调
#808080 - 亮色部分
#00bcd4 - 角色主色 (青色)
#fff8dc - 头灯光色
```

#### 第三步：逐像素绘制

**正面视图 (player_idle_front.png)**
```
像素坐标图 (32x32):
行1-4: 透明背景
行5-12: 头部和头灯
  - 头灯位置: (14,5) 到 (18,7)
  - 头部轮廓: (12,6) 到 (20,12)
行13-24: 身体部分
  - 肩膀: (11,13) 到 (21,15)
  - 躯干: (12,16) 到 (20,22)
  - 手臂: (10,16) 到 (11,20) 和 (21,16) 到 (22,20)
行25-32: 腿部
  - 左腿: (13,25) 到 (15,32)
  - 右腿: (17,25) 到 (19,32)
```

**侧面视图 (player_walk_right_01.png)**
```
调整要点：
- 头灯方向改变，指向右侧
- 身体略微转向侧面
- 一条腿向前，一条腿向后
- 手臂摆动姿势
```

### 动画制作指南

#### 待机动画 (2帧，1秒循环)
**帧1: player_idle_01.png**
- 基础站立姿势
- 头灯正常亮度

**帧2: player_idle_02.png**
- 轻微的身体上下移动 (1像素)
- 头灯稍微变亮

**时序设置:**
```
帧1: 0.7秒
帧2: 0.3秒
循环: 无限
```

#### 移动动画 (4帧，0.5秒循环)

**向右移动序列:**

**帧1: player_walk_right_01.png**
- 左脚向前，右脚向后
- 左臂向后，右臂向前
- 身体重心在左脚

**帧2: player_walk_right_02.png**
- 双脚并拢，身体直立
- 手臂回到中性位置
- 过渡帧

**帧3: player_walk_right_03.png**
- 右脚向前，左脚向后
- 右臂向后，左臂向前
- 身体重心在右脚

**帧4: player_walk_right_04.png**
- 双脚并拢，身体直立
- 手臂回到中性位置
- 过渡帧

**时序设置:**
```
每帧: 0.125秒
总循环: 0.5秒
循环: 无限
```

#### 其他方向动画
**向左移动**: 水平翻转右移动画
**向上移动**: 背面视图，类似移动动画
**向下移动**: 正面视图，类似移动动画

#### 受伤动画 (3帧，0.3秒播放一次)
**帧1: player_hurt_01.png**
- 正常颜色，轻微后退
- 头灯闪烁

**帧2: player_hurt_02.png**
- 红色叠加效果 (#f44336, 50%透明度)
- 身体向后倾斜2像素

**帧3: player_hurt_03.png**
- 恢复正常颜色
- 回到正常姿势

### 头灯效果设计

#### 头灯精灵 (独立图层)
**尺寸**: 6x4像素
**位置**: 角色头部前方
**颜色**: #fff8dc (暖白色)

#### 头灯状态
1. **正常状态**: 稳定发光
2. **低电量**: 闪烁效果
3. **无电量**: 微弱红光

#### 方向性头灯
- **正面**: 圆形光点
- **侧面**: 椭圆形光点
- **背面**: 不可见或微弱边缘光

### 制作技巧

#### 像素完美技巧
1. **使用网格**: 始终对齐到像素网格
2. **避免抗锯齿**: 使用铅笔工具，关闭平滑
3. **一致的线宽**: 轮廓线保持1像素宽度
4. **对称性**: 使用镜像工具确保对称

#### 动画流畅性
1. **关键帧**: 确定极端姿势
2. **中间帧**: 添加过渡帧
3. **弧线运动**: 身体部位沿弧线移动
4. **重叠动作**: 不同部位的动作有时间差

#### 色彩运用
1. **限制色彩**: 每个精灵最多8种颜色
2. **对比度**: 确保足够的明暗对比
3. **色彩层次**: 使用3-4个明度层次
4. **统一光源**: 保持一致的光照方向

### Aseprite制作流程

#### 项目设置
```
画布尺寸: 32x32像素
色彩模式: RGB
背景: 透明
网格: 1x1像素，显示
```

#### 图层结构
```
图层4: 特效 (头灯光晕)
图层3: 细节 (头灯、装备)
图层2: 主体 (身体、四肢)
图层1: 轮廓 (黑色边框)
```

#### 动画时间轴
```
标签: idle (帧1-2)
标签: walk_right (帧3-6)
标签: walk_left (帧7-10)
标签: walk_up (帧11-14)
标签: walk_down (帧15-18)
标签: hurt (帧19-21)
```

#### 导出设置
```
格式: PNG
尺寸: 32x32
透明背景: 是
索引颜色: 否
抖动: 否
```

### 质量检查清单

#### 技术检查
- [ ] 32x32像素尺寸正确
- [ ] 透明背景
- [ ] 像素完美对齐
- [ ] 无抗锯齿
- [ ] 文件命名规范

#### 艺术检查
- [ ] 轮廓清晰可辨
- [ ] 色彩对比充足
- [ ] 风格统一一致
- [ ] 细节适当简化
- [ ] 头灯特征明显

#### 动画检查
- [ ] 循环流畅无跳跃
- [ ] 时序节奏合适
- [ ] 各方向动画一致
- [ ] 关键帧姿势清晰
- [ ] 过渡自然

### 常见问题解决

#### 问题1: 角色太小看不清
**解决方案**: 
- 增强轮廓线对比度
- 简化细节，突出主要特征
- 使用更鲜明的色彩

#### 问题2: 动画不流畅
**解决方案**:
- 增加中间帧
- 调整帧时序
- 检查关键帧姿势

#### 问题3: 头灯效果不明显
**解决方案**:
- 增加头灯尺寸
- 使用更亮的颜色
- 添加发光动画

#### 问题4: 不同方向不一致
**解决方案**:
- 使用统一的比例
- 保持相同的色彩方案
- 确保头灯位置合理

### 扩展角色 (可选)

#### NPC角色
- 巡逻机器人 (敌对)
- 其他探险者 (中性)
- 商人角色 (友好)

#### 角色变体
- 不同装备的探险者
- 受伤状态的外观变化
- 特殊能力激活状态

每个扩展角色都应遵循相同的制作规范和质量标准。
