using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;

namespace MazeAdventure.UI
{
    /// <summary>
    /// UI动画管理器 - 管理所有UI元素的动画效果
    /// </summary>
    public class UIAnimationManager : MonoBehaviour
    {
        [Header("动画设置")]
        [SerializeField] private float defaultAnimationDuration = 0.3f;
        [SerializeField] private Ease defaultEaseType = Ease.OutQuart;
        [SerializeField] private bool enableUIAnimations = true;
        
        [Header("面板动画")]
        [SerializeField] private float panelFadeInDuration = 0.4f;
        [SerializeField] private float panelFadeOutDuration = 0.3f;
        [SerializeField] private Vector3 panelSlideOffset = new Vector3(0, 50f, 0);
        [SerializeField] private Ease panelEaseIn = Ease.OutBack;
        [SerializeField] private Ease panelEaseOut = Ease.InBack;
        
        [Header("按钮动画")]
        [SerializeField] private float buttonScaleDuration = 0.1f;
        [SerializeField] private float buttonHoverScale = 1.1f;
        [SerializeField] private float buttonPressScale = 0.95f;
        [SerializeField] private Color buttonHoverColor = Color.white;
        [SerializeField] private Color buttonPressColor = Color.gray;
        
        [Header("弹窗动画")]
        [SerializeField] private float popupScaleDuration = 0.5f;
        [SerializeField] private Vector3 popupStartScale = Vector3.zero;
        [SerializeField] private Vector3 popupEndScale = Vector3.one;
        [SerializeField] private Ease popupEase = Ease.OutBounce;
        
        [Header("文本动画")]
        [SerializeField] private float textTypewriterSpeed = 50f;
        [SerializeField] private float textFadeInDuration = 0.2f;
        [SerializeField] private float textCountUpDuration = 1f;
        
        [Header("特效动画")]
        [SerializeField] private float pulseAnimationDuration = 1f;
        [SerializeField] private float pulseScale = 1.2f;
        [SerializeField] private float shakeIntensity = 10f;
        [SerializeField] private float shakeDuration = 0.5f;
        
        // 单例模式
        public static UIAnimationManager Instance { get; private set; }
        
        // 动画缓存
        private Dictionary<GameObject, Tween> activeTweens;
        private Dictionary<GameObject, Sequence> activeSequences;
        
        // 预设动画序列
        private Dictionary<string, AnimationPreset> animationPresets;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeAnimationManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            SetupAnimationManager();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeAnimationManager()
        {
            activeTweens = new Dictionary<GameObject, Tween>();
            activeSequences = new Dictionary<GameObject, Sequence>();
            animationPresets = new Dictionary<string, AnimationPreset>();
            
            // 初始化DOTween设置
            DOTween.Init(false, true, LogBehaviour.ErrorsOnly);
            DOTween.defaultAutoPlay = AutoPlay.All;
            DOTween.defaultUpdateType = UpdateType.Normal;
        }
        
        private void SetupAnimationManager()
        {
            // 创建预设动画
            CreateAnimationPresets();
            
            Debug.Log("UI动画管理器初始化完成");
        }
        
        private void CreateAnimationPresets()
        {
            // 面板淡入动画预设
            animationPresets["PanelFadeIn"] = new AnimationPreset
            {
                duration = panelFadeInDuration,
                ease = panelEaseIn,
                animationType = AnimationType.FadeIn
            };
            
            // 面板淡出动画预设
            animationPresets["PanelFadeOut"] = new AnimationPreset
            {
                duration = panelFadeOutDuration,
                ease = panelEaseOut,
                animationType = AnimationType.FadeOut
            };
            
            // 按钮悬停动画预设
            animationPresets["ButtonHover"] = new AnimationPreset
            {
                duration = buttonScaleDuration,
                ease = Ease.OutQuart,
                animationType = AnimationType.Scale,
                targetScale = Vector3.one * buttonHoverScale
            };
            
            // 弹窗显示动画预设
            animationPresets["PopupShow"] = new AnimationPreset
            {
                duration = popupScaleDuration,
                ease = popupEase,
                animationType = AnimationType.Scale,
                startScale = popupStartScale,
                targetScale = popupEndScale
            };
        }
        
        #endregion
        
        #region 面板动画
        
        /// <summary>
        /// 面板淡入动画
        /// </summary>
        public void FadeInPanel(GameObject panel, System.Action onComplete = null)
        {
            if (!enableUIAnimations || panel == null) 
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(panel);
            
            CanvasGroup canvasGroup = GetOrAddCanvasGroup(panel);
            RectTransform rectTransform = panel.GetComponent<RectTransform>();
            
            // 设置初始状态
            canvasGroup.alpha = 0f;
            canvasGroup.interactable = false;
            canvasGroup.blocksRaycasts = false;
            
            if (rectTransform != null)
            {
                rectTransform.anchoredPosition += panelSlideOffset;
            }
            
            // 创建动画序列
            Sequence sequence = DOTween.Sequence();
            
            // 淡入
            sequence.Append(canvasGroup.DOFade(1f, panelFadeInDuration).SetEase(panelEaseIn));
            
            // 滑入
            if (rectTransform != null)
            {
                sequence.Join(rectTransform.DOAnchorPos(rectTransform.anchoredPosition - panelSlideOffset, panelFadeInDuration).SetEase(panelEaseIn));
            }
            
            // 完成回调
            sequence.OnComplete(() => {
                canvasGroup.interactable = true;
                canvasGroup.blocksRaycasts = true;
                onComplete?.Invoke();
            });
            
            activeSequences[panel] = sequence;
        }
        
        /// <summary>
        /// 面板淡出动画
        /// </summary>
        public void FadeOutPanel(GameObject panel, System.Action onComplete = null)
        {
            if (!enableUIAnimations || panel == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(panel);
            
            CanvasGroup canvasGroup = GetOrAddCanvasGroup(panel);
            RectTransform rectTransform = panel.GetComponent<RectTransform>();
            
            // 禁用交互
            canvasGroup.interactable = false;
            canvasGroup.blocksRaycasts = false;
            
            // 创建动画序列
            Sequence sequence = DOTween.Sequence();
            
            // 淡出
            sequence.Append(canvasGroup.DOFade(0f, panelFadeOutDuration).SetEase(panelEaseOut));
            
            // 滑出
            if (rectTransform != null)
            {
                sequence.Join(rectTransform.DOAnchorPos(rectTransform.anchoredPosition + panelSlideOffset, panelFadeOutDuration).SetEase(panelEaseOut));
            }
            
            // 完成回调
            sequence.OnComplete(() => {
                onComplete?.Invoke();
            });
            
            activeSequences[panel] = sequence;
        }
        
        #endregion
        
        #region 按钮动画
        
        /// <summary>
        /// 按钮悬停动画
        /// </summary>
        public void AnimateButtonHover(Button button, bool isHovering)
        {
            if (!enableUIAnimations || button == null) return;
            
            StopAnimation(button.gameObject);
            
            float targetScale = isHovering ? buttonHoverScale : 1f;
            Color targetColor = isHovering ? buttonHoverColor : Color.white;
            
            // 缩放动画
            Tween scaleTween = button.transform.DOScale(targetScale, buttonScaleDuration).SetEase(Ease.OutQuart);
            
            // 颜色动画
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                scaleTween.Join(buttonImage.DOColor(targetColor, buttonScaleDuration));
            }
            
            activeTweens[button.gameObject] = scaleTween;
        }
        
        /// <summary>
        /// 按钮点击动画
        /// </summary>
        public void AnimateButtonPress(Button button, System.Action onComplete = null)
        {
            if (!enableUIAnimations || button == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(button.gameObject);
            
            // 创建按压动画序列
            Sequence sequence = DOTween.Sequence();
            
            // 按下
            sequence.Append(button.transform.DOScale(buttonPressScale, buttonScaleDuration * 0.5f).SetEase(Ease.OutQuart));
            
            // 弹起
            sequence.Append(button.transform.DOScale(1f, buttonScaleDuration * 0.5f).SetEase(Ease.OutBack));
            
            // 颜色变化
            Image buttonImage = button.GetComponent<Image>();
            if (buttonImage != null)
            {
                sequence.Join(buttonImage.DOColor(buttonPressColor, buttonScaleDuration * 0.5f).SetLoops(2, LoopType.Yoyo));
            }
            
            sequence.OnComplete(() => onComplete?.Invoke());
            
            activeSequences[button.gameObject] = sequence;
        }
        
        /// <summary>
        /// 设置按钮动画事件
        /// </summary>
        public void SetupButtonAnimations(Button button)
        {
            if (button == null) return;
            
            // 添加事件触发器
            var eventTrigger = button.gameObject.GetComponent<UnityEngine.EventSystems.EventTrigger>();
            if (eventTrigger == null)
            {
                eventTrigger = button.gameObject.AddComponent<UnityEngine.EventSystems.EventTrigger>();
            }
            
            // 鼠标进入
            var pointerEnter = new UnityEngine.EventSystems.EventTrigger.Entry();
            pointerEnter.eventID = UnityEngine.EventSystems.EventTriggerType.PointerEnter;
            pointerEnter.callback.AddListener((data) => AnimateButtonHover(button, true));
            eventTrigger.triggers.Add(pointerEnter);
            
            // 鼠标离开
            var pointerExit = new UnityEngine.EventSystems.EventTrigger.Entry();
            pointerExit.eventID = UnityEngine.EventSystems.EventTriggerType.PointerExit;
            pointerExit.callback.AddListener((data) => AnimateButtonHover(button, false));
            eventTrigger.triggers.Add(pointerExit);
            
            // 点击
            var pointerClick = new UnityEngine.EventSystems.EventTrigger.Entry();
            pointerClick.eventID = UnityEngine.EventSystems.EventTriggerType.PointerClick;
            pointerClick.callback.AddListener((data) => AnimateButtonPress(button));
            eventTrigger.triggers.Add(pointerClick);
        }
        
        #endregion
        
        #region 弹窗动画
        
        /// <summary>
        /// 弹窗显示动画
        /// </summary>
        public void ShowPopup(GameObject popup, System.Action onComplete = null)
        {
            if (!enableUIAnimations || popup == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(popup);
            
            // 设置初始状态
            popup.transform.localScale = popupStartScale;
            CanvasGroup canvasGroup = GetOrAddCanvasGroup(popup);
            canvasGroup.alpha = 0f;
            
            // 创建动画序列
            Sequence sequence = DOTween.Sequence();
            
            // 缩放动画
            sequence.Append(popup.transform.DOScale(popupEndScale, popupScaleDuration).SetEase(popupEase));
            
            // 淡入动画
            sequence.Join(canvasGroup.DOFade(1f, popupScaleDuration * 0.8f).SetEase(Ease.OutQuart));
            
            sequence.OnComplete(() => onComplete?.Invoke());
            
            activeSequences[popup] = sequence;
        }
        
        /// <summary>
        /// 弹窗隐藏动画
        /// </summary>
        public void HidePopup(GameObject popup, System.Action onComplete = null)
        {
            if (!enableUIAnimations || popup == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(popup);
            
            CanvasGroup canvasGroup = GetOrAddCanvasGroup(popup);
            
            // 创建动画序列
            Sequence sequence = DOTween.Sequence();
            
            // 缩放动画
            sequence.Append(popup.transform.DOScale(popupStartScale, popupScaleDuration * 0.6f).SetEase(Ease.InBack));
            
            // 淡出动画
            sequence.Join(canvasGroup.DOFade(0f, popupScaleDuration * 0.6f).SetEase(Ease.InQuart));
            
            sequence.OnComplete(() => onComplete?.Invoke());
            
            activeSequences[popup] = sequence;
        }
        
        #endregion
        
        #region 文本动画
        
        /// <summary>
        /// 打字机效果
        /// </summary>
        public void TypewriterText(Text textComponent, string fullText, System.Action onComplete = null)
        {
            if (!enableUIAnimations || textComponent == null)
            {
                if (textComponent != null) textComponent.text = fullText;
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(textComponent.gameObject);
            
            textComponent.text = "";
            
            float duration = fullText.Length / textTypewriterSpeed;
            
            Tween textTween = textComponent.DOText(fullText, duration).SetEase(Ease.Linear);
            textTween.OnComplete(() => onComplete?.Invoke());
            
            activeTweens[textComponent.gameObject] = textTween;
        }
        
        /// <summary>
        /// 数字计数动画
        /// </summary>
        public void AnimateNumberCount(Text textComponent, int fromValue, int toValue, System.Action onComplete = null)
        {
            if (!enableUIAnimations || textComponent == null)
            {
                if (textComponent != null) textComponent.text = toValue.ToString();
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(textComponent.gameObject);
            
            Tween countTween = DOTween.To(() => fromValue, x => {
                textComponent.text = x.ToString();
            }, toValue, textCountUpDuration).SetEase(Ease.OutQuart);
            
            countTween.OnComplete(() => onComplete?.Invoke());
            
            activeTweens[textComponent.gameObject] = countTween;
        }
        
        /// <summary>
        /// 文本淡入动画
        /// </summary>
        public void FadeInText(Text textComponent, System.Action onComplete = null)
        {
            if (!enableUIAnimations || textComponent == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(textComponent.gameObject);
            
            Color originalColor = textComponent.color;
            textComponent.color = new Color(originalColor.r, originalColor.g, originalColor.b, 0f);
            
            Tween fadeTween = textComponent.DOFade(originalColor.a, textFadeInDuration).SetEase(Ease.OutQuart);
            fadeTween.OnComplete(() => onComplete?.Invoke());
            
            activeTweens[textComponent.gameObject] = fadeTween;
        }
        
        #endregion
        
        #region 特效动画
        
        /// <summary>
        /// 脉冲动画
        /// </summary>
        public void PulseAnimation(GameObject target, int loopCount = -1)
        {
            if (!enableUIAnimations || target == null) return;
            
            StopAnimation(target);
            
            Tween pulseTween = target.transform.DOScale(pulseScale, pulseAnimationDuration * 0.5f)
                .SetEase(Ease.InOutSine)
                .SetLoops(loopCount, LoopType.Yoyo);
            
            activeTweens[target] = pulseTween;
        }
        
        /// <summary>
        /// 震动动画
        /// </summary>
        public void ShakeAnimation(GameObject target, System.Action onComplete = null)
        {
            if (!enableUIAnimations || target == null)
            {
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(target);
            
            RectTransform rectTransform = target.GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                Tween shakeTween = rectTransform.DOShakeAnchorPos(shakeDuration, shakeIntensity, 10, 90, false, true);
                shakeTween.OnComplete(() => onComplete?.Invoke());
                
                activeTweens[target] = shakeTween;
            }
        }
        
        /// <summary>
        /// 旋转动画
        /// </summary>
        public void RotateAnimation(GameObject target, float duration, int rotations = 1)
        {
            if (!enableUIAnimations || target == null) return;
            
            StopAnimation(target);
            
            Tween rotateTween = target.transform.DORotate(new Vector3(0, 0, 360f * rotations), duration, RotateMode.FastBeyond360)
                .SetEase(Ease.Linear);
            
            activeTweens[target] = rotateTween;
        }
        
        #endregion
        
        #region 滑块和进度条动画
        
        /// <summary>
        /// 滑块值动画
        /// </summary>
        public void AnimateSliderValue(Slider slider, float targetValue, System.Action onComplete = null)
        {
            if (!enableUIAnimations || slider == null)
            {
                if (slider != null) slider.value = targetValue;
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(slider.gameObject);
            
            Tween sliderTween = slider.DOValue(targetValue, defaultAnimationDuration).SetEase(defaultEaseType);
            sliderTween.OnComplete(() => onComplete?.Invoke());
            
            activeTweens[slider.gameObject] = sliderTween;
        }
        
        /// <summary>
        /// 进度条填充动画
        /// </summary>
        public void AnimateProgressBar(Image progressBar, float targetFillAmount, System.Action onComplete = null)
        {
            if (!enableUIAnimations || progressBar == null)
            {
                if (progressBar != null) progressBar.fillAmount = targetFillAmount;
                onComplete?.Invoke();
                return;
            }
            
            StopAnimation(progressBar.gameObject);
            
            Tween fillTween = progressBar.DOFillAmount(targetFillAmount, defaultAnimationDuration).SetEase(defaultEaseType);
            fillTween.OnComplete(() => onComplete?.Invoke());
            
            activeTweens[progressBar.gameObject] = fillTween;
        }
        
        #endregion
        
        #region 工具方法
        
        /// <summary>
        /// 停止指定对象的动画
        /// </summary>
        public void StopAnimation(GameObject target)
        {
            if (target == null) return;
            
            if (activeTweens.ContainsKey(target))
            {
                activeTweens[target]?.Kill();
                activeTweens.Remove(target);
            }
            
            if (activeSequences.ContainsKey(target))
            {
                activeSequences[target]?.Kill();
                activeSequences.Remove(target);
            }
        }
        
        /// <summary>
        /// 停止所有动画
        /// </summary>
        public void StopAllAnimations()
        {
            foreach (var tween in activeTweens.Values)
            {
                tween?.Kill();
            }
            activeTweens.Clear();
            
            foreach (var sequence in activeSequences.Values)
            {
                sequence?.Kill();
            }
            activeSequences.Clear();
        }
        
        /// <summary>
        /// 获取或添加CanvasGroup组件
        /// </summary>
        private CanvasGroup GetOrAddCanvasGroup(GameObject target)
        {
            CanvasGroup canvasGroup = target.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
            {
                canvasGroup = target.AddComponent<CanvasGroup>();
            }
            return canvasGroup;
        }
        
        /// <summary>
        /// 设置动画启用状态
        /// </summary>
        public void SetAnimationsEnabled(bool enabled)
        {
            enableUIAnimations = enabled;
            
            if (!enabled)
            {
                StopAllAnimations();
            }
        }
        
        /// <summary>
        /// 批量设置按钮动画
        /// </summary>
        public void SetupButtonAnimations(Button[] buttons)
        {
            foreach (Button button in buttons)
            {
                if (button != null)
                {
                    SetupButtonAnimations(button);
                }
            }
        }
        
        /// <summary>
        /// 获取动画状态信息
        /// </summary>
        public string GetAnimationStatus()
        {
            return $"UI动画状态:\n" +
                   $"动画启用: {enableUIAnimations}\n" +
                   $"活跃Tween: {activeTweens.Count}\n" +
                   $"活跃Sequence: {activeSequences.Count}\n" +
                   $"预设动画: {animationPresets.Count}";
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            StopAllAnimations();
            DOTween.KillAll();
        }
        
        #endregion
    }
    
    /// <summary>
    /// 动画类型枚举
    /// </summary>
    public enum AnimationType
    {
        FadeIn,
        FadeOut,
        Scale,
        Slide,
        Rotate,
        Shake,
        Pulse
    }
    
    /// <summary>
    /// 动画预设数据
    /// </summary>
    [System.Serializable]
    public class AnimationPreset
    {
        public float duration = 0.3f;
        public Ease ease = Ease.OutQuart;
        public AnimationType animationType = AnimationType.FadeIn;
        public Vector3 startScale = Vector3.zero;
        public Vector3 targetScale = Vector3.one;
        public Vector3 slideOffset = Vector3.zero;
        public float rotationAngle = 0f;
        public int loopCount = 1;
    }
}
