using UnityEngine;
using UnityEngine.UI;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 响应式UI元素 - 根据屏幕尺寸自动调整UI元素
    /// </summary>
    public class ResponsiveUIElement : MonoBehaviour
    {
        [Header("响应式设置")]
        [SerializeField] private ResponsiveMode responsiveMode = ResponsiveMode.Scale;
        [SerializeField] private bool respondToScreenSize = true;
        [SerializeField] private bool respondToSafeArea = true;
        
        [Header("缩放设置")]
        [SerializeField] private Vector2 minScale = new Vector2(0.8f, 0.8f);
        [SerializeField] private Vector2 maxScale = new Vector2(1.2f, 1.2f);
        [SerializeField] private AnimationCurve scaleCurve = AnimationCurve.Linear(0, 0, 1, 1);
        
        [Header("位置设置")]
        [SerializeField] private Vector2 smallScreenOffset = Vector2.zero;
        [SerializeField] private Vector2 largeScreenOffset = Vector2.zero;
        
        [Header("尺寸设置")]
        [SerializeField] private Vector2 minSize = new Vector2(100, 50);
        [SerializeField] private Vector2 maxSize = new Vector2(200, 100);
        
        [Header("设备类型设置")]
        [SerializeField] private DeviceSettings phoneSettings;
        [SerializeField] private DeviceSettings tabletSettings;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugInfo = false;
        
        // 组件引用
        private RectTransform rectTransform;
        private Text textComponent;
        private Image imageComponent;
        private Button buttonComponent;
        
        // 原始值缓存
        private Vector3 originalScale;
        private Vector2 originalPosition;
        private Vector2 originalSize;
        private float originalFontSize;
        
        // 当前状态
        private Core.DeviceType lastDeviceType;
        private Vector2 lastScreenSize;
        
        #region Unity生命周期
        
        private void Awake()
        {
            InitializeComponent();
            CacheOriginalValues();
        }
        
        private void Start()
        {
            ApplyResponsiveSettings();
        }
        
        private void Update()
        {
            CheckForChanges();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeComponent()
        {
            rectTransform = GetComponent<RectTransform>();
            textComponent = GetComponent<Text>();
            imageComponent = GetComponent<Image>();
            buttonComponent = GetComponent<Button>();
        }
        
        private void CacheOriginalValues()
        {
            if (rectTransform != null)
            {
                originalScale = rectTransform.localScale;
                originalPosition = rectTransform.anchoredPosition;
                originalSize = rectTransform.sizeDelta;
            }
            
            if (textComponent != null)
            {
                originalFontSize = textComponent.fontSize;
            }
        }
        
        #endregion
        
        #region 响应式逻辑
        
        private void ApplyResponsiveSettings()
        {
            if (Core.ResolutionManager.Instance == null) return;
            
            Core.DeviceType deviceType = Core.ResolutionManager.Instance.CurrentDeviceType;
            Vector2 screenSize = Core.ResolutionManager.Instance.CurrentResolution;
            float aspectRatio = Core.ResolutionManager.Instance.AspectRatio;
            
            // 根据响应模式应用设置
            switch (responsiveMode)
            {
                case ResponsiveMode.Scale:
                    ApplyScaleResponse(deviceType, screenSize, aspectRatio);
                    break;
                case ResponsiveMode.Position:
                    ApplyPositionResponse(deviceType, screenSize, aspectRatio);
                    break;
                case ResponsiveMode.Size:
                    ApplySizeResponse(deviceType, screenSize, aspectRatio);
                    break;
                case ResponsiveMode.All:
                    ApplyScaleResponse(deviceType, screenSize, aspectRatio);
                    ApplyPositionResponse(deviceType, screenSize, aspectRatio);
                    ApplySizeResponse(deviceType, screenSize, aspectRatio);
                    break;
                case ResponsiveMode.DeviceSpecific:
                    ApplyDeviceSpecificSettings(deviceType);
                    break;
            }
            
            // 应用文本响应
            ApplyTextResponse(deviceType, screenSize);
            
            // 更新缓存
            lastDeviceType = deviceType;
            lastScreenSize = screenSize;
            
            if (showDebugInfo)
            {
                Debug.Log($"ResponsiveUIElement: 应用响应式设置 - 设备: {deviceType}, 屏幕: {screenSize}");
            }
        }
        
        private void ApplyScaleResponse(Core.DeviceType deviceType, Vector2 screenSize, float aspectRatio)
        {
            if (rectTransform == null) return;
            
            // 计算缩放因子
            float scaleFactor = CalculateScaleFactor(deviceType, screenSize);
            
            // 应用缩放曲线
            float curveValue = scaleCurve.Evaluate(scaleFactor);
            
            // 计算最终缩放
            Vector2 targetScale = Vector2.Lerp(minScale, maxScale, curveValue);
            
            // 应用到Transform
            rectTransform.localScale = new Vector3(
                originalScale.x * targetScale.x,
                originalScale.y * targetScale.y,
                originalScale.z
            );
        }
        
        private void ApplyPositionResponse(Core.DeviceType deviceType, Vector2 screenSize, float aspectRatio)
        {
            if (rectTransform == null) return;
            
            // 根据屏幕尺寸计算位置偏移
            float sizeRatio = screenSize.magnitude / new Vector2(1920, 1080).magnitude;
            Vector2 offset = Vector2.Lerp(smallScreenOffset, largeScreenOffset, sizeRatio);
            
            rectTransform.anchoredPosition = originalPosition + offset;
        }
        
        private void ApplySizeResponse(Core.DeviceType deviceType, Vector2 screenSize, float aspectRatio)
        {
            if (rectTransform == null) return;
            
            // 计算尺寸因子
            float sizeFactor = CalculateScaleFactor(deviceType, screenSize);
            
            // 计算目标尺寸
            Vector2 targetSize = Vector2.Lerp(minSize, maxSize, sizeFactor);
            
            rectTransform.sizeDelta = targetSize;
        }
        
        private void ApplyDeviceSpecificSettings(Core.DeviceType deviceType)
        {
            DeviceSettings settings = null;
            
            switch (deviceType)
            {
                case Core.DeviceType.SmallPhone:
                case Core.DeviceType.Phone:
                case Core.DeviceType.LargePhone:
                    settings = phoneSettings;
                    break;
                case Core.DeviceType.Tablet:
                    settings = tabletSettings;
                    break;
            }
            
            if (settings != null && rectTransform != null)
            {
                rectTransform.localScale = originalScale * settings.scale;
                rectTransform.anchoredPosition = originalPosition + settings.positionOffset;
                rectTransform.sizeDelta = settings.useCustomSize ? settings.customSize : originalSize;
            }
        }
        
        private void ApplyTextResponse(Core.DeviceType deviceType, Vector2 screenSize)
        {
            if (textComponent == null) return;
            
            // 根据设备类型调整字体大小
            float fontSizeMultiplier = 1f;
            
            switch (deviceType)
            {
                case Core.DeviceType.SmallPhone:
                    fontSizeMultiplier = 0.8f;
                    break;
                case Core.DeviceType.Phone:
                    fontSizeMultiplier = 1f;
                    break;
                case Core.DeviceType.LargePhone:
                    fontSizeMultiplier = 1.1f;
                    break;
                case Core.DeviceType.Tablet:
                    fontSizeMultiplier = 1.3f;
                    break;
            }
            
            textComponent.fontSize = Mathf.RoundToInt(originalFontSize * fontSizeMultiplier);
        }
        
        private float CalculateScaleFactor(Core.DeviceType deviceType, Vector2 screenSize)
        {
            // 基于设备类型的基础因子
            float deviceFactor = 0.5f;
            
            switch (deviceType)
            {
                case Core.DeviceType.SmallPhone:
                    deviceFactor = 0.2f;
                    break;
                case Core.DeviceType.Phone:
                    deviceFactor = 0.5f;
                    break;
                case Core.DeviceType.LargePhone:
                    deviceFactor = 0.7f;
                    break;
                case Core.DeviceType.Tablet:
                    deviceFactor = 1f;
                    break;
            }
            
            // 基于屏幕尺寸的调整
            float sizeRatio = screenSize.magnitude / new Vector2(1920, 1080).magnitude;
            float sizeFactor = Mathf.Clamp01(sizeRatio);
            
            // 综合计算
            return Mathf.Clamp01((deviceFactor + sizeFactor) * 0.5f);
        }
        
        #endregion
        
        #region 变化检测
        
        private void CheckForChanges()
        {
            if (Core.ResolutionManager.Instance == null) return;
            
            Core.DeviceType currentDeviceType = Core.ResolutionManager.Instance.CurrentDeviceType;
            Vector2 currentScreenSize = Core.ResolutionManager.Instance.CurrentResolution;
            
            // 检查是否需要更新
            if (currentDeviceType != lastDeviceType || currentScreenSize != lastScreenSize)
            {
                ApplyResponsiveSettings();
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置响应模式
        /// </summary>
        public void SetResponsiveMode(ResponsiveMode mode)
        {
            responsiveMode = mode;
            ApplyResponsiveSettings();
        }
        
        /// <summary>
        /// 设置缩放范围
        /// </summary>
        public void SetScaleRange(Vector2 min, Vector2 max)
        {
            minScale = min;
            maxScale = max;
            ApplyResponsiveSettings();
        }
        
        /// <summary>
        /// 强制刷新响应式设置
        /// </summary>
        public void RefreshResponsiveSettings()
        {
            ApplyResponsiveSettings();
        }
        
        /// <summary>
        /// 重置到原始值
        /// </summary>
        public void ResetToOriginal()
        {
            if (rectTransform != null)
            {
                rectTransform.localScale = originalScale;
                rectTransform.anchoredPosition = originalPosition;
                rectTransform.sizeDelta = originalSize;
            }
            
            if (textComponent != null)
            {
                textComponent.fontSize = Mathf.RoundToInt(originalFontSize);
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// 响应模式枚举
    /// </summary>
    public enum ResponsiveMode
    {
        Scale,           // 只调整缩放
        Position,        // 只调整位置
        Size,            // 只调整尺寸
        All,             // 调整所有属性
        DeviceSpecific   // 使用设备特定设置
    }
    
    /// <summary>
    /// 设备设置
    /// </summary>
    [System.Serializable]
    public class DeviceSettings
    {
        public float scale = 1f;
        public Vector2 positionOffset = Vector2.zero;
        public bool useCustomSize = false;
        public Vector2 customSize = new Vector2(100, 50);
    }
}
