# 核心游戏逻辑设置指南

## 🎮 Unity场景设置

### 第1步：创建游戏场景
1. 创建新场景：`File > New Scene > 2D (URP)`
2. 保存为：`Assets/Scenes/GamePlay.unity`
3. 设置为构建场景中的第一个场景

### 第2步：设置主相机
```
Main Camera 设置:
- Position: (0, 0, -10)
- Projection: Orthographic
- Size: 8 (根据迷宫大小调整)
- Background: 纯黑色 #000000
- Culling Mask: Everything
```

### 第3步：创建管理器对象
在Hierarchy中创建以下空对象：

```
Managers (空对象)
├── GameManager (添加GameManager脚本)
├── GameLoopManager (添加GameLoopManager脚本)
├── InputManager (添加InputManagerSimple脚本)
├── ResolutionManager (添加ResolutionManager脚本)
├── PlayerController (添加PlayerController脚本)
├── PlayerData (添加PlayerData脚本)
├── MazeGenerator (添加MazeGenerator脚本)
├── MazeRenderer (添加MazeRenderer脚本)
├── CollisionSystem (添加CollisionSystem脚本)
├── ItemSystem (添加ItemSystem脚本)
└── GameStateManager (添加GameStateManager脚本)
```

### 第4步：设置UI Canvas
```
MainCanvas 设置:
- Render Mode: Screen Space - Overlay
- Canvas Scaler:
  - UI Scale Mode: Scale With Screen Size
  - Reference Resolution: 1920 x 1080
  - Screen Match Mode: Match Width Or Height
  - Match: 0.5
- Graphic Raycaster: 启用

子对象:
├── SafeAreaPanel (添加SafeAreaAdapter脚本)
├── GameplayUI (游戏界面)
├── PauseMenu (暂停菜单)
└── GameOverScreen (游戏结束界面)
```

## 🔧 脚本配置

### GameManager配置
```csharp
// 在Inspector中设置
Game Config: 创建GameConfig ScriptableObject
Debug Mode: true (开发时)
Auto Save: true
Save Interval: 30秒
```

### MazeGenerator配置
```csharp
// 迷宫设置
Maze Width: 25
Maze Height: 15
Cell Size: 1.0
Algorithm: RecursiveBacktracking
Difficulty: Medium
Room Generation Chance: 0.3
```

### PlayerController配置
```csharp
// 移动设置
Move Speed: 5.0
Acceleration: 20.0
Deceleration: 15.0
Use Physics Movement: true

// 边界设置
Constrain To Bounds: true
Movement Bounds: 自动从迷宫获取
```

### CollisionSystem配置
```csharp
// 碰撞层设置
Wall Layer: 8 (Walls)
Item Layer: 9 (Items)
Trap Layer: 10 (Traps)
Boundary Layer: 11 (Boundaries)

// 检测参数
Player Radius: 0.4
Wall Check Distance: 0.1
Item Pickup Distance: 0.5
```

### ItemSystem配置
```csharp
// 道具数量
Battery Count: 10
Health Pack Count: 5
Special Item Count: 3

// 生成设置
Min Distance From Player: 3.0
Min Distance Between Items: 2.0
Use Temporary Items: true (如果没有预制体)
```

## 🎯 测试步骤

### 基础功能测试
1. **运行场景**
   - 按Play按钮
   - 检查Console是否有错误
   - 确认所有管理器正确初始化

2. **迷宫生成测试**
   - 观察Scene视图中的迷宫
   - 检查起点和终点是否正确标记
   - 验证迷宫是否可解

3. **玩家移动测试**
   - 使用WASD键移动玩家
   - 检查碰撞检测是否正常
   - 验证边界限制

4. **道具系统测试**
   - 移动到道具位置
   - 检查道具是否能被拾取
   - 验证道具效果是否生效

5. **UI系统测试**
   - 检查HUD显示是否正常
   - 测试暂停/恢复功能
   - 验证游戏状态切换

### 手机测试准备
1. **构建设置**
   ```
   File > Build Settings
   Platform: Android
   Scenes: 添加GamePlay场景
   Player Settings:
   - Orientation: Landscape Left
   - Package Name: com.mazeadventure.game
   - Minimum API Level: 21
   ```

2. **输入测试**
   - 连接Android设备
   - 构建并运行
   - 测试触屏输入响应

## 🐛 常见问题解决

### 问题1：迷宫不显示
**可能原因**：
- MazeRenderer未正确设置
- 缺少渲染材质或预制体

**解决方案**：
```csharp
// 在MazeRenderer中启用临时渲染
Use Temporary Rendering: true
```

### 问题2：玩家无法移动
**可能原因**：
- InputManager未正确初始化
- PlayerController被禁用

**解决方案**：
```csharp
// 检查PlayerController设置
Can Move: true
Use Physics Movement: true
// 检查InputManager是否存在
```

### 问题3：碰撞检测不工作
**可能原因**：
- 碰撞层设置错误
- 缺少Collider2D组件

**解决方案**：
```csharp
// 设置正确的碰撞层
Walls: Layer 8
Items: Layer 9
Player: Default Layer
```

### 问题4：UI不显示
**可能原因**：
- Canvas设置错误
- UI元素被遮挡

**解决方案**：
```csharp
// 检查Canvas设置
Render Mode: Screen Space - Overlay
Sort Order: 0
// 检查UI元素的Active状态
```

### 问题5：性能问题
**可能原因**：
- 迷宫过大
- 渲染效率低

**解决方案**：
```csharp
// 调整迷宫大小
Maze Width: 15-25
Maze Height: 10-15
// 启用性能优化
Enable Batching: true
Enable Culling: true
```

## 📱 手机优化建议

### 性能优化
1. **降低迷宫复杂度**
   - 减少迷宫尺寸
   - 简化渲染效果

2. **优化UI**
   - 减少UI元素数量
   - 使用简单的UI动画

3. **内存管理**
   - 及时清理不用的对象
   - 使用对象池管理道具

### 触屏优化
1. **增大触摸区域**
   - 虚拟摇杆区域足够大
   - 按钮尺寸适合手指

2. **添加视觉反馈**
   - 触摸时的高亮效果
   - 震动反馈

3. **响应式设计**
   - 适配不同屏幕尺寸
   - 安全区域处理

## 🚀 下一步扩展

### 可以添加的功能
1. **音效系统**
   - 背景音乐
   - 音效反馈

2. **光照系统**
   - 手电筒效果
   - 动态阴影

3. **更多道具**
   - 钥匙和门
   - 特殊能力道具

4. **敌人系统**
   - 巡逻敌人
   - AI行为

5. **关卡系统**
   - 多个迷宫关卡
   - 难度递增

### 完成检查清单
- [ ] 所有管理器正确初始化
- [ ] 迷宫能正常生成和显示
- [ ] 玩家移动控制正常
- [ ] 碰撞检测工作正常
- [ ] 道具系统功能完整
- [ ] UI界面显示正确
- [ ] 游戏状态管理正常
- [ ] 手机构建成功
- [ ] 触屏输入响应正常
- [ ] 性能表现良好

完成这些设置后，你就有了一个完整可玩的迷宫探险游戏核心系统！
