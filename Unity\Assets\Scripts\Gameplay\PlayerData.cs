using UnityEngine;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 玩家数据管理 - 处理玩家的生命值、电量等属性
    /// </summary>
    public class PlayerData : MonoBehaviour
    {
        [Header("生命值设置")]
        [SerializeField] private float maxHealth = 100f;
        [SerializeField] private float currentHealth = 100f;
        [SerializeField] private float healthRegenRate = 0f; // 每秒恢复量
        
        [Header("电量设置")]
        [SerializeField] private float maxBattery = 100f;
        [SerializeField] private float currentBattery = 100f;
        [SerializeField] private float batteryDrainRate = 1f; // 每秒消耗量
        [SerializeField] private float lowBatteryThreshold = 20f;
        
        [Header("状态设置")]
        [SerializeField] private bool isInvulnerable = false;
        [SerializeField] private float invulnerabilityDuration = 1f;
        
        // 单例模式
        public static PlayerData Instance { get; private set; }
        
        // 属性访问器
        public float Health => currentHealth;
        public float MaxHealth => maxHealth;
        public float HealthPercentage => currentHealth / maxHealth;
        
        public float Battery => currentBattery;
        public float MaxBattery => maxBattery;
        public float BatteryPercentage => currentBattery / maxBattery;
        
        public bool IsAlive => currentHealth > 0f;
        public bool IsLowBattery => currentBattery <= lowBatteryThreshold;
        public bool IsInvulnerable => isInvulnerable;
        
        // 事件系统
        public System.Action<float, float> OnHealthChanged; // current, max
        public System.Action<float, float> OnBatteryChanged; // current, max
        public System.Action OnPlayerDied;
        public System.Action OnLowBattery;
        public System.Action OnBatteryEmpty;
        public System.Action<float> OnDamageTaken; // damage amount
        
        // 私有变量
        private float invulnerabilityTimer = 0f;
        private bool wasLowBattery = false;
        private Coroutine batteryDrainCoroutine;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializePlayerData();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            LoadPlayerSettings();
            StartBatteryDrain();
        }
        
        private void Update()
        {
            UpdateInvulnerability();
            UpdateHealthRegeneration();
            CheckBatteryStatus();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializePlayerData()
        {
            // 从游戏配置加载默认值
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                maxHealth = config.playerMaxHealth;
                maxBattery = config.playerMaxBattery;
                batteryDrainRate = config.batteryDrainRate;
            }
            
            // 设置初始值
            currentHealth = maxHealth;
            currentBattery = maxBattery;
        }
        
        private void LoadPlayerSettings()
        {
            // 从存档数据加载玩家状态（如果需要）
            GameData gameData = GameManager.Instance?.GetGameData();
            if (gameData != null)
            {
                // 这里可以加载存档的玩家状态
                // 目前使用默认值
            }
        }
        
        #endregion
        
        #region 生命值管理
        
        /// <summary>
        /// 造成伤害
        /// </summary>
        public void TakeDamage(float damage)
        {
            if (isInvulnerable || !IsAlive || damage <= 0f)
                return;
            
            float actualDamage = Mathf.Min(damage, currentHealth);
            currentHealth = Mathf.Max(0f, currentHealth - actualDamage);
            
            // 触发无敌时间
            StartInvulnerability();
            
            // 触发事件
            OnDamageTaken?.Invoke(actualDamage);
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            
            // 检查死亡
            if (currentHealth <= 0f)
            {
                HandlePlayerDeath();
            }
            
            Debug.Log($"玩家受到 {actualDamage} 点伤害，剩余生命值: {currentHealth}");
        }
        
        /// <summary>
        /// 恢复生命值
        /// </summary>
        public void RestoreHealth(float amount)
        {
            if (amount <= 0f) return;
            
            float oldHealth = currentHealth;
            currentHealth = Mathf.Min(maxHealth, currentHealth + amount);
            
            if (currentHealth != oldHealth)
            {
                OnHealthChanged?.Invoke(currentHealth, maxHealth);
                Debug.Log($"玩家恢复 {currentHealth - oldHealth} 点生命值");
            }
        }
        
        /// <summary>
        /// 设置最大生命值
        /// </summary>
        public void SetMaxHealth(float newMaxHealth)
        {
            if (newMaxHealth <= 0f) return;
            
            float healthRatio = currentHealth / maxHealth;
            maxHealth = newMaxHealth;
            currentHealth = maxHealth * healthRatio;
            
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
        }
        
        private void UpdateHealthRegeneration()
        {
            if (healthRegenRate > 0f && currentHealth < maxHealth && IsAlive)
            {
                RestoreHealth(healthRegenRate * Time.deltaTime);
            }
        }
        
        private void HandlePlayerDeath()
        {
            Debug.Log("玩家死亡");
            OnPlayerDied?.Invoke();
            
            // 禁用玩家移动
            if (PlayerController.Instance != null)
            {
                PlayerController.Instance.SetCanMove(false);
            }
            
            // 记录死亡统计
            GameData gameData = GameManager.Instance?.GetGameData();
            if (gameData != null)
            {
                gameData.RecordDeath();
            }
        }
        
        #endregion
        
        #region 电量管理
        
        /// <summary>
        /// 消耗电量
        /// </summary>
        public void ConsumeBattery(float amount)
        {
            if (amount <= 0f) return;
            
            float oldBattery = currentBattery;
            currentBattery = Mathf.Max(0f, currentBattery - amount);
            
            if (currentBattery != oldBattery)
            {
                OnBatteryChanged?.Invoke(currentBattery, maxBattery);
                
                if (currentBattery <= 0f)
                {
                    OnBatteryEmpty?.Invoke();
                }
            }
        }
        
        /// <summary>
        /// 恢复电量
        /// </summary>
        public void RestoreBattery(float amount)
        {
            if (amount <= 0f) return;
            
            float oldBattery = currentBattery;
            currentBattery = Mathf.Min(maxBattery, currentBattery + amount);
            
            if (currentBattery != oldBattery)
            {
                OnBatteryChanged?.Invoke(currentBattery, maxBattery);
                Debug.Log($"玩家恢复 {currentBattery - oldBattery} 点电量");
                
                // 记录电池收集统计
                GameData gameData = GameManager.Instance?.GetGameData();
                if (gameData != null)
                {
                    gameData.RecordBatteryCollected();
                }
            }
        }
        
        /// <summary>
        /// 设置最大电量
        /// </summary>
        public void SetMaxBattery(float newMaxBattery)
        {
            if (newMaxBattery <= 0f) return;
            
            float batteryRatio = currentBattery / maxBattery;
            maxBattery = newMaxBattery;
            currentBattery = maxBattery * batteryRatio;
            
            OnBatteryChanged?.Invoke(currentBattery, maxBattery);
        }
        
        private void StartBatteryDrain()
        {
            if (batteryDrainCoroutine != null)
            {
                StopCoroutine(batteryDrainCoroutine);
            }
            
            batteryDrainCoroutine = StartCoroutine(BatteryDrainCoroutine());
        }
        
        private System.Collections.IEnumerator BatteryDrainCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(1f);
                
                if (IsAlive && batteryDrainRate > 0f)
                {
                    ConsumeBattery(batteryDrainRate);
                }
            }
        }
        
        private void CheckBatteryStatus()
        {
            bool isCurrentlyLowBattery = IsLowBattery;
            
            if (isCurrentlyLowBattery && !wasLowBattery)
            {
                OnLowBattery?.Invoke();
                Debug.Log("电量不足警告！");
            }
            
            wasLowBattery = isCurrentlyLowBattery;
        }
        
        #endregion
        
        #region 无敌状态管理
        
        private void StartInvulnerability()
        {
            isInvulnerable = true;
            invulnerabilityTimer = invulnerabilityDuration;
        }
        
        private void UpdateInvulnerability()
        {
            if (isInvulnerable)
            {
                invulnerabilityTimer -= Time.deltaTime;
                
                if (invulnerabilityTimer <= 0f)
                {
                    isInvulnerable = false;
                    invulnerabilityTimer = 0f;
                }
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 重置玩家数据到初始状态
        /// </summary>
        public void ResetToDefault()
        {
            currentHealth = maxHealth;
            currentBattery = maxBattery;
            isInvulnerable = false;
            invulnerabilityTimer = 0f;
            wasLowBattery = false;
            
            OnHealthChanged?.Invoke(currentHealth, maxHealth);
            OnBatteryChanged?.Invoke(currentBattery, maxBattery);
            
            Debug.Log("玩家数据已重置");
        }
        
        /// <summary>
        /// 获取玩家状态摘要
        /// </summary>
        public string GetStatusSummary()
        {
            return $"生命值: {currentHealth:F0}/{maxHealth:F0} ({HealthPercentage:P0})\n" +
                   $"电量: {currentBattery:F0}/{maxBattery:F0} ({BatteryPercentage:P0})\n" +
                   $"状态: {(IsAlive ? "存活" : "死亡")}, {(IsInvulnerable ? "无敌" : "正常")}";
        }
        
        /// <summary>
        /// 检查是否可以使用需要电量的功能
        /// </summary>
        public bool CanUseBattery(float requiredAmount)
        {
            return currentBattery >= requiredAmount;
        }
        
        #endregion
        
        #region 清理
        
        private void OnDestroy()
        {
            if (batteryDrainCoroutine != null)
            {
                StopCoroutine(batteryDrainCoroutine);
            }
        }
        
        #endregion
    }
}
