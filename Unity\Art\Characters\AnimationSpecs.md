# 角色动画技术规格

## 📋 动画总览

### 必需动画状态
```
1. Idle (待机) - 2帧循环
2. Walk Right (右移) - 4帧循环  
3. Walk Left (左移) - 4帧循环 (或翻转)
4. Walk Up (上移) - 4帧循环
5. Walk Down (下移) - 4帧循环
6. Hurt (受伤) - 3帧单次播放
```

### 可选扩展动画
```
7. Death (死亡) - 4帧单次播放
8. Victory (胜利) - 3帧循环
9. Pickup (拾取) - 2帧单次播放
10. Use Item (使用道具) - 2帧单次播放
```

## 🎬 详细动画规格

### 1. Idle Animation (待机动画)
```
帧数: 2帧
总时长: 1.0秒
循环: 无限循环

帧1 (player_idle_01.png):
- 持续时间: 700ms
- 描述: 基础站立姿势
- 特点: 头灯正常亮度，双脚并拢

帧2 (player_idle_02.png):
- 持续时间: 300ms  
- 描述: 轻微呼吸效果
- 变化: 身体上移1像素，头灯稍亮

过渡: 平滑循环，无明显跳跃
```

### 2. Walk Right Animation (右移动画)
```
帧数: 4帧
总时长: 0.5秒
循环: 无限循环

帧1 (player_walk_right_01.png):
- 持续时间: 125ms
- 姿势: 左脚前，右脚后
- 手臂: 左臂后，右臂前
- 头灯: 指向右侧

帧2 (player_walk_right_02.png):
- 持续时间: 125ms
- 姿势: 双脚接近并拢
- 手臂: 回到中性位置
- 作用: 过渡帧

帧3 (player_walk_right_03.png):
- 持续时间: 125ms
- 姿势: 右脚前，左脚后
- 手臂: 右臂后，左臂前
- 重心: 转移到右脚

帧4 (player_walk_right_04.png):
- 持续时间: 125ms
- 姿势: 双脚接近并拢
- 手臂: 回到中性位置
- 作用: 过渡帧

节奏: 均匀，体现走路的韵律感
```

### 3. Walk Left Animation (左移动画)
```
选项A: 翻转右移动画
- 优点: 节省制作时间
- 缺点: 头灯方向可能不自然

选项B: 独立制作
- 帧数: 4帧
- 规格: 与右移动画相同，方向相反
- 推荐: 如果时间充足

建议: 先使用翻转，后期优化时独立制作
```

### 4. Walk Up Animation (上移动画)
```
帧数: 4帧
总时长: 0.5秒
视角: 背面视图

帧1: 左脚抬起，重心右倾
帧2: 双脚并拢，身体直立
帧3: 右脚抬起，重心左倾  
帧4: 双脚并拢，身体直立

特点: 头灯不可见或仅显示边缘光
```

### 5. Walk Down Animation (下移动画)
```
帧数: 4帧
总时长: 0.5秒
视角: 正面视图

帧1: 左脚前，身体略前倾
帧2: 双脚并拢，身体直立
帧3: 右脚前，身体略前倾
帧4: 双脚并拢，身体直立

特点: 头灯清晰可见，照向前方
```

### 6. Hurt Animation (受伤动画)
```
帧数: 3帧
总时长: 0.3秒
播放: 单次，不循环

帧1 (player_hurt_01.png):
- 持续时间: 100ms
- 描述: 正常颜色，准备后退
- 姿势: 轻微防御姿态

帧2 (player_hurt_02.png):
- 持续时间: 100ms
- 描述: 红色叠加效果
- 姿势: 身体后倾2像素
- 特效: 50%红色叠加

帧3 (player_hurt_03.png):
- 持续时间: 100ms
- 描述: 恢复正常
- 姿势: 回到标准站立
- 过渡: 回到Idle状态

触发: 受到伤害时播放
```

## 🎯 像素级动画要点

### 移动原则
```
垂直移动: 1-2像素范围
水平移动: 2-3像素范围
旋转: 避免，使用重绘
缩放: 避免，保持32x32
```

### 身体部位动画
```
头部:
- 基本不动，仅轻微摆动
- 头灯方向跟随移动方向
- 受伤时可以后仰

躯干:
- 上下轻微移动 (1像素)
- 左右摆动配合步伐
- 保持整体稳定

手臂:
- 与腿部相反摆动
- 摆动幅度: 2-3像素
- 保持自然节奏

腿部:
- 主要动画部位
- 前后摆动: 3-4像素
- 抬腿高度: 1-2像素
```

### 头灯动画
```
方向变化:
- 右移: 头灯向右
- 左移: 头灯向左  
- 上移: 头灯不可见
- 下移: 头灯向前

亮度变化:
- 待机: 正常亮度
- 移动: 稍微摇摆
- 受伤: 闪烁效果
- 低电量: 明暗闪烁
```

## 📐 关键帧位置参考

### 标准站立姿势 (Idle基准)
```
头部中心: (16, 8)
身体中心: (16, 18)
左脚位置: (13, 30)
右脚位置: (19, 30)
左手位置: (11, 18)
右手位置: (21, 18)
```

### 移动姿势变化范围
```
脚部前后移动: ±3像素
手臂前后摆动: ±2像素
身体上下浮动: ±1像素
头部轻微摆动: ±1像素
```

## 🎨 色彩动画效果

### 受伤效果
```
方法1: 色彩叠加
- 在原色基础上叠加50%红色
- 影响所有非轮廓像素
- 持续100ms

方法2: 色彩替换
- 将主色替换为红色调
- 保持明暗关系
- 更明显的视觉效果
```

### 头灯闪烁效果
```
正常状态: #fff8dc
闪烁状态: #ffeb3b
低电量闪烁:
- 亮: #fff8dc (300ms)
- 暗: #ffa726 (200ms)
- 循环播放
```

## 📊 性能考虑

### 内存优化
```
纹理尺寸: 32x32 (最小)
色彩深度: 8位索引色
压缩格式: 无压缩 (保持像素完美)
图集打包: 将所有帧打包到一个图集
```

### 动画优化
```
帧率: 8 FPS (足够流畅)
循环优化: 确保首尾帧连接自然
状态缓存: 预加载所有动画状态
过渡平滑: 避免突兀的状态切换
```

## 🔄 状态机设计

### 动画状态转换
```
Idle → Walk: 立即切换
Walk → Idle: 完成当前步伐后切换
Any → Hurt: 立即打断，播放受伤
Hurt → Idle: 受伤动画结束后回到待机
Walk Direction Change: 立即切换方向
```

### 触发条件
```
移动输入 > 0.1: 切换到Walk
移动输入 = 0: 切换到Idle
受到伤害: 触发Hurt
方向改变: 切换Walk方向
```

## ✅ 制作检查清单

### 每个动画完成后检查
- [ ] 帧数正确
- [ ] 时序合适
- [ ] 循环流畅
- [ ] 像素对齐
- [ ] 色彩正确
- [ ] 文件命名规范
- [ ] 透明背景
- [ ] 32x32尺寸

### 整套动画完成后检查
- [ ] 所有状态齐全
- [ ] 风格统一
- [ ] 过渡自然
- [ ] 性能合理
- [ ] 导出格式正确

完成这些动画后，你就有了一个完整的角色动画系统！
