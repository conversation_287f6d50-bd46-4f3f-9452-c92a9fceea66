using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using MazeAdventure.Core;

namespace MazeAdventure.Gameplay
{
    /// <summary>
    /// 迷宫生成器 - 程序化生成迷宫布局
    /// </summary>
    public class MazeGenerator : MonoBehaviour
    {
        [Header("迷宫尺寸")]
        [SerializeField] private int mazeWidth = 25;
        [SerializeField] private int mazeHeight = 15;
        [SerializeField] private float cellSize = 1f;
        
        [Header("生成设置")]
        [SerializeField] private MazeAlgorithm algorithm = MazeAlgorithm.RecursiveBacktracking;
        [SerializeField] private int seed = 0; // 0 = 随机种子
        [SerializeField] private bool useRandomSeed = true;
        
        [Header("难度设置")]
        [SerializeField] private MazeDifficulty difficulty = MazeDifficulty.Medium;
        [SerializeField] private float roomGenerationChance = 0.3f;
        [SerializeField] private int minRoomSize = 3;
        [SerializeField] private int maxRoomSize = 7;
        
        [Header("特殊区域")]
        [SerializeField] private bool generateStartArea = true;
        [SerializeField] private bool generateEndArea = true;
        [SerializeField] private int startAreaSize = 3;
        [SerializeField] private int endAreaSize = 3;
        
        [Header("调试选项")]
        [SerializeField] private bool showDebugInfo = false;
        [SerializeField] private bool visualizeGeneration = false;
        
        // 单例模式
        public static MazeGenerator Instance { get; private set; }
        
        // 迷宫数据
        public MazeCell[,] MazeGrid { get; private set; }
        public Vector2Int StartPosition { get; private set; }
        public Vector2Int EndPosition { get; private set; }
        public List<Vector2Int> RoomCenters { get; private set; }
        
        // 生成状态
        public bool IsGenerating { get; private set; }
        public float GenerationProgress { get; private set; }
        
        // 事件系统
        public System.Action<MazeCell[,]> OnMazeGenerated;
        public System.Action<float> OnGenerationProgress;
        public System.Action OnGenerationStarted;
        public System.Action OnGenerationCompleted;
        
        // 私有变量
        private System.Random random;
        private List<Vector2Int> generationStack;
        private HashSet<Vector2Int> visitedCells;
        private List<Room> rooms;
        
        #region Unity生命周期
        
        private void Awake()
        {
            if (Instance == null)
            {
                Instance = this;
                InitializeMazeGenerator();
            }
            else
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            LoadMazeSettings();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeMazeGenerator()
        {
            generationStack = new List<Vector2Int>();
            visitedCells = new HashSet<Vector2Int>();
            rooms = new List<Room>();
            RoomCenters = new List<Vector2Int>();
        }
        
        private void LoadMazeSettings()
        {
            GameConfig config = GameManager.Instance?.GetGameConfig();
            if (config != null)
            {
                mazeWidth = config.mazeWidth;
                mazeHeight = config.mazeHeight;
                cellSize = config.cellSize;
                roomGenerationChance = config.roomGenerationChance;
            }
        }
        
        #endregion
        
        #region 迷宫生成
        
        /// <summary>
        /// 生成新迷宫
        /// </summary>
        public void GenerateMaze()
        {
            if (IsGenerating) return;
            
            StartCoroutine(GenerateMazeCoroutine());
        }
        
        /// <summary>
        /// 使用指定参数生成迷宫
        /// </summary>
        public void GenerateMaze(int width, int height, MazeDifficulty mazeDifficulty, int mazeSeed = 0)
        {
            if (IsGenerating) return;
            
            mazeWidth = width;
            mazeHeight = height;
            difficulty = mazeDifficulty;
            seed = mazeSeed;
            useRandomSeed = mazeSeed == 0;
            
            GenerateMaze();
        }
        
        private System.Collections.IEnumerator GenerateMazeCoroutine()
        {
            IsGenerating = true;
            GenerationProgress = 0f;
            OnGenerationStarted?.Invoke();
            
            // 初始化随机数生成器
            InitializeRandom();
            
            // 初始化迷宫网格
            InitializeMazeGrid();
            yield return null;
            
            // 生成房间
            if (roomGenerationChance > 0f)
            {
                GenerateRooms();
                yield return null;
            }
            
            // 生成迷宫路径
            yield return StartCoroutine(GenerateMazePaths());
            
            // 连接房间
            if (rooms.Count > 0)
            {
                ConnectRooms();
                yield return null;
            }
            
            // 设置起点和终点
            SetStartAndEndPositions();
            yield return null;
            
            // 确保迷宫可解
            EnsureMazeSolvable();
            yield return null;
            
            // 应用难度调整
            ApplyDifficultyModifications();
            yield return null;
            
            // 完成生成
            GenerationProgress = 1f;
            IsGenerating = false;
            
            OnGenerationCompleted?.Invoke();
            OnMazeGenerated?.Invoke(MazeGrid);
            
            if (showDebugInfo)
            {
                Debug.Log($"迷宫生成完成: {mazeWidth}x{mazeHeight}, 种子: {seed}, 难度: {difficulty}");
            }
        }
        
        private void InitializeRandom()
        {
            if (useRandomSeed)
            {
                seed = Random.Range(1, int.MaxValue);
            }
            
            random = new System.Random(seed);
            Random.InitState(seed);
        }
        
        private void InitializeMazeGrid()
        {
            MazeGrid = new MazeCell[mazeWidth, mazeHeight];
            
            for (int x = 0; x < mazeWidth; x++)
            {
                for (int y = 0; y < mazeHeight; y++)
                {
                    MazeGrid[x, y] = new MazeCell
                    {
                        position = new Vector2Int(x, y),
                        cellType = CellType.Wall,
                        isVisited = false,
                        walls = WallFlags.All
                    };
                }
            }
            
            visitedCells.Clear();
            generationStack.Clear();
        }
        
        #endregion
        
        #region 路径生成算法
        
        private System.Collections.IEnumerator GenerateMazePaths()
        {
            switch (algorithm)
            {
                case MazeAlgorithm.RecursiveBacktracking:
                    yield return StartCoroutine(GenerateRecursiveBacktracking());
                    break;
                case MazeAlgorithm.Prims:
                    yield return StartCoroutine(GeneratePrims());
                    break;
                case MazeAlgorithm.Kruskals:
                    yield return StartCoroutine(GenerateKruskals());
                    break;
                default:
                    yield return StartCoroutine(GenerateRecursiveBacktracking());
                    break;
            }
        }
        
        private System.Collections.IEnumerator GenerateRecursiveBacktracking()
        {
            // 从中心开始
            Vector2Int current = new Vector2Int(mazeWidth / 2, mazeHeight / 2);
            MazeGrid[current.x, current.y].cellType = CellType.Path;
            MazeGrid[current.x, current.y].isVisited = true;
            visitedCells.Add(current);
            generationStack.Add(current);
            
            int totalCells = (mazeWidth / 2) * (mazeHeight / 2);
            int processedCells = 0;
            
            while (generationStack.Count > 0)
            {
                current = generationStack[generationStack.Count - 1];
                
                List<Vector2Int> neighbors = GetUnvisitedNeighbors(current);
                
                if (neighbors.Count > 0)
                {
                    Vector2Int next = neighbors[random.Next(neighbors.Count)];
                    
                    // 移除两个单元格之间的墙
                    RemoveWallBetween(current, next);
                    
                    // 标记下一个单元格为已访问
                    MazeGrid[next.x, next.y].cellType = CellType.Path;
                    MazeGrid[next.x, next.y].isVisited = true;
                    visitedCells.Add(next);
                    generationStack.Add(next);
                }
                else
                {
                    generationStack.RemoveAt(generationStack.Count - 1);
                }
                
                processedCells++;
                GenerationProgress = (float)processedCells / totalCells * 0.7f; // 70%进度用于路径生成
                OnGenerationProgress?.Invoke(GenerationProgress);
                
                if (visualizeGeneration && processedCells % 10 == 0)
                {
                    yield return new WaitForSeconds(0.01f);
                }
            }
        }
        
        private System.Collections.IEnumerator GeneratePrims()
        {
            // Prim算法实现
            List<Vector2Int> frontier = new List<Vector2Int>();
            
            // 随机选择起始点
            Vector2Int start = new Vector2Int(
                random.Next(1, mazeWidth - 1),
                random.Next(1, mazeHeight - 1)
            );
            
            MazeGrid[start.x, start.y].cellType = CellType.Path;
            MazeGrid[start.x, start.y].isVisited = true;
            visitedCells.Add(start);
            
            AddFrontierCells(start, frontier);
            
            while (frontier.Count > 0)
            {
                Vector2Int current = frontier[random.Next(frontier.Count)];
                frontier.Remove(current);
                
                List<Vector2Int> visitedNeighbors = GetVisitedNeighbors(current);
                if (visitedNeighbors.Count > 0)
                {
                    Vector2Int neighbor = visitedNeighbors[random.Next(visitedNeighbors.Count)];
                    
                    RemoveWallBetween(current, neighbor);
                    MazeGrid[current.x, current.y].cellType = CellType.Path;
                    MazeGrid[current.x, current.y].isVisited = true;
                    visitedCells.Add(current);
                    
                    AddFrontierCells(current, frontier);
                }
                
                GenerationProgress = (float)visitedCells.Count / ((mazeWidth / 2) * (mazeHeight / 2)) * 0.7f;
                OnGenerationProgress?.Invoke(GenerationProgress);
                
                if (visualizeGeneration)
                {
                    yield return new WaitForSeconds(0.01f);
                }
            }
        }
        
        private System.Collections.IEnumerator GenerateKruskals()
        {
            // Kruskal算法实现 (简化版)
            List<Edge> edges = new List<Edge>();
            Dictionary<Vector2Int, int> cellSets = new Dictionary<Vector2Int, int>();
            
            // 初始化所有单元格为独立集合
            int setId = 0;
            for (int x = 1; x < mazeWidth - 1; x += 2)
            {
                for (int y = 1; y < mazeHeight - 1; y += 2)
                {
                    Vector2Int cell = new Vector2Int(x, y);
                    MazeGrid[x, y].cellType = CellType.Path;
                    cellSets[cell] = setId++;
                    
                    // 添加边
                    if (x + 2 < mazeWidth - 1)
                        edges.Add(new Edge(cell, new Vector2Int(x + 2, y)));
                    if (y + 2 < mazeHeight - 1)
                        edges.Add(new Edge(cell, new Vector2Int(x, y + 2)));
                }
            }
            
            // 随机打乱边的顺序
            for (int i = 0; i < edges.Count; i++)
            {
                Edge temp = edges[i];
                int randomIndex = random.Next(i, edges.Count);
                edges[i] = edges[randomIndex];
                edges[randomIndex] = temp;
            }
            
            // 处理边
            foreach (Edge edge in edges)
            {
                if (cellSets[edge.from] != cellSets[edge.to])
                {
                    // 连接两个不同的集合
                    RemoveWallBetween(edge.from, edge.to);
                    
                    // 合并集合
                    int oldSet = cellSets[edge.to];
                    int newSet = cellSets[edge.from];
                    
                    foreach (var kvp in cellSets.ToList())
                    {
                        if (kvp.Value == oldSet)
                        {
                            cellSets[kvp.Key] = newSet;
                        }
                    }
                }
                
                if (visualizeGeneration)
                {
                    yield return new WaitForSeconds(0.01f);
                }
            }
            
            GenerationProgress = 0.7f;
            OnGenerationProgress?.Invoke(GenerationProgress);
        }
        
        #endregion
        
        #region 辅助方法
        
        private List<Vector2Int> GetUnvisitedNeighbors(Vector2Int cell)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            Vector2Int[] directions = { Vector2Int.up * 2, Vector2Int.right * 2, Vector2Int.down * 2, Vector2Int.left * 2 };
            
            foreach (Vector2Int dir in directions)
            {
                Vector2Int neighbor = cell + dir;
                if (IsValidCell(neighbor) && !MazeGrid[neighbor.x, neighbor.y].isVisited)
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }
        
        private List<Vector2Int> GetVisitedNeighbors(Vector2Int cell)
        {
            List<Vector2Int> neighbors = new List<Vector2Int>();
            Vector2Int[] directions = { Vector2Int.up * 2, Vector2Int.right * 2, Vector2Int.down * 2, Vector2Int.left * 2 };
            
            foreach (Vector2Int dir in directions)
            {
                Vector2Int neighbor = cell + dir;
                if (IsValidCell(neighbor) && MazeGrid[neighbor.x, neighbor.y].isVisited)
                {
                    neighbors.Add(neighbor);
                }
            }
            
            return neighbors;
        }
        
        private void AddFrontierCells(Vector2Int cell, List<Vector2Int> frontier)
        {
            Vector2Int[] directions = { Vector2Int.up * 2, Vector2Int.right * 2, Vector2Int.down * 2, Vector2Int.left * 2 };
            
            foreach (Vector2Int dir in directions)
            {
                Vector2Int neighbor = cell + dir;
                if (IsValidCell(neighbor) && !MazeGrid[neighbor.x, neighbor.y].isVisited && !frontier.Contains(neighbor))
                {
                    frontier.Add(neighbor);
                }
            }
        }
        
        private void RemoveWallBetween(Vector2Int cell1, Vector2Int cell2)
        {
            Vector2Int wall = (cell1 + cell2) / 2;
            if (IsValidCell(wall))
            {
                MazeGrid[wall.x, wall.y].cellType = CellType.Path;
                MazeGrid[wall.x, wall.y].isVisited = true;
            }
        }
        
        private bool IsValidCell(Vector2Int cell)
        {
            return cell.x >= 0 && cell.x < mazeWidth && cell.y >= 0 && cell.y < mazeHeight;
        }
        
        #endregion
        
        #region 房间生成
        
        private void GenerateRooms()
        {
            rooms.Clear();
            RoomCenters.Clear();
            
            int roomCount = Mathf.RoundToInt((mazeWidth * mazeHeight) * roomGenerationChance / 100f);
            int attempts = 0;
            int maxAttempts = roomCount * 10;
            
            while (rooms.Count < roomCount && attempts < maxAttempts)
            {
                attempts++;
                
                int roomWidth = random.Next(minRoomSize, maxRoomSize + 1);
                int roomHeight = random.Next(minRoomSize, maxRoomSize + 1);
                
                int x = random.Next(1, mazeWidth - roomWidth - 1);
                int y = random.Next(1, mazeHeight - roomHeight - 1);
                
                Room newRoom = new Room(x, y, roomWidth, roomHeight);
                
                if (CanPlaceRoom(newRoom))
                {
                    PlaceRoom(newRoom);
                    rooms.Add(newRoom);
                    RoomCenters.Add(newRoom.Center);
                }
            }
            
            if (showDebugInfo)
            {
                Debug.Log($"生成了 {rooms.Count} 个房间");
            }
        }
        
        private bool CanPlaceRoom(Room room)
        {
            // 检查是否与现有房间重叠
            foreach (Room existingRoom in rooms)
            {
                if (room.Overlaps(existingRoom, 2)) // 2格间距
                {
                    return false;
                }
            }
            
            return true;
        }
        
        private void PlaceRoom(Room room)
        {
            for (int x = room.x; x < room.x + room.width; x++)
            {
                for (int y = room.y; y < room.y + room.height; y++)
                {
                    if (IsValidCell(new Vector2Int(x, y)))
                    {
                        MazeGrid[x, y].cellType = CellType.Room;
                        MazeGrid[x, y].isVisited = true;
                    }
                }
            }
        }
        
        private void ConnectRooms()
        {
            // 简单的房间连接：连接每个房间到最近的路径
            foreach (Room room in rooms)
            {
                ConnectRoomToMaze(room);
            }
        }
        
        private void ConnectRoomToMaze(Room room)
        {
            Vector2Int roomCenter = room.Center;
            Vector2Int closestPath = FindClosestPath(roomCenter);
            
            if (closestPath != Vector2Int.zero)
            {
                CreatePath(roomCenter, closestPath);
            }
        }
        
        private Vector2Int FindClosestPath(Vector2Int from)
        {
            float minDistance = float.MaxValue;
            Vector2Int closestPath = Vector2Int.zero;
            
            for (int x = 0; x < mazeWidth; x++)
            {
                for (int y = 0; y < mazeHeight; y++)
                {
                    if (MazeGrid[x, y].cellType == CellType.Path)
                    {
                        float distance = Vector2Int.Distance(from, new Vector2Int(x, y));
                        if (distance < minDistance)
                        {
                            minDistance = distance;
                            closestPath = new Vector2Int(x, y);
                        }
                    }
                }
            }
            
            return closestPath;
        }
        
        private void CreatePath(Vector2Int from, Vector2Int to)
        {
            Vector2Int current = from;
            
            // 先水平移动
            while (current.x != to.x)
            {
                current.x += current.x < to.x ? 1 : -1;
                if (IsValidCell(current) && MazeGrid[current.x, current.y].cellType == CellType.Wall)
                {
                    MazeGrid[current.x, current.y].cellType = CellType.Path;
                }
            }
            
            // 再垂直移动
            while (current.y != to.y)
            {
                current.y += current.y < to.y ? 1 : -1;
                if (IsValidCell(current) && MazeGrid[current.x, current.y].cellType == CellType.Wall)
                {
                    MazeGrid[current.x, current.y].cellType = CellType.Path;
                }
            }
        }
        
        #endregion
        
        #region 起点终点设置
        
        private void SetStartAndEndPositions()
        {
            // 寻找合适的起点（左上角区域）
            StartPosition = FindPositionInArea(0, 0, mazeWidth / 3, mazeHeight / 3, CellType.Path);
            
            // 寻找合适的终点（右下角区域）
            EndPosition = FindPositionInArea(mazeWidth * 2 / 3, mazeHeight * 2 / 3, mazeWidth, mazeHeight, CellType.Path);
            
            // 如果找不到合适位置，使用默认位置
            if (StartPosition == Vector2Int.zero)
            {
                StartPosition = new Vector2Int(1, 1);
                MazeGrid[1, 1].cellType = CellType.Path;
            }
            
            if (EndPosition == Vector2Int.zero)
            {
                EndPosition = new Vector2Int(mazeWidth - 2, mazeHeight - 2);
                MazeGrid[mazeWidth - 2, mazeHeight - 2].cellType = CellType.Path;
            }
            
            // 标记起点和终点
            MazeGrid[StartPosition.x, StartPosition.y].cellType = CellType.Start;
            MazeGrid[EndPosition.x, EndPosition.y].cellType = CellType.End;
        }
        
        private Vector2Int FindPositionInArea(int minX, int minY, int maxX, int maxY, CellType targetType)
        {
            List<Vector2Int> validPositions = new List<Vector2Int>();
            
            for (int x = minX; x < maxX; x++)
            {
                for (int y = minY; y < maxY; y++)
                {
                    if (IsValidCell(new Vector2Int(x, y)) && MazeGrid[x, y].cellType == targetType)
                    {
                        validPositions.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return validPositions.Count > 0 ? validPositions[random.Next(validPositions.Count)] : Vector2Int.zero;
        }
        
        #endregion
        
        #region 难度调整和验证
        
        private void EnsureMazeSolvable()
        {
            // 使用简单的路径查找确保迷宫可解
            if (!IsPathExists(StartPosition, EndPosition))
            {
                // 如果无路径，创建一条直接路径
                CreatePath(StartPosition, EndPosition);
                
                if (showDebugInfo)
                {
                    Debug.Log("迷宫不可解，已创建直接路径");
                }
            }
        }
        
        private bool IsPathExists(Vector2Int start, Vector2Int end)
        {
            // 简单的BFS路径查找
            Queue<Vector2Int> queue = new Queue<Vector2Int>();
            HashSet<Vector2Int> visited = new HashSet<Vector2Int>();
            
            queue.Enqueue(start);
            visited.Add(start);
            
            Vector2Int[] directions = { Vector2Int.up, Vector2Int.right, Vector2Int.down, Vector2Int.left };
            
            while (queue.Count > 0)
            {
                Vector2Int current = queue.Dequeue();
                
                if (current == end)
                {
                    return true;
                }
                
                foreach (Vector2Int dir in directions)
                {
                    Vector2Int next = current + dir;
                    
                    if (IsValidCell(next) && !visited.Contains(next) && 
                        (MazeGrid[next.x, next.y].cellType == CellType.Path || 
                         MazeGrid[next.x, next.y].cellType == CellType.Room ||
                         MazeGrid[next.x, next.y].cellType == CellType.End))
                    {
                        queue.Enqueue(next);
                        visited.Add(next);
                    }
                }
            }
            
            return false;
        }
        
        private void ApplyDifficultyModifications()
        {
            switch (difficulty)
            {
                case MazeDifficulty.Easy:
                    // 增加更多路径，减少死胡同
                    AddExtraPaths(0.1f);
                    break;
                    
                case MazeDifficulty.Medium:
                    // 保持默认生成结果
                    break;
                    
                case MazeDifficulty.Hard:
                    // 增加死胡同，减少路径
                    RemoveRandomPaths(0.05f);
                    break;
                    
                case MazeDifficulty.Expert:
                    // 创建更复杂的路径结构
                    RemoveRandomPaths(0.1f);
                    AddFalseDeadEnds();
                    break;
            }
        }
        
        private void AddExtraPaths(float percentage)
        {
            int pathsToAdd = Mathf.RoundToInt((mazeWidth * mazeHeight) * percentage);
            
            for (int i = 0; i < pathsToAdd; i++)
            {
                Vector2Int randomWall = FindRandomWall();
                if (randomWall != Vector2Int.zero)
                {
                    MazeGrid[randomWall.x, randomWall.y].cellType = CellType.Path;
                }
            }
        }
        
        private void RemoveRandomPaths(float percentage)
        {
            int pathsToRemove = Mathf.RoundToInt((mazeWidth * mazeHeight) * percentage);
            
            for (int i = 0; i < pathsToRemove; i++)
            {
                Vector2Int randomPath = FindRandomPath();
                if (randomPath != Vector2Int.zero && randomPath != StartPosition && randomPath != EndPosition)
                {
                    // 确保移除后仍然可解
                    MazeGrid[randomPath.x, randomPath.y].cellType = CellType.Wall;
                    if (!IsPathExists(StartPosition, EndPosition))
                    {
                        // 如果不可解，恢复路径
                        MazeGrid[randomPath.x, randomPath.y].cellType = CellType.Path;
                    }
                }
            }
        }
        
        private void AddFalseDeadEnds()
        {
            // 添加一些看似死胡同但实际有隐藏路径的区域
            // 这是高级功能，可以后续实现
        }
        
        private Vector2Int FindRandomWall()
        {
            List<Vector2Int> walls = new List<Vector2Int>();
            
            for (int x = 1; x < mazeWidth - 1; x++)
            {
                for (int y = 1; y < mazeHeight - 1; y++)
                {
                    if (MazeGrid[x, y].cellType == CellType.Wall)
                    {
                        walls.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return walls.Count > 0 ? walls[random.Next(walls.Count)] : Vector2Int.zero;
        }
        
        private Vector2Int FindRandomPath()
        {
            List<Vector2Int> paths = new List<Vector2Int>();
            
            for (int x = 1; x < mazeWidth - 1; x++)
            {
                for (int y = 1; y < mazeHeight - 1; y++)
                {
                    if (MazeGrid[x, y].cellType == CellType.Path)
                    {
                        paths.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return paths.Count > 0 ? paths[random.Next(paths.Count)] : Vector2Int.zero;
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 获取指定位置的单元格类型
        /// </summary>
        public CellType GetCellType(Vector2Int position)
        {
            if (IsValidCell(position))
            {
                return MazeGrid[position.x, position.y].cellType;
            }
            return CellType.Wall;
        }
        
        /// <summary>
        /// 获取指定位置的单元格类型（世界坐标）
        /// </summary>
        public CellType GetCellType(Vector2 worldPosition)
        {
            Vector2Int gridPos = WorldToGrid(worldPosition);
            return GetCellType(gridPos);
        }
        
        /// <summary>
        /// 世界坐标转网格坐标
        /// </summary>
        public Vector2Int WorldToGrid(Vector2 worldPosition)
        {
            return new Vector2Int(
                Mathf.RoundToInt(worldPosition.x / cellSize),
                Mathf.RoundToInt(worldPosition.y / cellSize)
            );
        }
        
        /// <summary>
        /// 网格坐标转世界坐标
        /// </summary>
        public Vector2 GridToWorld(Vector2Int gridPosition)
        {
            return new Vector2(gridPosition.x * cellSize, gridPosition.y * cellSize);
        }
        
        /// <summary>
        /// 检查位置是否可通行
        /// </summary>
        public bool IsWalkable(Vector2Int position)
        {
            CellType cellType = GetCellType(position);
            return cellType == CellType.Path || cellType == CellType.Room || 
                   cellType == CellType.Start || cellType == CellType.End;
        }
        
        /// <summary>
        /// 获取迷宫边界
        /// </summary>
        public Bounds GetMazeBounds()
        {
            Vector2 center = new Vector2((mazeWidth - 1) * cellSize * 0.5f, (mazeHeight - 1) * cellSize * 0.5f);
            Vector2 size = new Vector2(mazeWidth * cellSize, mazeHeight * cellSize);
            return new Bounds(center, size);
        }
        
        #endregion

        #region 调试绘制

        private void OnDrawGizmosSelected()
        {
            if (MazeGrid == null || !showDebugInfo) return;

            for (int x = 0; x < mazeWidth; x++)
            {
                for (int y = 0; y < mazeHeight; y++)
                {
                    Vector3 worldPos = new Vector3(x * cellSize, y * cellSize, 0);

                    switch (MazeGrid[x, y].cellType)
                    {
                        case CellType.Wall:
                            Gizmos.color = Color.black;
                            break;
                        case CellType.Path:
                            Gizmos.color = Color.white;
                            break;
                        case CellType.Room:
                            Gizmos.color = Color.blue;
                            break;
                        case CellType.Start:
                            Gizmos.color = Color.green;
                            break;
                        case CellType.End:
                            Gizmos.color = Color.red;
                            break;
                    }

                    Gizmos.DrawCube(worldPos, Vector3.one * cellSize * 0.9f);
                }
            }
        }

        #endregion
    }

    #region 数据结构
    
    /// <summary>
    /// 迷宫单元格
    /// </summary>
    [System.Serializable]
    public class MazeCell
    {
        public Vector2Int position;
        public CellType cellType;
        public bool isVisited;
        public WallFlags walls;
    }
    
    /// <summary>
    /// 单元格类型
    /// </summary>
    public enum CellType
    {
        Wall,    // 墙壁
        Path,    // 路径
        Room,    // 房间
        Start,   // 起点
        End      // 终点
    }
    
    /// <summary>
    /// 墙壁标志
    /// </summary>
    [System.Flags]
    public enum WallFlags
    {
        None = 0,
        North = 1,
        East = 2,
        South = 4,
        West = 8,
        All = North | East | South | West
    }
    
    /// <summary>
    /// 迷宫生成算法
    /// </summary>
    public enum MazeAlgorithm
    {
        RecursiveBacktracking,  // 递归回溯
        Prims,                  // Prim算法
        Kruskals               // Kruskal算法
    }
    
    /// <summary>
    /// 迷宫难度
    /// </summary>
    public enum MazeDifficulty
    {
        Easy,    // 简单
        Medium,  // 中等
        Hard,    // 困难
        Expert   // 专家
    }
    
    /// <summary>
    /// 房间数据结构
    /// </summary>
    public class Room
    {
        public int x, y, width, height;
        
        public Room(int x, int y, int width, int height)
        {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
        }
        
        public Vector2Int Center => new Vector2Int(x + width / 2, y + height / 2);
        
        public bool Overlaps(Room other, int margin = 0)
        {
            return x < other.x + other.width + margin &&
                   x + width + margin > other.x &&
                   y < other.y + other.height + margin &&
                   y + height + margin > other.y;
        }
    }
    
    /// <summary>
    /// 边数据结构（用于Kruskal算法）
    /// </summary>
    public struct Edge
    {
        public Vector2Int from;
        public Vector2Int to;
        
        public Edge(Vector2Int from, Vector2Int to)
        {
            this.from = from;
            this.to = to;
        }
    }
    
    #endregion
}
