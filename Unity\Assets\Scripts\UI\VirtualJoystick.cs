using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

namespace MazeAdventure.UI
{
    /// <summary>
    /// 虚拟摇杆控制器 - 处理触屏移动输入
    /// </summary>
    public class VirtualJoystick : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>oint<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IDragHandler
    {
        [Header("摇杆组件")]
        [SerializeField] private RectTransform joystickBackground;
        [SerializeField] private RectTransform joystickHandle;
        [SerializeField] private Image backgroundImage;
        [SerializeField] private Image handleImage;
        
        [Header("摇杆设置")]
        [SerializeField] private float joystickRange = 50f;
        [SerializeField] private bool dynamicJoystick = true;
        [SerializeField] private bool snapToCenter = true;
        [SerializeField] private float returnSpeed = 10f;
        
        [Header("输入设置")]
        [SerializeField] private float deadZone = 0.1f;
        [SerializeField] private float sensitivity = 1f;
        [SerializeField] private bool invertY = false;
        
        [Header("视觉反馈")]
        [SerializeField] private float activeAlpha = 0.8f;
        [SerializeField] private float inactiveAlpha = 0.4f;
        [SerializeField] private float fadeSpeed = 5f;
        [SerializeField] private bool showOnlyWhenActive = true;
        
        // 输入状态
        public Vector2 InputVector { get; private set; }
        public bool IsActive { get; private set; }
        
        // 事件
        public System.Action<Vector2> OnInputChanged;
        public System.Action OnJoystickPressed;
        public System.Action OnJoystickReleased;
        
        // 私有变量
        private Vector2 joystickCenter;
        private Vector2 currentInput;
        private bool isDragging = false;
        private Canvas parentCanvas;
        private Camera uiCamera;
        
        // 动画相关
        private float targetAlpha;
        private Vector2 handleTargetPosition;
        
        #region Unity生命周期
        
        private void Awake()
        {
            InitializeJoystick();
        }
        
        private void Start()
        {
            SetupInitialState();
        }
        
        private void Update()
        {
            UpdateJoystick();
            UpdateVisuals();
        }
        
        #endregion
        
        #region 初始化
        
        private void InitializeJoystick()
        {
            // 获取Canvas组件
            parentCanvas = GetComponentInParent<Canvas>();
            if (parentCanvas != null)
            {
                uiCamera = parentCanvas.worldCamera;
            }
            
            // 验证必需组件
            if (joystickBackground == null)
                joystickBackground = transform as RectTransform;
            
            if (joystickHandle == null)
                joystickHandle = transform.GetChild(0) as RectTransform;
            
            if (backgroundImage == null)
                backgroundImage = joystickBackground.GetComponent<Image>();
            
            if (handleImage == null)
                handleImage = joystickHandle.GetComponent<Image>();
            
            // 设置初始位置
            joystickCenter = joystickBackground.anchoredPosition;
            handleTargetPosition = Vector2.zero;
        }
        
        private void SetupInitialState()
        {
            // 设置初始透明度
            if (showOnlyWhenActive)
            {
                targetAlpha = inactiveAlpha;
                SetAlpha(inactiveAlpha);
                
                if (dynamicJoystick)
                {
                    gameObject.SetActive(false);
                }
            }
            else
            {
                targetAlpha = inactiveAlpha;
                SetAlpha(inactiveAlpha);
            }
            
            // 重置摇杆位置
            joystickHandle.anchoredPosition = Vector2.zero;
            InputVector = Vector2.zero;
        }
        
        #endregion
        
        #region 触摸事件处理
        
        public void OnPointerDown(PointerEventData eventData)
        {
            Vector2 touchPosition = GetTouchPosition(eventData);
            
            if (dynamicJoystick)
            {
                // 动态摇杆：移动到触摸位置
                MoveTo(touchPosition);
                gameObject.SetActive(true);
            }
            
            // 开始拖拽
            isDragging = true;
            IsActive = true;
            
            // 视觉反馈
            targetAlpha = activeAlpha;
            
            // 触发事件
            OnJoystickPressed?.Invoke();
            
            // 震动反馈
            if (InputManager.Instance != null)
            {
                InputManager.Instance.Vibrate(0.05f);
            }
        }
        
        public void OnDrag(PointerEventData eventData)
        {
            if (!isDragging) return;
            
            Vector2 touchPosition = GetTouchPosition(eventData);
            Vector2 direction = touchPosition - joystickCenter;
            
            // 限制在摇杆范围内
            float distance = direction.magnitude;
            if (distance > joystickRange)
            {
                direction = direction.normalized * joystickRange;
                distance = joystickRange;
            }
            
            // 更新手柄位置
            handleTargetPosition = direction;
            
            // 计算输入值
            Vector2 normalizedInput = direction / joystickRange;
            
            // 应用死区
            if (normalizedInput.magnitude < deadZone)
            {
                normalizedInput = Vector2.zero;
            }
            else
            {
                // 重新映射到死区外的范围
                float magnitude = (normalizedInput.magnitude - deadZone) / (1f - deadZone);
                normalizedInput = normalizedInput.normalized * magnitude;
            }
            
            // 应用设置
            normalizedInput *= sensitivity;
            if (invertY)
            {
                normalizedInput.y = -normalizedInput.y;
            }
            
            // 更新输入
            SetInput(normalizedInput);
        }
        
        public void OnPointerUp(PointerEventData eventData)
        {
            // 停止拖拽
            isDragging = false;
            IsActive = false;
            
            // 重置输入
            SetInput(Vector2.zero);
            
            // 视觉反馈
            targetAlpha = showOnlyWhenActive ? 0f : inactiveAlpha;
            
            if (snapToCenter)
            {
                handleTargetPosition = Vector2.zero;
            }
            
            // 触发事件
            OnJoystickReleased?.Invoke();
            
            // 隐藏动态摇杆
            if (dynamicJoystick && showOnlyWhenActive)
            {
                StartCoroutine(HideAfterDelay(0.2f));
            }
        }
        
        #endregion
        
        #region 更新逻辑
        
        private void UpdateJoystick()
        {
            // 平滑移动手柄到目标位置
            if (joystickHandle.anchoredPosition != handleTargetPosition)
            {
                float speed = isDragging ? 20f : returnSpeed;
                joystickHandle.anchoredPosition = Vector2.Lerp(
                    joystickHandle.anchoredPosition,
                    handleTargetPosition,
                    speed * Time.unscaledDeltaTime
                );
            }
        }
        
        private void UpdateVisuals()
        {
            // 平滑透明度变化
            float currentAlpha = backgroundImage.color.a;
            if (Mathf.Abs(currentAlpha - targetAlpha) > 0.01f)
            {
                float newAlpha = Mathf.Lerp(currentAlpha, targetAlpha, fadeSpeed * Time.unscaledDeltaTime);
                SetAlpha(newAlpha);
            }
        }
        
        #endregion
        
        #region 辅助方法
        
        private Vector2 GetTouchPosition(PointerEventData eventData)
        {
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                parentCanvas.transform as RectTransform,
                eventData.position,
                uiCamera,
                out localPoint
            );
            return localPoint;
        }
        
        private void MoveTo(Vector2 position)
        {
            joystickBackground.anchoredPosition = position;
            joystickCenter = position;
        }
        
        private void SetInput(Vector2 input)
        {
            if (InputVector != input)
            {
                InputVector = input;
                currentInput = input;
                OnInputChanged?.Invoke(input);
                
                // 通知输入管理器
                if (InputManager.Instance != null)
                {
                    InputManager.Instance.SetMoveInput(input);
                }
            }
        }
        
        private void SetAlpha(float alpha)
        {
            if (backgroundImage != null)
            {
                Color bgColor = backgroundImage.color;
                bgColor.a = alpha;
                backgroundImage.color = bgColor;
            }
            
            if (handleImage != null)
            {
                Color handleColor = handleImage.color;
                handleColor.a = alpha;
                handleImage.color = handleColor;
            }
        }
        
        private System.Collections.IEnumerator HideAfterDelay(float delay)
        {
            yield return new WaitForSecondsRealtime(delay);
            if (!IsActive)
            {
                gameObject.SetActive(false);
            }
        }
        
        #endregion
        
        #region 公共方法
        
        /// <summary>
        /// 设置摇杆灵敏度
        /// </summary>
        public void SetSensitivity(float newSensitivity)
        {
            sensitivity = Mathf.Clamp(newSensitivity, 0.1f, 3f);
        }
        
        /// <summary>
        /// 设置死区大小
        /// </summary>
        public void SetDeadZone(float newDeadZone)
        {
            deadZone = Mathf.Clamp01(newDeadZone);
        }
        
        /// <summary>
        /// 启用/禁用动态摇杆
        /// </summary>
        public void SetDynamicMode(bool enabled)
        {
            dynamicJoystick = enabled;
            SetupInitialState();
        }
        
        /// <summary>
        /// 设置摇杆范围
        /// </summary>
        public void SetJoystickRange(float range)
        {
            joystickRange = Mathf.Max(10f, range);
        }
        
        /// <summary>
        /// 重置摇杆状态
        /// </summary>
        public void ResetJoystick()
        {
            isDragging = false;
            IsActive = false;
            SetInput(Vector2.zero);
            handleTargetPosition = Vector2.zero;
            SetupInitialState();
        }
        
        /// <summary>
        /// 获取输入角度（度）
        /// </summary>
        public float GetInputAngle()
        {
            if (InputVector.magnitude < 0.1f) return 0f;
            return Mathf.Atan2(InputVector.y, InputVector.x) * Mathf.Rad2Deg;
        }
        
        /// <summary>
        /// 获取输入强度
        /// </summary>
        public float GetInputMagnitude()
        {
            return InputVector.magnitude;
        }
        
        #endregion
        
        #region 调试
        
        private void OnDrawGizmosSelected()
        {
            if (joystickBackground != null)
            {
                // 绘制摇杆范围
                Gizmos.color = Color.yellow;
                Vector3 center = joystickBackground.position;
                Gizmos.DrawWireSphere(center, joystickRange);
                
                // 绘制死区
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(center, joystickRange * deadZone);
            }
        }
        
        #endregion
    }
}
